package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@ToString
@Data
@Table(name = "tj_actual_sales_temp")
//@Where(clause = " is_deleted=0 ")
//@SQLDelete(sql = "UPDATE tj_actual_sales_temp SET is_deleted = 1 WHERE id = ?")
//@SQLDeleteAll(sql = "UPDATE tj_actual_sales_temp SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TJActualSalesTemp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 排产行ID
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "ID")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    @ApiModelProperty(value = "区域")
    @Column(name = "region")
    private String region;

    @ApiModelProperty(value = "子区域")
    @Column(name = "sub_region")
    private String subRegion;

    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    @ApiModelProperty(value = "年")
    @Column(name = "year")
    private String year;

    @ApiModelProperty(value = "月")
    @Column(name = "month")
    private String month;

    @ApiModelProperty(value = "实际销售量")
    @Column(name = "quantity")
    private BigDecimal quantity;

    @ApiModelProperty(value = "国家")
    @Column(name = "country")
    private String country;

    @ApiModelProperty(value = "产品系列")
    @Transient
    private String productSeries;

    /**
     * 创建人
     */
    @Basic
    @CreatedBy
    @Column(name = "created_by", updatable = false)
    @ApiModelProperty(value = "创建人", notes = "")
    private String createdBy;
    /**
     * 创建时间
     */
    @Basic
    @CreatedDate
    @Column(name = "created_time", updatable = false)
    @ApiModelProperty(value = "创建时间", notes = "")
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    @Basic
    @LastModifiedBy
    @Column(name = "updated_by")
    @ApiModelProperty(value = "更新人", notes = "")
    private String updatedBy;
    /**
     * 更新时间
     */
    @Basic
    @LastModifiedDate
    @Column(name = "updated_time")
    @ApiModelProperty(value = "更新时间", notes = "")
    private LocalDateTime updatedTime;


    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Transient
    private String isOversea;

    /**
     * 组件类型
     */
    @ApiModelProperty(value = "组件类型")
    @Transient
    private String componentType;
}
