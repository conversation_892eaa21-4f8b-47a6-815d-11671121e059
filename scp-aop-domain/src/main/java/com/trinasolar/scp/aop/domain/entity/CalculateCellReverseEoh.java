package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池反算EOH表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:15:03
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_cell_reverse_eoh")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_cell_reverse_eoh SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_cell_reverse_eoh SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateCellReverseEoh extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池/兼容等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池/兼容等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 区域细分
     */
    @ApiModelProperty(value = "区域细分")
    @Column(name = "sub_area")
    private String subArea;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    @Column(name = "project_place")
    private String projectPlace;

    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 计算方式：电池可供量计算/组件可排产量计算
     */
    @ApiModelProperty(value = "计算方式：电池可供量计算/组件可排产量计算")
    @Column(name = "calculate_type")
    private String calculateType;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月EOH量
     */
    @ApiModelProperty(value = "1月EOH量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月EOH量
     */
    @ApiModelProperty(value = "2月EOH量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月EOH量
     */
    @ApiModelProperty(value = "3月EOH量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月EOH量
     */
    @ApiModelProperty(value = "4月EOH量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月EOH量
     */
    @ApiModelProperty(value = "5月EOH量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月EOH量
     */
    @ApiModelProperty(value = "6月EOH量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月EOH量
     */
    @ApiModelProperty(value = "7月EOH量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月EOH量
     */
    @ApiModelProperty(value = "8月EOH量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月EOH量
     */
    @ApiModelProperty(value = "9月EOH量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月EOH量
     */
    @ApiModelProperty(value = "10月EOH量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月EOH量
     */
    @ApiModelProperty(value = "11月EOH量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月EOH量
     */
    @ApiModelProperty(value = "12月EOH量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;
    /**
     * 原id
     */
    @ApiModelProperty(value = "原id")
    @Column(name = "old_id")
    private Long oldId;


}
