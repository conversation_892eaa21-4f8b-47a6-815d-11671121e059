package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.WorkshopPriority;
import com.trinasolar.scp.aop.domain.dto.WorkshopPriorityDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 车间优先级 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 16:12:08
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface WorkshopPriorityDEConvert extends BaseDEConvert<WorkshopPriorityDTO, WorkshopPriority> {

    WorkshopPriorityDEConvert INSTANCE = Mappers.getMapper(WorkshopPriorityDEConvert.class);

    List<WorkshopPriorityDTO> toDTO(List<WorkshopPriority> list);

    @Override
    List<WorkshopPriority> toEntity(List<WorkshopPriorityDTO> list);
}
