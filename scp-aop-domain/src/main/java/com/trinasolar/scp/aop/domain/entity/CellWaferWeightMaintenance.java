package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池硅片权重
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
@Entity
@ToString
@Data
@Table(name = "aop_cell_wafer_weight_maintenance")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_cell_wafer_weight_maintenance SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_cell_wafer_weight_maintenance SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CellWaferWeightMaintenance extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品大类(电池/硅片)
     */
    @ApiModelProperty(value = "产品大类(电池/硅片)")
    @Column(name = "product_type")
    private String productType;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;

    /**
     * 电池分片方式
     */
    @ApiModelProperty(value = "电池分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月权重
     */
    @ApiModelProperty(value = "1月权重")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月权重
     */
    @ApiModelProperty(value = "2月权重")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月权重
     */
    @ApiModelProperty(value = "3月权重")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月权重
     */
    @ApiModelProperty(value = "4月权重")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月权重
     */
    @ApiModelProperty(value = "5月权重")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月权重
     */
    @ApiModelProperty(value = "6月权重")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月权重
     */
    @ApiModelProperty(value = "7月权重")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月权重
     */
    @ApiModelProperty(value = "8月权重")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月权重
     */
    @ApiModelProperty(value = "9月权重")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月权重
     */
    @ApiModelProperty(value = "10月权重")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月权重
     */
    @ApiModelProperty(value = "11月权重")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月权重
     */
    @ApiModelProperty(value = "12月权重")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    @ApiModelProperty(value = "1月平均良率")
    @Column(name = "average_yield_rate_1")
    private BigDecimal averageYieldRate1;

    @ApiModelProperty(value = "2月平均良率")
    @Column(name = "average_yield_rate_2")
    private BigDecimal averageYieldRate2;

    @ApiModelProperty(value = "3月平均良率")
    @Column(name = "average_yield_rate_3")
    private BigDecimal averageYieldRate3;

    @ApiModelProperty(value = "4月平均良率")
    @Column(name = "average_yield_rate_4")
    private BigDecimal averageYieldRate4;

    @ApiModelProperty(value = "5月平均良率")
    @Column(name = "average_yield_rate_5")
    private BigDecimal averageYieldRate5;

    @ApiModelProperty(value = "6月平均良率")
    @Column(name = "average_yield_rate_6")
    private BigDecimal averageYieldRate6;

    @ApiModelProperty(value = "7月平均良率")
    @Column(name = "average_yield_rate_7")
    private BigDecimal averageYieldRate7;

    @ApiModelProperty(value = "8月平均良率")
    @Column(name = "average_yield_rate_8")
    private BigDecimal averageYieldRate8;

    @ApiModelProperty(value = "9月平均良率")
    @Column(name = "average_yield_rate_9")
    private BigDecimal averageYieldRate9;

    @ApiModelProperty(value = "10月平均良率")
    @Column(name = "average_yield_rate_10")
    private BigDecimal averageYieldRate10;

    @ApiModelProperty(value = "11月平均良率")
    @Column(name = "average_yield_rate_11")
    private BigDecimal averageYieldRate11;

    @ApiModelProperty(value = "12月平均良率")
    @Column(name = "average_yield_rate_12")
    private BigDecimal averageYieldRate12;

    @ApiModelProperty(value = "1月平均功率")
    @Transient
    private BigDecimal averagePower1;
    @ApiModelProperty(value = "2月平均功率")
    @Transient
    private BigDecimal averagePower2;
    @ApiModelProperty(value = "3月平均功率")
    @Transient
    private BigDecimal averagePower3;
    @ApiModelProperty(value = "4月平均功率")
    @Transient
    private BigDecimal averagePower4;
    @ApiModelProperty(value = "5月平均功率")
    @Transient
    private BigDecimal averagePower5;
    @ApiModelProperty(value = "6月平均功率")
    @Transient
    private BigDecimal averagePower6;
    @ApiModelProperty(value = "7月平均功率")
    @Transient
    private BigDecimal averagePower7;
    @ApiModelProperty(value = "8月平均功率")
    @Transient
    private BigDecimal averagePower8;
    @ApiModelProperty(value = "9月平均功率")
    @Transient
    private BigDecimal averagePower9;
    @ApiModelProperty(value = "10月平均功率")
    @Transient
    private BigDecimal averagePower10;
    @ApiModelProperty(value = "11月平均功率")
    @Transient
    private BigDecimal averagePower11;
    @ApiModelProperty(value = "12月平均功率")
    @Transient
    private BigDecimal averagePower12;

}
