package com.trinasolar.scp.aop.domain.constant;

import cn.hutool.core.collection.ListUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

@AllArgsConstructor
@Getter
public enum QuarterMonthEnum {

    Q1(1, ListUtil.of("1","2","3")),
    Q2(2, ListUtil.of("4","5","6")),
    Q3(3, ListUtil.of("7","8","9")),
    Q4(4, ListUtil.of("10","11","12"));

    private Integer quarter;
    private List<String> months;

    public static QuarterMonthEnum getByMonth(String month) {
        List<String> codes = Lists.newArrayList();
        for (QuarterMonthEnum quarterMonthEnum : values()) {
            if(quarterMonthEnum.months.contains(month)){
                return quarterMonthEnum;
            }
        }
        return null;
    }

}
