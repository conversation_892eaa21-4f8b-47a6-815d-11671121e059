package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.base.OriginalCapacityIeExtDTO;
import com.trinasolar.scp.aop.domain.entity.OriginCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @className OriginalCapacityIeConvert
 * @description 转换
 * @date 2022-06-10
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginalCapacityIeDEConvert extends BaseDEConvert<OriginalCapacityIeExtDTO, OriginCapacityIe> {

    OriginalCapacityIeDEConvert INSTANCE = Mappers.getMapper(OriginalCapacityIeDEConvert.class);

    List<OriginalCapacityIeExtDTO> toDemandLinesSaveDTOList(List<OriginCapacityIe> orderLines);

}