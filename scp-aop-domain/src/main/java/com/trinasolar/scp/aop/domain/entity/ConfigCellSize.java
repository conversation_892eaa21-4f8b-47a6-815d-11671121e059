package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池尺寸行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:01
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_cell_size")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_cell_size SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_cell_size SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigCellSize extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @Column(name = "product_type")
    private String productType;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    @Column(name = "product_category")
    private String productCategory;

    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @Column(name = "area")
    private BigDecimal area;

    /**
     * 面积单位
     */
    @ApiModelProperty(value = "面积单位")
    @Column(name = "area_unit")
    private BigDecimal areaUnit;

    /**
     * 长度
     */
    @ApiModelProperty(value = "长度")
    @Column(name = "cell_long")
    private BigDecimal cellLong;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    @Column(name = "cell_width")
    private BigDecimal cellWidth;

    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    @Column(name = "cell_high")
    private BigDecimal cellHigh;

    /**
     * 长宽高单位
     */
    @ApiModelProperty(value = "长宽高单位")
    @Column(name = "unit")
    private String unit;


}
