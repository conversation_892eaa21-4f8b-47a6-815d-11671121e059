package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.HaEoh;
import com.trinasolar.scp.aop.domain.dto.HaEohDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * ha的EOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-31 20:07:37
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HaEohDEConvert extends BaseDEConvert<HaEohDTO, HaEoh> {

    HaEohDEConvert INSTANCE = Mappers.getMapper(HaEohDEConvert.class);

}
