package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Entity
@ToString
@Data
@Builder
@Table(name = "aop_product_group_percent")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_product_group_percent SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_product_group_percent SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class ProductGroupPercent extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "调整版本号")
    @Column(name = "calculate_version")
    private String calculateVersion;

    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    @ApiModelProperty(value = "Q1比例")
    @Column(name = "q1_percent")
    private BigDecimal q1Percent;

    @ApiModelProperty(value = "Q2比例")
    @Column(name = "q2_percent")
    private BigDecimal q2Percent;

    @ApiModelProperty(value = "Q3比例")
    @Column(name = "q3_percent")
    private BigDecimal q3Percent;

    @ApiModelProperty(value = "Q4比例")
    @Column(name = "q4_percent")
    private BigDecimal q4Percent;
}
