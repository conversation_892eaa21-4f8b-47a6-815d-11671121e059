package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigProductionDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigProduction;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 投产效率表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigProductionDEConvert extends BaseDEConvert<ConfigProductionDTO, ConfigProduction> {

    ConfigProductionDEConvert INSTANCE = Mappers.getMapper(ConfigProductionDEConvert.class);

}
