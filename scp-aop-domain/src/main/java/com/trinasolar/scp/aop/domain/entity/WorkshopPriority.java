package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 车间优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-22 16:12:08
 */
@Entity
@ToString
@Data
@Table(name = "aop_workshop_priority")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_workshop_priority SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_workshop_priority SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class WorkshopPriority extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;

    /**
     * 电池分片方式
     */
    @ApiModelProperty(value = "电池分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    /**
     * W/W美学
     */
    @ApiModelProperty(value = "W/W美学")
    @Column(name = "w_aesthetics")
    private String waesthetics;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 车间优先级
     */
    @ApiModelProperty(value = "车间优先级")
    @Column(name = "priority")
    private Integer priority;


    /**
     * 分片方式优先级
     */
    @ApiModelProperty(value = "分片方式优先级")
    @Column(name = "shard_priority")
    private Integer shardPriority;
    /**
     * 优先级类型
     */
    @ApiModelProperty(value = "优先级类型")
    @Column(name = "priority_type")
    private String priorityType;
}
