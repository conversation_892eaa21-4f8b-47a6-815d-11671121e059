package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductGroupPercentDTO;
import com.trinasolar.scp.aop.domain.entity.ProductGroupPercent;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR>
 * @Date 2024/5/8
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductGroupPercentDEConvert extends BaseDEConvert<ProductGroupPercentDTO, ProductGroupPercent> {
}
