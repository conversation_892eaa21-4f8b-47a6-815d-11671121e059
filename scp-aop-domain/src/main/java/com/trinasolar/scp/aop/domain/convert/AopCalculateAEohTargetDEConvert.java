package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ACellSupplySummaryInnerDTO;
import com.trinasolar.scp.aop.domain.entity.AopCalculateAEohTarget;
import com.trinasolar.scp.aop.domain.entity.AopCalculateASupply;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AopCalculateAEohTargetDEConvert extends BaseDEConvert<ACellSupplySummaryInnerDTO, AopCalculateAEohTarget> {
    AopCalculateAEohTargetDEConvert INSTANCE = Mappers.getMapper(AopCalculateAEohTargetDEConvert.class);

    AopCalculateAEohTarget convertEohTarget(AopCalculateASupply bean);

    List<AopCalculateAEohTarget> convertEohTarget(List<AopCalculateASupply> list);
}
