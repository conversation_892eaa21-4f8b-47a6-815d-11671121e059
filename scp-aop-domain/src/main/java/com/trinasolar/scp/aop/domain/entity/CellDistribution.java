package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池分配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-30 14:32:03
 */
@Entity
@ToString
@Data
@Table(name = "aop_cell_distribution")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_cell_distribution SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_cell_distribution SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CellDistribution extends BasePO implements Serializable, Comparable<CellDistribution> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 计算版本号
     */
    @ApiModelProperty(value = "计算版本号")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * W/W美学
     */
    @ApiModelProperty(value = "W/W美学")
    @Column(name = "w_aesthetics")
    private String waesthetics;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 电池分片方式
     */
    @ApiModelProperty(value = "电池分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 一季度
     */
    @ApiModelProperty(value = "一季度")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 二季度
     */
    @ApiModelProperty(value = "二季度")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 三季度
     */
    @ApiModelProperty(value = "三季度")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 四季度
     */
    @ApiModelProperty(value = "四季度")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @Column(name = "total_quantity")
    private BigDecimal totalQuantity;

    /**
     * 电池型号
     */
    @Transient
    private String cellModel;
    @Transient
    private Integer priority;

    public void sum() {
        this.setQ1Quantity(this.getM1Quantity().add(this.getM2Quantity()).add(this.getM3Quantity()));
        this.setQ2Quantity(this.getM4Quantity().add(this.getM5Quantity()).add(this.getM6Quantity()));
        this.setQ3Quantity(this.getM7Quantity().add(this.getM8Quantity()).add(this.getM9Quantity()));
        this.setQ4Quantity(this.getM10Quantity().add(this.getM11Quantity()).add(this.getM12Quantity()));
        this.setTotalQuantity(this.getQ1Quantity().add(this.getQ2Quantity()).add(this.getQ3Quantity()).add(this.getQ4Quantity()));
    }

    @Override
    public int compareTo(CellDistribution arg) {
        if (ObjectUtils.isEmpty(arg.getPriority()) || ObjectUtils.isEmpty(this.getPriority())) {
            return 0;
        }
        return this.getPriority().compareTo(arg.getPriority());
    }

    public String sign(){
        return StringUtils.join(this.calculateVersion, this.countryFlag, this.productSeries, this.waesthetics, this.year, this.cellShard, this.basePlace, this.workshop);
    }
}
