package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductSeriesPriorityDTO;
import com.trinasolar.scp.aop.domain.entity.ProductSeriesPriority;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 产品系列优先级 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-26 10:33:09
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductSeriesPriorityDEConvert extends BaseDEConvert<ProductSeriesPriorityDTO, ProductSeriesPriority> {

    ProductSeriesPriorityDEConvert INSTANCE = Mappers.getMapper(ProductSeriesPriorityDEConvert.class);

    List<ProductSeriesPriorityDTO> toDTO(List<ProductSeriesPriority> productSeriesPriorityList);
}
