package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.PreSaleDTO;
import com.trinasolar.scp.aop.domain.entity.OriginSale;
import com.trinasolar.scp.aop.domain.entity.PreSale;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/13 15:10
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginSaleDEConvert extends BaseDEConvert<PreSaleDTO, OriginSale> {

    OriginSaleDEConvert INSTANCE = Mappers.getMapper(OriginSaleDEConvert.class);

    /**
     * List<OriginSale> 转为 List<PreSaleDTO>
     *
     * @param originSales
     * @return
     */
    List<PreSaleDTO> toPreSaleDTOList(List<OriginSale> originSales);

    /**
     * List<OriginSale> 转为 List<PreSale>
     *
     * @param originSales 原始销售目标集合
     * @return 预处理后销售目标集合
     */
    List<PreSale> toPreSaleList(List<OriginSale> originSales);
}
