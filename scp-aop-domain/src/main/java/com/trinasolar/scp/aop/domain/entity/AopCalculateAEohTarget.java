/**
 * <AUTHOR>
 * @date 2024-11-01 9:47
 */
package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2024/11/1 9:47
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_a_eoh_target")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_a_eoh_target SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_a_eoh_target SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AopCalculateAEohTarget extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "country_flag")
    private String countryFlag;


    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 电池分片方式
     */
    @ApiModelProperty(value = "电池分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    /**
     * 1月产能
     */
    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月产能
     */
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月产能
     */
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月产能
     */
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月产能
     */
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月产能
     */
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月产能
     */
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月产能
     */
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月产能
     */
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月产能
     */
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月产能
     */
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月产能
     */
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 一季度
     */
    @ApiModelProperty(value = "一季度")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 二季度
     */
    @ApiModelProperty(value = "二季度")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 三季度
     */
    @ApiModelProperty(value = "三季度")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 四季度
     */
    @ApiModelProperty(value = "四季度")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;
}
