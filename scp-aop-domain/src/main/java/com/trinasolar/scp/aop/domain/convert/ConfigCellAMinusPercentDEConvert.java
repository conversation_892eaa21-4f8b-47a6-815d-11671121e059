package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigCellAMinusPercentDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigCellAMinusPercent;
import com.trinasolar.scp.aop.domain.excel.ConfigCellAMinusPercentIEExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ConfigCellAMinusPercentImportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ConfigCellAMinusPercentPurchaseExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ConfigCellSupplyPercentPurchaseExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigCellAMinusPercentDEConvert extends BaseDEConvert<ConfigCellAMinusPercentDTO, ConfigCellAMinusPercent> {

    ConfigCellAMinusPercentDEConvert INSTANCE = Mappers.getMapper(ConfigCellAMinusPercentDEConvert.class);

    List<ConfigCellAMinusPercentDTO> excelToDTO(List<ConfigCellAMinusPercentImportExcelDTO> excelDTOS);

    List<ConfigCellAMinusPercentIEExportExcelDTO> toIEExcel(List<ConfigCellAMinusPercentDTO> dtoList);

    List<ConfigCellAMinusPercentPurchaseExportExcelDTO> toPurchaseExcel(List<ConfigCellAMinusPercentDTO> dtoList);

    List<ConfigCellSupplyPercentPurchaseExportExcelDTO> toSupplyPurchaseExcel(List<ConfigCellAMinusPercentDTO> dtoList);
}
