package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigUsageDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigUsage;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/7/12 16:12
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigUsageDEConvert extends BaseDEConvert<ConfigUsageDTO, ConfigUsage> {

    ConfigUsageDEConvert INSTANCE = Mappers.getMapper(ConfigUsageDEConvert.class);

}
