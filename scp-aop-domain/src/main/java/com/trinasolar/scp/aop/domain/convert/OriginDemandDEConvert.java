package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.OriginDemandDTO;
import com.trinasolar.scp.aop.domain.entity.OriginDemand;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17 17:51
 * @apiNote
 */
public interface OriginDemandDEConvert extends BaseDEConvert<OriginDemand, OriginDemandDTO> {
    OriginDemandDEConvert INSTANCE = Mappers.getMapper(OriginDemandDEConvert.class);

    /**
     * List<OriginDemandDTO> 转为 List<OriginDemand>
     *
     * @param originDemandDTOs
     * @return
     */
    List<OriginDemand> toOriginDemands(List<OriginDemandDTO> originDemandDTOs);

    /**
     * List<OriginDemand> 转为 List<OriginDemandDTO>
     *
     * @param originDemands
     * @return
     */
    List<OriginDemandDTO> toOriginDemandDTOs(List<OriginDemand> originDemands);
}
