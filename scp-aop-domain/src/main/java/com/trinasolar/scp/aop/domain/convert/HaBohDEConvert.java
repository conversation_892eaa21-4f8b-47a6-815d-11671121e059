package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.HaBoh;
import com.trinasolar.scp.aop.domain.dto.HaBohDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * HBOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-26 11:24:44
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HaBohDEConvert extends BaseDEConvert<HaBohDTO, HaBoh> {

    HaBohDEConvert INSTANCE = Mappers.getMapper(HaBohDEConvert.class);

}
