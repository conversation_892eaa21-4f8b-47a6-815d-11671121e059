package com.trinasolar.scp.aop.domain.constant;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 天玑常量
 */
public interface TJConstant {

    @AllArgsConstructor
    @Getter
    enum ProductType {

        @ApiModelProperty("电池")
        CELL("CELL", "C"),
        @ApiModelProperty("硅片")
        WAFER("WAFER", "S"),
        @ApiModelProperty("组件")
        MODULE("MODULE", "M"),
        @ApiModelProperty("PREBILLING")
        PREBILLING("PREBILLING", "PREBILLING"),
        @ApiModelProperty("CLOSED")
        CLOSED("CLOSED", "CLOSED");

        private String code;
        private String tjCode;

        public static ProductType match(String code) {
            return Arrays.stream(values()).filter((e) -> {
                return e.getTjCode().equals(code) || e.getCode().equals(code);
            }).findFirst().orElse(null);
        }
    }

    @AllArgsConstructor
    @Getter
    enum Type {

        @ApiModelProperty("自产")
        MAKE("IE", "MAKE"),
        @ApiModelProperty("硅片")
        BUY("purchase", "BUY");

        private String code;
        private String tjCode;

        public static Type match(String code) {
            return Arrays.stream(values()).filter((e) -> {
                return e.getTjCode().equals(code) || e.getCode().equals(code);
            }).findFirst().orElse(null);
        }
    }

    @AllArgsConstructor
    @Getter
    enum Channel {

        @ApiModelProperty("直销")
        MAKE("Utility", "Utility"),
        @ApiModelProperty("分销")
        BUY("Non-Utility", "Distribution");

        private String code;
        private String tjCode;

        public static Channel match(String code) {
            return Arrays.stream(values()).filter((e) -> {
                return e.getTjCode().equals(code) || e.getCode().equals(code);
            }).findFirst().orElse(null);
        }

        public static String getByTjCode(String tjCode) {
            if (StringUtils.isBlank(tjCode)) {
                return null;
            }
            for (Channel value : values()) {
                if (value.getTjCode().equals(tjCode)) {
                    return value.getCode();
                }
            }
            return null;
        }
    }

    @AllArgsConstructor
    @Getter
    enum Flag {

        @ApiModelProperty("prebling")
        PRE("prebling", "PRE");

        private String code;
        private String tjCode;

        public static Flag match(String code) {
            return Arrays.stream(values()).filter((e) -> {
                return e.getTjCode().equals(code) || e.getCode().equals(code);
            }).findFirst().orElse(null);
        }
    }

    @AllArgsConstructor
    @Getter
    enum Area {

        @ApiModelProperty("OTHS")
        OTHS("OTHS", "OTHS"),

        @ApiModelProperty("OTHE")
        OTHE("OTHE", "OTHE");

        private String code;
        private String tjCode;

        public static Area match(String code) {
            return Arrays.stream(values()).filter((e) -> {
                return e.getTjCode().equals(code) || e.getCode().equals(code);
            }).findFirst().orElse(null);
        }
    }

    String OVERSEA_LOV = "AOP_Product_oversea";

}
