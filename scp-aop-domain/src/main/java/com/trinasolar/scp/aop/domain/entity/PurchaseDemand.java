package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 通合电池、外购、OEM基础表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-08 14:58:25
 */
@Entity
@ToString
@Data
@Table(name = "aop_purchase_demand")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_purchase_demand SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_purchase_demand SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false,of = {"countryFlag","fromType","productFrom","year","productSeries"})
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseDemand extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 最新标识
     */
    @ApiModelProperty(value = "最新标识")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 来源类型
     */
    @ApiModelProperty(value = "来源类型")
    @Column(name = "from_type")
    private String fromType;

    /**
     * 产品大类
     */
    @ApiModelProperty(value = "产品大类")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品来源(TH、PURCHASE、OEM)
     */
    @ApiModelProperty(value = "产品来源(TH、PURCHASE、OEM)")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月需求量
     */
    @ApiModelProperty(value = "1月需求量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月需求量
     */
    @ApiModelProperty(value = "2月需求量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月需求量
     */
    @ApiModelProperty(value = "3月需求量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月需求量
     */
    @ApiModelProperty(value = "4月需求量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月需求量
     */
    @ApiModelProperty(value = "5月需求量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月需求量
     */
    @ApiModelProperty(value = "6月需求量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月需求量
     */
    @ApiModelProperty(value = "7月需求量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月需求量
     */
    @ApiModelProperty(value = "8月需求量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月需求量
     */
    @ApiModelProperty(value = "9月需求量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月需求量
     */
    @ApiModelProperty(value = "10月需求量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月需求量
     */
    @ApiModelProperty(value = "11月需求量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月需求量
     */
    @ApiModelProperty(value = "12月需求量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    public List<String> fillLovValue(Map<String, LovLineDTO> lovMap, Integer count) {
        List<String> stringList = new ArrayList<>();
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());
        LovLineDTO productFromLov = lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_FROM + getProductFrom());
        //国内/海外
        if (ObjectUtils.isEmpty(countryFlagLov)) {
            String format = String.format("第{%s}行，国内/海外的值集不存在-->{%s}", count, getCountryFlag());
            stringList.add(format);
        }
        if (ProductTypeEnum.CELL.getCode().equals(this.getProductType())) {
            if (ObjectUtils.isEmpty(productFromLov)) {
                String format = String.format("第{%s}行，产品来源的值集不存在-->{%s}", count, getProductFrom());
                stringList.add(format);
            }
        }
        setCountryFlag(countryFlagLov.getLovValue());
        return stringList;
    }

    public String selectQuery(PurchaseDemand dto) {
        //组件OEM 导入
        if (ProductTypeEnum.MODULE.getCode().equals(dto.getProductType())) {
            if (StringUtils.isBlank(dto.getCountryFlag()) || StringUtils.isBlank(dto.getProductSeries()) || dto.getYear() == null) {
                return "数据异常";
            }
        } else {
            //电池/硅片 导入
            if (StringUtils.isBlank(dto.getProductSeries()) || StringUtils.isBlank(dto.getFromType()) || StringUtils.isBlank(dto.getCountryFlag()) || dto.getYear() == null) {
                return "数据异常";
            }
        }

        setCountryFlag(dto.getCountryFlag());
        //setDataVersion(dto.getDataVersion());
        setLatestFlag("Y");
        //setVersionType();
        setFromType(dto.getFromType());
        setProductSeries(dto.getProductSeries());
        setYear(dto.getYear());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());
        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());
        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());
        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());
        return null;
    }

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }

    public void setNullZero() {
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }
}
