package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 计算后IE产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:22:06
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_capacity_ie")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_capacity_ie SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_capacity_ie SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculateCapacityIe extends BasePO implements Serializable, Comparable<CalculateCapacityIe> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 业务场景：RMA/OTHER
     */
    @ApiModelProperty(value = "业务场景：RMA/OTHER")
    @Column(name = "business_type")
    private String businessType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品类型：电池/组件自产/硅片/组件兼容
     */
    @ApiModelProperty(value = "产品类型：电池/组件自产/硅片/组件兼容")
    @Column(name = "product_type")
    private String productType;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 总线数
     */
    @ApiModelProperty(value = "总线数")
    @Column(name = "total_line")
    private Integer totalLine;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月产能
     */
    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月产能
     */
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月产能
     */
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月产能
     */
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月产能
     */
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月产能
     */
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月产能
     */
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月产能
     */
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月产能
     */
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月产能
     */
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月产能
     */
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月产能
     */
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;


    /**
     * 数据类型：满产/公布
     */
    @ApiModelProperty(value = "数据类型：满产/公布")
    @Column(name = "data_type")
    private String dataType;


    @ApiModelProperty("基地")
    @Column(name = "base_place")
    private String basePlace;
    @Transient
    private String area;
    @Transient
    private Integer actualMonth;
    @Transient
    private Integer planMonth;
    @Transient
    private String cellModel;
    @Transient
    private String waferModel;
    @Transient
    private String priorityLevel;

    @Transient
    private Integer priority;

    public void round() {
        if (ObjectUtils.isNotEmpty(this.getM1Quantity())) {
            this.setM1Quantity(this.getM1Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM2Quantity())) {
            this.setM2Quantity(this.getM2Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM3Quantity())) {
            this.setM3Quantity(this.getM3Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM4Quantity())) {
            this.setM4Quantity(this.getM4Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM5Quantity())) {
            this.setM5Quantity(this.getM5Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM6Quantity())) {
            this.setM6Quantity(this.getM6Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM7Quantity())) {
            this.setM7Quantity(this.getM7Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM8Quantity())) {
            this.setM8Quantity(this.getM8Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM9Quantity())) {
            this.setM9Quantity(this.getM9Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM10Quantity())) {
            this.setM10Quantity(this.getM10Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM11Quantity())) {
            this.setM11Quantity(this.getM11Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM12Quantity())) {
            this.setM12Quantity(this.getM12Quantity().setScale(6, RoundingMode.HALF_UP));
        }
    }

    public void roundOne() {
        if (ObjectUtils.isNotEmpty(this.getM1Quantity())) {
            this.setM1Quantity(this.getM1Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM2Quantity())) {
            this.setM2Quantity(this.getM2Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM3Quantity())) {
            this.setM3Quantity(this.getM3Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM4Quantity())) {
            this.setM4Quantity(this.getM4Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM5Quantity())) {
            this.setM5Quantity(this.getM5Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM6Quantity())) {
            this.setM6Quantity(this.getM6Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM7Quantity())) {
            this.setM7Quantity(this.getM7Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM8Quantity())) {
            this.setM8Quantity(this.getM8Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM9Quantity())) {
            this.setM9Quantity(this.getM9Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM10Quantity())) {
            this.setM10Quantity(this.getM10Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM11Quantity())) {
            this.setM11Quantity(this.getM11Quantity().setScale(1, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM12Quantity())) {
            this.setM12Quantity(this.getM12Quantity().setScale(1, RoundingMode.HALF_UP));
        }
    }

    @Override
    public int compareTo(CalculateCapacityIe calculateCapacityIe) {
        if (ObjectUtils.isNotEmpty(this.getPriority()) && ObjectUtils.isNotEmpty(calculateCapacityIe.getPriority())) {
            return this.getPriority().compareTo(calculateCapacityIe.getPriority());
        } else if (ObjectUtils.isNotEmpty(this.getPriorityLevel()) && ObjectUtils.isNotEmpty(calculateCapacityIe.getPriorityLevel())) {
            return this.getPriorityLevel().compareTo(calculateCapacityIe.getPriorityLevel());
        } else {
            return this.getProductSeries().compareTo(calculateCapacityIe.getProductSeries());
        }
    }

    public void perfect() {
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }
}
