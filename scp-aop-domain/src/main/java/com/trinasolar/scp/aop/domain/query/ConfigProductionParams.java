package com.trinasolar.scp.aop.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;


/**
 * 投产效率行表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ConfigProductionParams对象", description = "DTO对象")
public class ConfigProductionParams {

    /**
     * 国内/海外
     */
    @NotBlank(message = "国内/海外 不可为空！")
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 年份
     */
    @NotBlank(message = "年份 不可为空！")
    @ApiModelProperty(value = "年份")
    private String year;
    /**
     * 产品来源：自产/外购
     */
    @NotBlank(message = "产品来源 不可为空！")
    @ApiModelProperty(value = "产品来源：自产/外购")
    private String productFrom;
    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    private String productSeries;
    /**
     * 产品族
     */
    @NotBlank(message = "产品族 不可为空！")
    @ApiModelProperty(value = "产品族")
    private String productFamily;
    /**
     * 材料搭配1
     */
//    @NotBlank(message = "材料搭配1 不可为空！")
    @ApiModelProperty(value = "材料搭配1")
    private String material1;
    /**
     * 材料搭配2
     */
//    @NotBlank(message = "材料搭配2 不可为空！")
    @ApiModelProperty(value = "材料搭配2")
    private String material2;
    /**
     * 材料搭配3
     */
//    @NotBlank(message = "材料搭配3 不可为空！")
    @ApiModelProperty(value = "材料搭配3")
    private String material3;
    /**
     * 材料搭配4
     */
//    @NotBlank(message = "材料搭配4 不可为空！")
    @ApiModelProperty(value = "材料搭配4")
    private String material4;
    /**
     * 材料搭配5
     */
    @ApiModelProperty(value = "材料搭配5")
    private String material5;
    /**
     * 材料搭配6
     */
    @ApiModelProperty(value = "材料搭配6")
    private String material6;
    /**
     * 材料搭配7
     */
    @ApiModelProperty(value = "材料搭配7")
    private String material7;
    /**
     * 材料搭配8
     */
    @ApiModelProperty(value = "材料搭配8")
    private String material8;
    /**
     * 材料搭配9
     */
    @ApiModelProperty(value = "材料搭配9")
    private String material9;
    /**
     * 材料搭配10
     */
    @ApiModelProperty(value = "材料搭配10")
    private String material10;
    /**
     * 安装类型：竖装，横装
     */
    @NotBlank(message = "安装类型 不可为空！")
    @ApiModelProperty(value = "安装类型")
    private String installType;
}
