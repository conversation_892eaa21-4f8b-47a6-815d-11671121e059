package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 制造参数管理-满产产能/排产产能
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 09:11:41
 */
@Entity
@ToString
@Data
@Table(name = "aop_year_capacity")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_year_capacity SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_year_capacity SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigYearCapacity extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 满产产能/排产产能
     */
    @ApiModelProperty(value = "满产产能/排产产能")
    @Column(name = "capacity_type")
    private String capacityType;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    @Column(name = "quantity_m1")
    private BigDecimal quantityM1;

    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    @Column(name = "quantity_m2")
    private BigDecimal quantityM2;

    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    @Column(name = "quantity_m3")
    private BigDecimal quantityM3;

    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    @Column(name = "quantity_m4")
    private BigDecimal quantityM4;

    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    @Column(name = "quantity_m5")
    private BigDecimal quantityM5;

    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    @Column(name = "quantity_m6")
    private BigDecimal quantityM6;

    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    @Column(name = "quantity_m7")
    private BigDecimal quantityM7;

    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    @Column(name = "quantity_m8")
    private BigDecimal quantityM8;

    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    @Column(name = "quantity_m9")
    private BigDecimal quantityM9;

    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    @Column(name = "quantity_m10")
    private BigDecimal quantityM10;

    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    @Column(name = "quantity_m11")
    private BigDecimal quantityM11;

    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    @Column(name = "quantity_m12")
    private BigDecimal quantityM12;


}
