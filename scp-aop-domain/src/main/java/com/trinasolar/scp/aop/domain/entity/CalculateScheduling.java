package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 计算排产量表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:14:43
 */
@Entity
@ToString
@Data
@Builder
@Table(name = "aop_calculate_scheduling")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_scheduling SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_scheduling SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateScheduling extends BasePO implements Serializable, Comparable<CalculateScheduling> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池/兼容等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池/兼容等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 区域细分
     */
    @ApiModelProperty(value = "区域细分")
    @Column(name = "sub_area")
    private String subArea;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    @Column(name = "project_place")
    private String projectPlace;

    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 计算方式：正算/反算
     */
    @ApiModelProperty(value = "计算方式：正算/反算")
    @Column(name = "calculate_type")
    private String calculateType;

    /**
     * 数据类型：理论/实际
     */
    @ApiModelProperty(value = "数据类型：理论/实际")
    @Column(name = "data_type")
    private String dataType;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月排产量
     */
    @ApiModelProperty(value = "1月排产量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月排产量
     */
    @ApiModelProperty(value = "2月排产量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月排产量
     */
    @ApiModelProperty(value = "3月排产量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月排产量
     */
    @ApiModelProperty(value = "4月排产量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月排产量
     */
    @ApiModelProperty(value = "5月排产量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月排产量
     */
    @ApiModelProperty(value = "6月排产量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月排产量
     */
    @ApiModelProperty(value = "7月排产量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月排产量
     */
    @ApiModelProperty(value = "8月排产量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月排产量
     */
    @ApiModelProperty(value = "9月排产量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月排产量
     */
    @ApiModelProperty(value = "10月排产量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月排产量
     */
    @ApiModelProperty(value = "11月排产量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月排产量
     */
    @ApiModelProperty(value = "12月排产量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 原ID，调整用
     */
    @ApiModelProperty(value = "原ID，调整用")
    @Column(name = "old_id")
    private Long oldId;

    @ApiModelProperty(value = "实际月份")
    @Column(name = "actual_month")
    private Integer actualMonth;

    @ApiModelProperty(value = "排产月份")
    @Column(name = "plan_month")
    private Integer planMonth;

    @Transient
    private String cellModel;

    @Transient
    private Integer priorityLevel;

    @Transient
    private String mainProductGroup;

    @Transient
    private String componentType;

    public void zeroSetQuantity() {

        setM1Quantity(BigDecimal.ZERO);
        setM2Quantity(BigDecimal.ZERO);
        setM3Quantity(BigDecimal.ZERO);
        setM4Quantity(BigDecimal.ZERO);
        setM5Quantity(BigDecimal.ZERO);
        setM6Quantity(BigDecimal.ZERO);
        setM7Quantity(BigDecimal.ZERO);
        setM8Quantity(BigDecimal.ZERO);
        setM9Quantity(BigDecimal.ZERO);
        setM10Quantity(BigDecimal.ZERO);
        setM11Quantity(BigDecimal.ZERO);
        setM12Quantity(BigDecimal.ZERO);
    }

    public void round() {
        if (ObjectUtils.isNotEmpty(this.getM1Quantity())) {
            this.setM1Quantity(this.getM1Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM2Quantity())) {
            this.setM2Quantity(this.getM2Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM3Quantity())) {
            this.setM3Quantity(this.getM3Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM4Quantity())) {
            this.setM4Quantity(this.getM4Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM5Quantity())) {
            this.setM5Quantity(this.getM5Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM6Quantity())) {
            this.setM6Quantity(this.getM6Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM7Quantity())) {
            this.setM7Quantity(this.getM7Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM8Quantity())) {
            this.setM8Quantity(this.getM8Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM9Quantity())) {
            this.setM9Quantity(this.getM9Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM10Quantity())) {
            this.setM10Quantity(this.getM10Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM11Quantity())) {
            this.setM11Quantity(this.getM11Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM12Quantity())) {
            this.setM12Quantity(this.getM12Quantity().setScale(6, RoundingMode.HALF_UP));
        }
    }

    @Override
    public int compareTo(CalculateScheduling calculateScheduling) {
        return this.getPriorityLevel().compareTo(calculateScheduling.getPriorityLevel());
    }

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }
}
