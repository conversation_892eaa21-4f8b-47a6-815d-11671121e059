package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductBomDTO;
import com.trinasolar.scp.aop.domain.entity.ProductBom;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 新产品BOM 
 *
 * <AUTHOR>
 * @date 2022-8-15
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductBomDEConvert extends BaseDEConvert<ProductBomDTO, ProductBom> {
}
