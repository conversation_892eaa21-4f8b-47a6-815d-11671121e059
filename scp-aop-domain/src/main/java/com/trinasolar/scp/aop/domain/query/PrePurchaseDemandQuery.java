package com.trinasolar.scp.aop.domain.query;

import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 预处理外购目标/反馈/指定
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 14:51:23
 */
@Data
@ApiModel(value = "PrePurchaseDemand查询条件", description = "查询条件")
@Accessors(chain = true)
public class PrePurchaseDemandQuery extends PageDTO implements Serializable {

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    private ProductFromEnum productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    private String productSeries;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private Integer year;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    private String dataVersion;

    /**
     * 产品类型：电池/组件/硅片/物料/硅料/
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片/物料/硅料/")
    private ProductTypeEnum productType;

    /**
     * 导出字段属性
     */
    private ExcelPara excelPara;
}
