package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateCellMinusDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateCellMinus;
import com.trinasolar.scp.aop.domain.excel.CalculateIEAMinusCellSupplyExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.CalculatePurchaseAMinusCellSupplyExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateCellMinusDEConvert extends BaseDEConvert<CalculateCellMinusDTO, CalculateCellMinus> {

    CalculateCellMinusDEConvert INSTANCE = Mappers.getMapper(CalculateCellMinusDEConvert.class);

    List<CalculateIEAMinusCellSupplyExportExcelDTO> toIESupplyExportExcelDTO(List<CalculateCellMinusDTO> dtoList);

    List<CalculatePurchaseAMinusCellSupplyExportExcelDTO> toPurchaseSupplyExportExcelDTO(List<CalculateCellMinusDTO> dtoList);
}
