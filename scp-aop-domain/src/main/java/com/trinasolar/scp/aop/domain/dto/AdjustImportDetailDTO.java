package com.trinasolar.scp.aop.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023/12/13
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "AdjustImportDetailDTO", description = "DTO对象")
public class AdjustImportDetailDTO implements Serializable {

    private static final long serialVersionUID = -2109042337068807052L;

    @ApiModelProperty(value = "产地")
    private String countryFlag;

    @ApiModelProperty(value = "产品类型")
    private String productType;

    @ApiModelProperty(value = "供应类型")
    private String productFrom;

    @ApiModelProperty(value = "车间")
    private String workshop;

    @ApiModelProperty(value = "生产基地")
    private String basePlace;

    @ApiModelProperty(value = "产品型号")
    private String productSeries;

    @ApiModelProperty(value = "Jan")
    private BigDecimal m1Quantity;

    @ApiModelProperty(value = "Feb")
    private BigDecimal m2Quantity;

    @ApiModelProperty(value = "Mar")
    private BigDecimal m3Quantity;

    @ApiModelProperty(value = "Apr")
    private BigDecimal m4Quantity;

    @ApiModelProperty(value = "May")
    private BigDecimal m5Quantity;

    @ApiModelProperty(value = "Jun")
    private BigDecimal m6Quantity;

    @ApiModelProperty(value = "Jul")
    private BigDecimal m7Quantity;

    @ApiModelProperty(value = "Aug")
    private BigDecimal m8Quantity;

    @ApiModelProperty(value = "Sep")
    private BigDecimal m9Quantity;

    @ApiModelProperty(value = "Oct")
    private BigDecimal m10Quantity;

    @ApiModelProperty(value = "Nov")
    private BigDecimal m11Quantity;

    @ApiModelProperty(value = "Dec")
    private BigDecimal m12Quantity;

    @ApiModelProperty(value = "电池分片方式")
    private String cellShard;

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }
}
