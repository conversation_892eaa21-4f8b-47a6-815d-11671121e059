package com.trinasolar.scp.aop.domain.constant;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 基础常量
 */
public interface QPConstant {

    Integer MONTH_NUM = 12;


    int ZERO = 0;

    int ONE = 1;

    BigDecimal SPLITLOSS = BigDecimal.valueOf(2.5);


    @AllArgsConstructor
    @Getter
    enum QuarterMapping {

        Q1(1, new Integer[]{1, 2, 3}),
        Q2(2, new Integer[]{4, 5, 6}),
        Q3(3, new Integer[]{7, 8, 9}),
        Q4(4, new Integer[]{10, 11, 12});

        private Integer code;
        private Integer[] value;

        public static Map<Integer, List<Integer>> loadQuarterMap() {
            Map<Integer, List<Integer>> quarterMap = Maps.newHashMap();
            for (QuarterMapping quarterMapping : values()) {
                quarterMap.put(quarterMapping.code, Arrays.asList(quarterMapping.value));
            }
            return quarterMap;
        }

        public static List<Integer> loadQuarterCode() {
            List<Integer> codes = Lists.newArrayList();
            for (QuarterMapping quarterMapping : values()) {
                codes.add(quarterMapping.code);
            }
            return codes;
        }
    }

    @AllArgsConstructor
    @Getter
    enum MonthMapping {

        M1("1", "一月"),
        M2("2", "二月"),
        M3("3", "三月"),
        M4("4", "四月"),
        M5("5", "五月"),
        M6("6", "六月"),
        M7("7", "七月"),
        M8("8", "八月"),
        M9("9", "九月"),
        M10("10", "十月"),
        M11("11", "十一月"),
        M12("12", "十二月");

        private String code;
        private String value;

        public static List<String> loadMonthCodes() {
            List<String> codes = Lists.newArrayList();
            for (MonthMapping month : values()) {
                codes.add(month.code);
            }
            return codes;
        }
    }

    @AllArgsConstructor
    @Getter
    enum MaterialMatch {

        MATERIAL_MATCH_1("material1", "materialMatch1", "configMaterialMatch1", "材料搭配配置1"),
        MATERIAL_MATCH_2("material2", "materialMatch2", "configMaterialMatch2", "材料搭配配置2"),
        MATERIAL_MATCH_3("material3", "materialMatch3", "configMaterialMatch3", "材料搭配配置3"),
        MATERIAL_MATCH_4("material4", "materialMatch4", "configMaterialMatch4", "材料搭配配置4"),
        MATERIAL_MATCH_5("material5", "materialMatch5", "configMaterialMatch5", "材料搭配配置5"),
        MATERIAL_MATCH_6("material6", "materialMatch6", "configMaterialMatch6", "材料搭配配置6"),
        MATERIAL_MATCH_7("material7", "materialMatch7", "configMaterialMatch7", "材料搭配配置7"),
        MATERIAL_MATCH_8("material8", "materialMatch8", "configMaterialMatch8", "材料搭配配置8"),
        MATERIAL_MATCH_9("material9", "materialMatch9", "configMaterialMatch9", "材料搭配配置9"),
        MATERIAL_MATCH_10("material10", "materialMatch10", "configMaterialMatch10", "材料搭配配置10");

        /**
         * 材料搭配属性值
         */
        private String materialField;
        /**
         * 材料搭配配置表ID
         */
        private String materialMatchField;
        /**
         * 材料搭配配置表对象
         */
        private String configMaterialMatchField;
        /**
         * 备注
         */
        private String remark;

        /**
         * 加载所有材料搭配
         *
         * @return
         */
        public static List<MaterialMatch> loadAllMaterialMatch() {
            List<MaterialMatch> materialMatches = Lists.newArrayList();
            for (MaterialMatch materialMatch : values()) {
                materialMatches.add(materialMatch);
            }
            return materialMatches;
        }
    }

    /**
     * 值集code
     */
    interface Lov {
        /**
         * 产品来源
         */
        String EQS_PRODUCT_FROM = "EQS_PRODUCT_FROM";
        /**
         * 国内/海外
         */
        String DOMESTIC_OVERSEA = "Domestic/Oversea";
        /**
         * 国内/海外
         */
        String AOP_COUNTRY_FLAG = "AOP_COUNTRY_FLAG";
        /**
         * 满产/排产
         */
        String EQS_PARAMETER_TYPE = "EQS_PARAMETER_TYPE";
        /**
         * 产品系列
         */
        String AOP_PRODUCT_SERIES = "aop_product_series";
        /**
         * 晶体类型
         */
        String CRYSTAL_TYPE = "6A00100100145";

        /**
         * 物料小类
         */
        String EQS_ITEM_PO_CATEGORY = "EQS_item_po_category";
        /**
         * 多晶/单晶
         */
        String EQS_CRYSTAL = "EQS_crystal";
        /**
         * 产品族
         */
        String FAMILY_CODE = "Family_Code";
        /**
         * 车间
         */
        String WORK_SHOP = "work_shop";
        /**
         * 组件非Q1/工原损
         */
        String EQS_INDEX_TYPE = "EQS_INDEX_TYPE";
        /**
         * 电池片_产品分类
         */
        String CELL_CLASSIFICATION = "5A00200100113";
        /**
         * 电池片_品类
         */
        String CELL_CATEGORY = "5A00200200129";
        /**
         * 电池片_主栅
         */
        String CELL_MAIN_GATE = "5A00100100105";
        /**
         * 电池片_厚度(μm)
         */
        String CELL_THICKNESS = "5A00100100118";
        /**
         * 电池片_分片方式
         */
        String CELL_SHARD_WAY = "5A00100100131";
        /**
         * 电池片_电池工艺
         */
        String CELL_PROCESS = "5A00100100124";
        /**
         * 电池片_面积
         */
        String CELL_AREA = "5A00100100115";
        /**
         * 电池片_长度
         */
        String CELL_LENGTH = "5A00100100116";
        /**
         * 电池片_宽度（mm）
         */
        String CELL_WIDTH = "5A00100100117";
        /**
         * 物料采购单位
         */
        String EQS_UNIT = "EQS-Unit";
        /**
         * 数量单位（系统计算时需要剔除）
         */
        String EQS_QTY_UNIT = "EQS_qty_unit";
        /**
         * 产品结构
         */
        String PRODUCT_STRUCTURE = "Product_Structure";
        /**
         * 成本区域
         */
        String EQS_COST_REGION = "EQS_Cost_Region";
        /**
         * 库存组织
         */
        String ORGANIZATION = "inventoryOrg";
        /**
         * 物料类别
         */
        String EQS_ITEM_CATEGORY = "EQS_item_category";

        /**
         * 电池指标
         */
        String EQS_BATTERY_INDICATOR = "EQS_battery_indicator";

        /**
         * 组件制造参数
         */
        String EQS_MANUFACTURING_PARAMETER = "EQS_manufacturing_parameter";

        /**
         * 生产基地
         */
        String BASE_PLACE = "base_place";
    }

    /**
     * 安装类型
     */
    @Getter
    @AllArgsConstructor
    enum InstallType {
        /**
         * 横装
         */
        HORIZONTAL("横装"),
        /**
         * 横装
         */
        NONSTANDARD("非标"),
        /**
         * 竖装
         */
        VERTICAL("竖装");

        String value;
    }
}
