package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.aop.domain.utils.MathUtils;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 预处理外购目标/反馈/指定
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 14:51:23
 */
@Entity
@ToString
@Data
@Table(name = "aop_pre_purchase_demand")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_pre_purchase_demand SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_pre_purchase_demand SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class PrePurchaseDemand extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 预处理版本
     */
    @ApiModelProperty(value = "预处理版本")
    @Column(name = "pre_version")
    private String preVersion;

    /**
     * 产品类型：电池/组件/硅片/物料/硅料/
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片/物料/硅料/")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 来源类型：反馈/指定
     */
    @ApiModelProperty(value = "来源类型：反馈/指定")
    @Column(name = "from_type")
    private String fromType;
    /**
     * 业务类型：RMA/OTHER
     */
    @ApiModelProperty(value = "业务类型：RMA/OTHER")
    @Column(name = "business_type")
    private String businessType;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 1月需求量
     */
    @ApiModelProperty(value = "1月需求量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月需求量
     */
    @ApiModelProperty(value = "2月需求量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月需求量
     */
    @ApiModelProperty(value = "3月需求量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 1季度需求量
     */
    @ApiModelProperty(value = "1季度需求量")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 4月需求量
     */
    @ApiModelProperty(value = "4月需求量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月需求量
     */
    @ApiModelProperty(value = "5月需求量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月需求量
     */
    @ApiModelProperty(value = "6月需求量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 2季度需求量
     */
    @ApiModelProperty(value = "2季度需求量")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 7月需求量
     */
    @ApiModelProperty(value = "7月需求量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月需求量
     */
    @ApiModelProperty(value = "8月需求量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月需求量
     */
    @ApiModelProperty(value = "9月需求量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * q3需求量
     */
    @ApiModelProperty(value = "q3需求量")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 10月需求量
     */
    @ApiModelProperty(value = "10月需求量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月需求量
     */
    @ApiModelProperty(value = "11月需求量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月需求量
     */
    @ApiModelProperty(value = "12月需求量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * q4需求量
     */
    @ApiModelProperty(value = "q4需求量")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    @Transient
    private BigDecimal totalQuantity;

    public String fillLovValue(Map<String, LovLineDTO> lovMap) {
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());
        //国内/海外
        if (countryFlagLov == null) {
            return String.format("国内/海外的值集不存在-->{%s}", getFromType());
        }
        setCountryFlag(countryFlagLov.getLovValue());


        //单位
        if (StringUtils.isNotBlank(getUnit())) {
            LovLineDTO unitLov = lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit());
            if (unitLov == null) {
                return String.format("单位的值集不存在-->{%s}", getUnit());
            }
            setUnit(unitLov.getLovValue());
        }
        return null;
    }

    public String selectQuery(PrePurchaseDemand dto) {
        //组件OEM 导入
        if (ProductTypeEnum.MODULE.getCode().equals(dto.getProductType())) {
            if (StringUtils.isBlank(dto.getCountryFlag()) || StringUtils.isBlank(dto.getProductSeries()) || dto.getYear() == null) {
                return "数据异常";
            }
            setProductFrom(ProductFromEnum.OEM.getCode());
            setProductType(dto.getProductType());
        } else {
            //电池/硅片 导入
            if (StringUtils.isBlank(dto.getProductSeries()) || StringUtils.isBlank(dto.getFromType()) || StringUtils.isBlank(dto.getCountryFlag()) || dto.getYear() == null) {
                return "数据异常";
            }
            setProductFrom(ProductFromEnum.PURCHASE.getCode());
            setProductType(dto.getProductType());
        }

        setCountryFlag(dto.getCountryFlag());
        //setDataVersion(dto.getDataVersion());
        setLatestFlag("Y");
        //setVersionType();
        setFromType(dto.getFromType());
        setPreVersion(dto.getPreVersion());
        setProductSeries(dto.getProductSeries());
        setYear(dto.getYear());
        setUnit(dto.getUnit());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());
        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());
        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());
        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());
        return null;
    }

    public void setMothData(PrePurchaseDemand dto) {
        setCountryFlag(dto.getCountryFlag());
        //setDataVersion(dto.getDataVersion());
        setLatestFlag("Y");
        //setVersionType();
        setFromType(dto.getFromType());
        setPreVersion(dto.getPreVersion());
        setProductType(dto.getProductType());
        setProductFrom(dto.getProductFrom());
        setProductSeries(dto.getProductSeries());
        setYear(dto.getYear());
        setUnit(dto.getUnit());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());
        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());
        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());
        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());
    }

    public void perfect(Integer actualMonth, PrePurchaseDemand oemEntity) {

        if (actualMonth >= 1) {
            if (ObjectUtils.isEmpty(this.getM1Quantity()) || oemEntity.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM1Quantity(oemEntity.getM1Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM1Quantity()) && oemEntity.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM1Quantity(this.getM1Quantity().add(oemEntity.getM1Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM1Quantity())) {
                this.setM1Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 2) {
            if (ObjectUtils.isEmpty(this.getM2Quantity()) || oemEntity.getM2Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM2Quantity(oemEntity.getM2Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM2Quantity()) && oemEntity.getM2Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM2Quantity(this.getM2Quantity().add(oemEntity.getM2Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM2Quantity())) {
                this.setM2Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 3) {
            if (ObjectUtils.isEmpty(this.getM3Quantity()) || oemEntity.getM3Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM3Quantity(oemEntity.getM3Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM3Quantity()) && oemEntity.getM3Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM3Quantity(this.getM3Quantity().add(oemEntity.getM3Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM3Quantity())) {
                this.setM3Quantity(BigDecimal.ZERO);
            }
        }

        if (actualMonth >= 4) {
            if (ObjectUtils.isEmpty(this.getM4Quantity()) || oemEntity.getM4Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM4Quantity(oemEntity.getM4Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM4Quantity()) && oemEntity.getM4Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM4Quantity(this.getM4Quantity().add(oemEntity.getM4Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM4Quantity())) {
                this.setM4Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 5) {
            if (ObjectUtils.isEmpty(this.getM5Quantity()) || oemEntity.getM5Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM5Quantity(oemEntity.getM5Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM5Quantity()) && oemEntity.getM5Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM5Quantity(this.getM5Quantity().add(oemEntity.getM5Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM5Quantity())) {
                this.setM5Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 6) {
            if (ObjectUtils.isEmpty(this.getM6Quantity()) || oemEntity.getM6Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM6Quantity(oemEntity.getM6Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM6Quantity()) && oemEntity.getM6Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM6Quantity(this.getM6Quantity().add(oemEntity.getM6Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM6Quantity())) {
                this.setM6Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 7) {
            if (ObjectUtils.isEmpty(this.getM7Quantity()) || oemEntity.getM7Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM7Quantity(oemEntity.getM7Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM7Quantity()) && oemEntity.getM7Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM7Quantity(this.getM7Quantity().add(oemEntity.getM7Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM7Quantity())) {
                this.setM7Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 8) {
            if (ObjectUtils.isEmpty(this.getM8Quantity()) || oemEntity.getM8Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM8Quantity(oemEntity.getM8Quantity());
            }
            if (ObjectUtils.isNotEmpty(this.getM8Quantity()) && oemEntity.getM8Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM8Quantity(this.getM8Quantity().add(oemEntity.getM8Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM8Quantity())) {
                this.setM8Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 9) {
            if (ObjectUtils.isEmpty(this.getM9Quantity()) || oemEntity.getM9Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM9Quantity(oemEntity.getM9Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM9Quantity()) && oemEntity.getM9Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM9Quantity(this.getM9Quantity().add(oemEntity.getM9Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM9Quantity())) {
                this.setM9Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 10) {
            if (ObjectUtils.isEmpty(this.getM10Quantity()) || oemEntity.getM10Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM10Quantity(oemEntity.getM10Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM10Quantity()) && oemEntity.getM10Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM10Quantity(this.getM10Quantity().add(oemEntity.getM10Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM10Quantity())) {
                this.setM10Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 11) {
            if (ObjectUtils.isEmpty(this.getM11Quantity()) || oemEntity.getM11Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM11Quantity(oemEntity.getM11Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM11Quantity()) && oemEntity.getM11Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM11Quantity(this.getM10Quantity().add(oemEntity.getM11Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM11Quantity())) {
                this.setM11Quantity(BigDecimal.ZERO);
            }
        }
        if (actualMonth >= 12) {
            if (ObjectUtils.isEmpty(this.getM12Quantity()) || oemEntity.getM12Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM12Quantity(oemEntity.getM12Quantity());
            } else if (ObjectUtils.isNotEmpty(this.getM12Quantity()) && oemEntity.getM12Quantity().compareTo(BigDecimal.ZERO) != 0) {
                this.setM12Quantity(this.getM12Quantity().add(oemEntity.getM12Quantity()));
            } else if (ObjectUtils.isEmpty(this.getM12Quantity())) {
                this.setM12Quantity(BigDecimal.ZERO);
            }
        }
    }

    public void ltToZero() {
        if (this.getM1Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (this.getM2Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (this.getM3Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (this.getM4Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (this.getM5Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (this.getM6Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (this.getM7Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (this.getM8Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (this.getM9Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (this.getM10Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (this.getM11Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (this.getM12Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }

    public void perfect() {
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }

    public void sum() {
        this.setQ1Quantity(add(this.getM1Quantity(), this.getM2Quantity(), this.getM3Quantity()));
        this.setQ2Quantity(add(this.getM4Quantity(), this.getM5Quantity(), this.getM6Quantity()));
        this.setQ3Quantity(add(this.getM7Quantity(), this.getM8Quantity(), this.getM9Quantity()));
        this.setQ4Quantity(add(this.getM10Quantity(), this.getM11Quantity(), this.getM12Quantity()));
        this.setTotalQuantity(add(this.getQ1Quantity(), this.getQ2Quantity(), this.getQ3Quantity(), this.getQ4Quantity()));
    }

    private BigDecimal add(BigDecimal... args) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal arg : args) {
            if (ObjectUtils.isNotEmpty(arg)) {
                result = result.add(arg);
            }
        }
        return result;
    }

    public BigDecimal sumTotal() {
        return MathUtils.addBigDecimalToZero(this.getM1Quantity(), this.getM2Quantity(), this.getM3Quantity(), this.getM4Quantity(), this.getM5Quantity(), this.getM6Quantity(), this.getM7Quantity(), this.getM8Quantity(), this.getM9Quantity(), this.getM10Quantity(), this.getM11Quantity(), this.getM12Quantity());
    }
}
