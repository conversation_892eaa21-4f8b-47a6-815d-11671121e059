package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 外购反馈
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:41:26
 */
@Entity
@ToString
@Data
@Table(name = "aop_purchase_feedback")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_purchase_feedback SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_purchase_feedback SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PurchaseFeedback extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 外购目标ID
     */
    @ApiModelProperty(value = "外购目标ID")
    @Column(name = "purchase_id")
    private Long purchaseId;

    /**
     * 来源类型：反馈/指定
     */
    @ApiModelProperty(value = "来源类型：反馈/指定")
    @Column(name = "from_type")
    private String fromType;

    /**
     * 1月数量
     */
    @ApiModelProperty(value = "1月数量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月数量
     */
    @ApiModelProperty(value = "2月数量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月数量
     */
    @ApiModelProperty(value = "3月数量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月数量
     */
    @ApiModelProperty(value = "4月数量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月数量
     */
    @ApiModelProperty(value = "5月数量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月数量
     */
    @ApiModelProperty(value = "6月数量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月数量
     */
    @ApiModelProperty(value = "7月数量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月数量
     */
    @ApiModelProperty(value = "8月数量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月数量
     */
    @ApiModelProperty(value = "9月数量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月数量
     */
    @ApiModelProperty(value = "10月数量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月数量
     */
    @ApiModelProperty(value = "11月数量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月数量
     */
    @ApiModelProperty(value = "12月数量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;


}
