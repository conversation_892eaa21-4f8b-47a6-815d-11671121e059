/**
 * <AUTHOR>
 * @date 2024-11-15 16:33
 */
package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/15 16:33
 */
@Entity
@ToString
@Data
@Table(name = "aop_third_a_outgoing_battery")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_third_a_outgoing_battery SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_third_a_outgoing_battery SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AopThirdAOutgoingBattery extends BasePO implements Serializable {

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "国内国外")
    @Column(name = "country_flag")
    private String countryFlag;

    @ApiModelProperty(value = "电池等级")
    @Column(name = "battery_level")
    private String batteryLevel;

    @ApiModelProperty(value = "电池尺寸")
    @Column(name = "battery_size")
    private String batterySize;

    @ApiModelProperty(value = "晶体类型")
    @Column(name = "crystal_type")
    private String crystalType;

    @ApiModelProperty(value = "分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    @ApiModelProperty(value = "数量")
    @Column(name = "quantity")
    private BigDecimal quantity;

    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;
}
