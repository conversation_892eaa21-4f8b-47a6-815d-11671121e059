package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductFamilyCalculationPowerMaintenanceDTO;
import com.trinasolar.scp.aop.domain.entity.ProductFamilyCalculationPowerMaintenance;
import com.trinasolar.scp.aop.domain.excel.ProductFamilyCalculationPowerMaintenanceExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ProductFamilyCalculationPowerMaintenanceImportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 产品族测算功率维护 DTO与实体转换器
 *
 * <AUTHOR>
 * @email
 * @date 2025-01-07
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductFamilyCalculationPowerMaintenanceDEConvert extends BaseDEConvert<ProductFamilyCalculationPowerMaintenanceDTO, ProductFamilyCalculationPowerMaintenance> {

    ProductFamilyCalculationPowerMaintenanceDEConvert INSTANCE = Mappers.getMapper(ProductFamilyCalculationPowerMaintenanceDEConvert.class);

    List<ProductFamilyCalculationPowerMaintenanceExportExcelDTO> toProductFamilyCalculationPowerMaintenanceExcelDTO(List<ProductFamilyCalculationPowerMaintenanceDTO> dtos);

    ProductFamilyCalculationPowerMaintenanceDTO importExcelToDto(ProductFamilyCalculationPowerMaintenanceImportExcelDTO importExcelDTO);
}
