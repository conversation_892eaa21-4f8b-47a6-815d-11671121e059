package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 兼容产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 14:29:40
 */
@Entity
@ToString
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "aop_origin_capacity_compatibility")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_origin_capacity_compatibility SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_origin_capacity_compatibility SET is_deleted = 1 WHERE id = ?")
@AllArgsConstructor
@NoArgsConstructor
public class OriginCapacityCompatibility extends BasePO implements Serializable, Comparable<OriginCapacityCompatibility> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 是否海外
     */
    @ApiModelProperty(value = "是否海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 总线数
     */
    @ApiModelProperty(value = "总线数")
    @Column(name = "total_line_num")
    private Integer totalLineNum;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @Column(name = "data_from")
    private String dataFrom;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;
    /**
     * 使用的满产产能版本号
     */
    @ApiModelProperty(value = "使用的满产产能版本号")
    @Column(name = "origin_capacity_version")
    private String originCapacityVersion;

    /**
     * 1月产能
     */
    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月产能
     */
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月产能
     */
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月产能
     */
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月产能
     */
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月产能
     */
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月产能
     */
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月产能
     */
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月产能
     */
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月产能
     */
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月产能
     */
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月产能
     */
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 1月线数
     */
    @ApiModelProperty(value = "1月线数")
    @Column(name = "m1_line_number")
    private Integer m1LineNumber;

    /**
     * 2月线数
     */
    @ApiModelProperty(value = "2月线数")
    @Column(name = "m2_line_number")
    private Integer m2LineNumber;

    /**
     * 3月线数
     */
    @ApiModelProperty(value = "3月线数")
    @Column(name = "m3_line_number")
    private Integer m3LineNumber;

    /**
     * 4月线数
     */
    @ApiModelProperty(value = "4月线数")
    @Column(name = "m4_line_number")
    private Integer m4LineNumber;

    /**
     * 5月线数
     */
    @ApiModelProperty(value = "5月线数")
    @Column(name = "m5_line_number")
    private Integer m5LineNumber;

    /**
     * 6月线数
     */
    @ApiModelProperty(value = "6月线数")
    @Column(name = "m6_line_number")
    private Integer m6LineNumber;

    /**
     * 7月线数
     */
    @ApiModelProperty(value = "7月线数")
    @Column(name = "m7_line_number")
    private Integer m7LineNumber;

    /**
     * 8月线数
     */
    @ApiModelProperty(value = "8月线数")
    @Column(name = "m8_line_number")
    private Integer m8LineNumber;

    /**
     * 9月线数
     */
    @ApiModelProperty(value = "9月线数")
    @Column(name = "m9_line_number")
    private Integer m9LineNumber;

    /**
     * 10月线数
     */
    @ApiModelProperty(value = "10月线数")
    @Column(name = "m10_line_number")
    private Integer m10LineNumber;

    /**
     * 11月线数
     */
    @ApiModelProperty(value = "11月线数")
    @Column(name = "m11_line_number")
    private Integer m11LineNumber;

    /**
     * 12月线数
     */
    @ApiModelProperty(value = "12月线数")
    @Column(name = "m12_line_number")
    private Integer m12LineNumber;

    @Transient
    private BigDecimal q1Quantity;
    @Transient
    private BigDecimal q2Quantity;
    @Transient
    private BigDecimal q3Quantity;
    @Transient
    private BigDecimal q4Quantity;
    @Transient
    private BigDecimal totalQuantity;

    @Transient
    private Integer priority;

    public void sum() {
        this.setQ1Quantity(add(this.getM1Quantity(), this.getM2Quantity(), this.getM3Quantity()));
        this.setQ2Quantity(add(this.getM4Quantity(), this.getM5Quantity(), this.getM6Quantity()));
        this.setQ3Quantity(add(this.getM7Quantity(), this.getM8Quantity(), this.getM9Quantity()));
        this.setQ4Quantity(add(this.getM10Quantity(), this.getM11Quantity(), this.getM12Quantity()));
        this.setTotalQuantity(add(this.getQ1Quantity(), this.getQ2Quantity(), this.getQ3Quantity(), this.getQ4Quantity()));
    }

    private BigDecimal add(BigDecimal... args) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal arg : args) {
            if (arg != null) {
                result = result.add(arg);
            }
        }
        return result;
    }

    public void setEntity(OriginCapacityCompatibility originCapacityCompatibility) {
        setCountryFlag(originCapacityCompatibility.getCountryFlag());
        setProductType(originCapacityCompatibility.getProductType());
        setDataFrom(originCapacityCompatibility.getDataFrom());
        setWorkshop(originCapacityCompatibility.getWorkshop());
        setProductSeries(originCapacityCompatibility.getProductSeries());
        setProductGroup(originCapacityCompatibility.getProductGroup());
        setYear(originCapacityCompatibility.getYear());

        setM1Quantity(originCapacityCompatibility.getM1Quantity());
        setM2Quantity(originCapacityCompatibility.getM2Quantity());
        setM3Quantity(originCapacityCompatibility.getM3Quantity());
        setM4Quantity(originCapacityCompatibility.getM4Quantity());
        setM5Quantity(originCapacityCompatibility.getM5Quantity());
        setM6Quantity(originCapacityCompatibility.getM6Quantity());
        setM7Quantity(originCapacityCompatibility.getM7Quantity());
        setM8Quantity(originCapacityCompatibility.getM8Quantity());
        setM9Quantity(originCapacityCompatibility.getM9Quantity());
        setM10Quantity(originCapacityCompatibility.getM10Quantity());
        setM11Quantity(originCapacityCompatibility.getM11Quantity());
        setM12Quantity(originCapacityCompatibility.getM12Quantity());

        setM1LineNumber(originCapacityCompatibility.getM1LineNumber());
        setM2LineNumber(originCapacityCompatibility.getM2LineNumber());
        setM3LineNumber(originCapacityCompatibility.getM3LineNumber());
        setM4LineNumber(originCapacityCompatibility.getM4LineNumber());
        setM5LineNumber(originCapacityCompatibility.getM5LineNumber());
        setM6LineNumber(originCapacityCompatibility.getM6LineNumber());
        setM7LineNumber(originCapacityCompatibility.getM7LineNumber());
        setM8LineNumber(originCapacityCompatibility.getM8LineNumber());
        setM9LineNumber(originCapacityCompatibility.getM9LineNumber());
        setM10LineNumber(originCapacityCompatibility.getM10LineNumber());
        setM11LineNumber(originCapacityCompatibility.getM11LineNumber());
        setM12LineNumber(originCapacityCompatibility.getM12LineNumber());
    }

    public String setInitialValue(OriginCapacityCompatibility originCapacityCompatibility) {
        String error = "";
        if (
                StringUtils.isBlank(originCapacityCompatibility.getWorkshop()) || StringUtils.isBlank(originCapacityCompatibility.getProductSeries()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getYear()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM1LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM2LineNumber()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM3LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM4LineNumber()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM5LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM6LineNumber()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM7LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM8LineNumber()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM9LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM10LineNumber()) || ObjectUtils.isEmpty(originCapacityCompatibility.getM11LineNumber()) ||
                        ObjectUtils.isEmpty(originCapacityCompatibility.getM12LineNumber())
        ) {
            return error + "数据异常，请检查是否有空字段";
        }

        setCountryFlag(originCapacityCompatibility.getCountryFlag());
        setProductType(originCapacityCompatibility.getProductType());
        setDataFrom(originCapacityCompatibility.getDataFrom());
        setWorkshop(originCapacityCompatibility.getWorkshop());
        setProductSeries(originCapacityCompatibility.getProductSeries());
        setProductGroup(originCapacityCompatibility.getProductGroup());
        setYear(originCapacityCompatibility.getYear());

        setM1Quantity(originCapacityCompatibility.getM1Quantity());
        setM2Quantity(originCapacityCompatibility.getM2Quantity());
        setM3Quantity(originCapacityCompatibility.getM3Quantity());
        setM4Quantity(originCapacityCompatibility.getM4Quantity());
        setM5Quantity(originCapacityCompatibility.getM5Quantity());
        setM6Quantity(originCapacityCompatibility.getM6Quantity());
        setM7Quantity(originCapacityCompatibility.getM7Quantity());
        setM8Quantity(originCapacityCompatibility.getM8Quantity());
        setM9Quantity(originCapacityCompatibility.getM9Quantity());
        setM10Quantity(originCapacityCompatibility.getM10Quantity());
        setM11Quantity(originCapacityCompatibility.getM11Quantity());
        setM12Quantity(originCapacityCompatibility.getM12Quantity());

        setM1LineNumber(originCapacityCompatibility.getM1LineNumber());
        setM2LineNumber(originCapacityCompatibility.getM2LineNumber());
        setM3LineNumber(originCapacityCompatibility.getM3LineNumber());
        setM4LineNumber(originCapacityCompatibility.getM4LineNumber());
        setM5LineNumber(originCapacityCompatibility.getM5LineNumber());
        setM6LineNumber(originCapacityCompatibility.getM6LineNumber());
        setM7LineNumber(originCapacityCompatibility.getM7LineNumber());
        setM8LineNumber(originCapacityCompatibility.getM8LineNumber());
        setM9LineNumber(originCapacityCompatibility.getM9LineNumber());
        setM10LineNumber(originCapacityCompatibility.getM10LineNumber());
        setM11LineNumber(originCapacityCompatibility.getM11LineNumber());
        setM12LineNumber(originCapacityCompatibility.getM12LineNumber());
        return error;
    }

    @Override
    public int compareTo(OriginCapacityCompatibility inner) {
        return this.getPriority().compareTo(inner.getPriority());
    }
}
