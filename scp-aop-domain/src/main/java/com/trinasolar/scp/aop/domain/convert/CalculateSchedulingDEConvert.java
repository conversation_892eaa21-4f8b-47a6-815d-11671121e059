package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateSchedulingDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateScheduling;
import com.trinasolar.scp.aop.domain.entity.ImportActualCapacity;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 计算排产量表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:14:43
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateSchedulingDEConvert extends BaseDEConvert<CalculateSchedulingDTO, CalculateScheduling> {

    CalculateSchedulingDEConvert INSTANCE = Mappers.getMapper(CalculateSchedulingDEConvert.class);

    List<CalculateScheduling> importToList(List<ImportActualCapacity> list);
}
