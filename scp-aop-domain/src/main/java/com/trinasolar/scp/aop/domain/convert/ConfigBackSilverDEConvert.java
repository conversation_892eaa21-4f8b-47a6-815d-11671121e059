package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigBackSilverDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigBackSilver;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 背银行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:49
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigBackSilverDEConvert extends BaseDEConvert<ConfigBackSilverDTO, ConfigBackSilver> {

    ConfigBackSilverDEConvert INSTANCE = Mappers.getMapper(ConfigBackSilverDEConvert.class);

}
