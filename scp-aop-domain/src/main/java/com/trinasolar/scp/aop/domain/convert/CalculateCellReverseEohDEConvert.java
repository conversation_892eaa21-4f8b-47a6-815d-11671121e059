package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateCellReverseEohDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateCellReverseEoh;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池反算EOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:15:03
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateCellReverseEohDEConvert extends BaseDEConvert<CalculateCellReverseEohDTO, CalculateCellReverseEoh> {

    CalculateCellReverseEohDEConvert INSTANCE = Mappers.getMapper(CalculateCellReverseEohDEConvert.class);

}
