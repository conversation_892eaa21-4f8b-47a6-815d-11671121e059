package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.OriginCapacityCompatibility;
import com.trinasolar.scp.aop.domain.entity.OriginCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @className CalculateCapacityUtils
 * @description 兼容产能和原始产能转换
 * @date 2022-08-23
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginCapacityDEConvert extends BaseDEConvert<OriginCapacityIe, OriginCapacityCompatibility> {
    OriginCapacityDEConvert INSTANCE = Mappers.getMapper(OriginCapacityDEConvert.class);

    List<OriginCapacityIe> toCapacity(List<OriginCapacityCompatibility> originCapacityCompatibilityList);
}
