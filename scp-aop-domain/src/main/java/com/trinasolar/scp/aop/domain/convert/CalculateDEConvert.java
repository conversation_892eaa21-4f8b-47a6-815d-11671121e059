package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateDTO;
import com.trinasolar.scp.aop.domain.entity.Calculate;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @Date 2024/5/24
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateDEConvert extends BaseDEConvert<CalculateDTO, Calculate> {
    CalculateDEConvert INSTANCE = Mappers.getMapper(CalculateDEConvert.class);
}
