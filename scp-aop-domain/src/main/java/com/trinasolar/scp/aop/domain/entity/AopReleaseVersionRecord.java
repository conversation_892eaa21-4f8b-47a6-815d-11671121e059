package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2024/4/25
 */
@Entity
@ToString
@Data
@Table(name = "aop_release_version_record")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_release_version_record SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_release_version_record SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AopReleaseVersionRecord extends BasePO implements Serializable {
    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    @ApiModelProperty(value = "调整生成版本号")
    @Column(name = "aop_version")
    private String aopVersion;

    @ApiModelProperty(value = "调整生成版本号的后台ID")
    @Column(name = "aop_version_id")
    private Long aopVersionId;

    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    @ApiModelProperty(value = "预处理版本号")
    @Column(name = "pre_version")
    private String preVersion;

    @ApiModelProperty(value = "发布时间，按钮点击时间")
    @Column(name = "release_date")
    private LocalDateTime releaseDate;

    @ApiModelProperty(value = "是否为MAPS标记的版本")
    @Column(name = "maps_flag")
    private String mapsFlag;

    @ApiModelProperty(value = "是否为DP标记的版本")
    @Column(name = "dp_flag")
    private String dpFlag;
}
