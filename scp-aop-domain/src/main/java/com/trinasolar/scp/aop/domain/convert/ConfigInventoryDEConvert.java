package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigInventoryDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigInventory;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/20 13:19
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigInventoryDEConvert extends BaseDEConvert<ConfigInventory, ConfigInventoryDTO> {
    ConfigInventoryDEConvert INSTANCE = Mappers.getMapper(ConfigInventoryDEConvert.class);

    List<ConfigInventory> toConfigInventorys(List<ConfigInventoryDTO> ConfigInventoryDTOs);

    List<ConfigInventoryDTO> toConfigInventoryDTOs(List<ConfigInventory> ConfigInventorys);

}
