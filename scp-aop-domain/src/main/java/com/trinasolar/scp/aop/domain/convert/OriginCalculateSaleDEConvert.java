package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculateSale;
import com.trinasolar.scp.aop.domain.entity.OriginSale;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;
/**
 * <AUTHOR>
 * @version v1.0
 * @className CalculateCapacityUtils
 * @description 原始产能和计算产能转换类
 * @date 2022-08-23
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginCalculateSaleDEConvert extends BaseDEConvert<CalculateSale, OriginSale> {

    OriginCalculateSaleDEConvert INSTANCE = Mappers.getMapper(OriginCalculateSaleDEConvert.class);

    List<CalculateSale> toCalculate(List<OriginSale> list);
}
