package com.trinasolar.scp.aop.domain.constant;

import com.trinasolar.scp.aop.domain.enums.CountryFlagEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @ClassName CountryMapping
 * @Description 区域映射，报价系统国内，海外转换为aop
 * <AUTHOR>
 * @Date 2022/9/6 18:17
 * @Version 1.0
 */
@Getter
@AllArgsConstructor
public enum CountryMapping {
    /**
     *
     */
    CN("CN", CountryFlagEnum.INLAND, "国内"),
    OS("OS", CountryFlagEnum.OVERSEA, "海外"),
    OS2("OS", CountryFlagEnum.OVERSEA, "国外"),
    EU("EU", CountryFlagEnum.INLAND, "欧洲");

    String code;
    CountryFlagEnum aopEnum;
    String remark;

    /**
     * 匹配
     *
     * @param code
     * @return
     */
    public static CountryMapping match(String code) {
        CountryMapping[] values = CountryMapping.values();
        for (CountryMapping value : values) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static CountryMapping matchByAll(String code) {
        CountryMapping[] values = CountryMapping.values();
        for (CountryMapping value : values) {
            if (value.code.equals(code)
                || value.aopEnum.getCode().equals(code)
                || value.remark.equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * aop枚举转换eqs
     * @param isOversea
     * @return
     */
    public static String convertEqsArea(String isOversea){
        CountryFlagEnum countryFlag = CountryFlagEnum.getCountryFlagEnumCode(isOversea);
        CountryMapping[] values = CountryMapping.values();
        for (CountryMapping value : values) {
            if (value.aopEnum.equals(countryFlag)) {
                return value.getCode();
            }
        }
        return null;
    }

}
