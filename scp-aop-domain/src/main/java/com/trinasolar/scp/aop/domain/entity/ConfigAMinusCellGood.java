package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A-良率行表
 *
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_a_minus_cell_good")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_a_minus_cell_good SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_a_minus_cell_good SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigAMinusCellGood extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;

    /**
     * 满产/排产
     */
    @ApiModelProperty(value = "满产/排产")
    @Column(name = "parameter_type")
    private String parameterType;

    /**
     * 产品分类
     */
    @ApiModelProperty(value = "产品分类")
    @Column(name = "product_type")
    private String productType;

    /**
     * 品类
     */
    @ApiModelProperty(value = "品类")
    @Column(name = "product_category")
    private String productCategory;

    /**
     * 主栅
     */
    @ApiModelProperty(value = "主栅")
    @Column(name = "main_grid")
    private String mainGrid;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    @Column(name = "crystal_type")
    private String crystalType;

    /**
     * 单晶多晶
     */
    @ApiModelProperty(value = "单晶多晶")
    @Column(name = "crystal_spec")
    private String crystalSpec;

    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    @Column(name = "cell_high")
    private BigDecimal cellHigh;

    /**
     * 厚度单位
     */
    @ApiModelProperty(value = "厚度单位")
    @Column(name = "high_unit")
    private String highUnit;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 1月数
     */
    @ApiModelProperty(value = "1月数")
    @Column(name = "quantity_m1")
    private BigDecimal quantityM1;

    /**
     * 2月数
     */
    @ApiModelProperty(value = "2月数")
    @Column(name = "quantity_m2")
    private BigDecimal quantityM2;

    /**
     * 3月数
     */
    @ApiModelProperty(value = "3月数")
    @Column(name = "quantity_m3")
    private BigDecimal quantityM3;

    /**
     * 4月数
     */
    @ApiModelProperty(value = "4月数")
    @Column(name = "quantity_m4")
    private BigDecimal quantityM4;

    /**
     * 5月数
     */
    @ApiModelProperty(value = "5月数")
    @Column(name = "quantity_m5")
    private BigDecimal quantityM5;

    /**
     * 6月数
     */
    @ApiModelProperty(value = "6月数")
    @Column(name = "quantity_m6")
    private BigDecimal quantityM6;

    /**
     * 7月数
     */
    @ApiModelProperty(value = "7月数")
    @Column(name = "quantity_m7")
    private BigDecimal quantityM7;

    /**
     * 8月数
     */
    @ApiModelProperty(value = "8月数")
    @Column(name = "quantity_m8")
    private BigDecimal quantityM8;

    /**
     * 9月数
     */
    @ApiModelProperty(value = "9月数")
    @Column(name = "quantity_m9")
    private BigDecimal quantityM9;

    /**
     * 10月数
     */
    @ApiModelProperty(value = "10月数")
    @Column(name = "quantity_m10")
    private BigDecimal quantityM10;

    /**
     * 11月数
     */
    @ApiModelProperty(value = "11月数")
    @Column(name = "quantity_m11")
    private BigDecimal quantityM11;

    /**
     * 12月数
     */
    @ApiModelProperty(value = "12月数")
    @Column(name = "quantity_m12")
    private BigDecimal quantityM12;


}
