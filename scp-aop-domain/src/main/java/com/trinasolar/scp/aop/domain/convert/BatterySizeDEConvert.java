package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.BatterySizeDTO;
import com.trinasolar.scp.aop.domain.entity.BatterySize;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 电池面积表 
 *
 * <AUTHOR>
 * @date 2022-10-11
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface BatterySizeDEConvert extends BaseDEConvert<BatterySizeDTO, BatterySize> {
}