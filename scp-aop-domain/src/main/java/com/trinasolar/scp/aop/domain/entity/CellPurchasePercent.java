package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池外购占比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-15 11:18:25
 */
@Entity
@ToString
@Data
@Table(name = "aop_cell_purchase_percent")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_cell_purchase_percent SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_cell_purchase_percent SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CellPurchasePercent extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 产品来源：自产/外购
     */
    @ApiModelProperty(value = "产品来源：自产/外购")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 月度
     */
    @ApiModelProperty(value = "月度")
    @Column(name = "month")
    private String month;

    /**
     * 占比
     */
    @ApiModelProperty(value = "占比")
    @Column(name = "percent")
    private BigDecimal percent;


}
