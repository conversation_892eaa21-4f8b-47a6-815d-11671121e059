package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.HorizontalOrVerticalDTO;
import com.trinasolar.scp.aop.domain.entity.HorizontalOrVertical;
import com.trinasolar.scp.aop.domain.excel.HorizontalOrVerticalExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.HorizontalOrVerticalImportExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * DTO与实体转换器
 *
 * <AUTHOR>
 * @date 2022-07-12 10:06:20
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HorizontalOrVerticalDEConvert extends BaseDEConvert<HorizontalOrVerticalDTO, HorizontalOrVertical> {

    HorizontalOrVerticalDEConvert INSTANCE = Mappers.getMapper(HorizontalOrVerticalDEConvert.class);

    @Override
    HorizontalOrVerticalDTO toDto(HorizontalOrVertical horizontalOrVertical);

    List<HorizontalOrVerticalExportExcelDTO> toExportExcelDtos(List<HorizontalOrVerticalDTO> dtos);

    HorizontalOrVerticalDTO importExcelToDto(HorizontalOrVerticalImportExportExcelDTO importExcelDTO);
}
