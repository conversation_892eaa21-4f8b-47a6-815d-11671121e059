package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 制造参数材料搭配设置表 
 *
 * <AUTHOR>
 * @date 2022-9-28
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_material_match")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_material_match SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_material_match SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigMaterialMatch extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 
    *主键 
    */
     @Id
     @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
     @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id ;
    /** 
    *物料分类 
    */
    @ApiModelProperty(value = "物料分类")
    @Column(name = "classify")
    private String classify ;
    /** 
    *物料结构 
    */
    @ApiModelProperty(value = "物料结构")
    @Column(name = "structure")
    private String structure ;
    /** 
    *物料属性 
    */
    @ApiModelProperty(value = "物料属性")
    @Column(name = "attribute")
    private String attribute ;
    /** 
    *物料属性值 
    */
    @ApiModelProperty(value = "物料属性值")
    @Column(name = "attribute_value")
    private String attributeValue ;
    /**
     * 属性值集
     */
    @ApiModelProperty(value = "属性值集")
    @Column(name = "attribute_lov")
    private String attributeLov;
}