package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 投产效率表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:07
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_production")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_production SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_production SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigProduction extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 产品来源：自产/外购
     */
    @ApiModelProperty(value = "产品来源：自产/外购")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 晶体类型
     */
    @ApiModelProperty(value = "晶体类型")
    @Column(name = "crystal_type")
    private String crystalType;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 材料搭配1
     */
    @ApiModelProperty(value = "材料搭配1")
    @Column(name = "material1")
    private String material1;

    /**
     * 材料搭配2
     */
    @ApiModelProperty(value = "材料搭配2")
    @Column(name = "material2")
    private String material2;

    /**
     * 材料搭配3
     */
    @ApiModelProperty(value = "材料搭配3")
    @Column(name = "material3")
    private String material3;

    /**
     * 材料搭配4
     */
    @ApiModelProperty(value = "材料搭配4")
    @Column(name = "material4")
    private String material4;

    /**
     * 材料搭配5
     */
    @ApiModelProperty(value = "材料搭配5")
    @Column(name = "material5")
    private String material5;

    /**
     * 材料搭配6
     */
    @ApiModelProperty(value = "材料搭配6")
    @Column(name = "material6")
    private String material6;

    /**
     * 材料搭配7
     */
    @ApiModelProperty(value = "材料搭配7")
    @Column(name = "material7")
    private String material7;

    /**
     * 材料搭配8
     */
    @ApiModelProperty(value = "材料搭配8")
    @Column(name = "material8")
    private String material8;

    /**
     * 材料搭配9
     */
    @ApiModelProperty(value = "材料搭配9")
    @Column(name = "material9")
    private String material9;

    /**
     * 材料搭配10
     */
    @ApiModelProperty(value = "材料搭配10")
    @Column(name = "material10")
    private String material10;
    /**
     * 材料搭配1
     */
    @ApiModelProperty(value = "材料搭配1")
    @Column(name = "material_str1")
    private String materialStr1;
    /**
     * 材料搭配2
     */
    @ApiModelProperty(value = "材料搭配2")
    @Column(name = "material_str2")
    private String materialStr2;
    /**
     * 材料搭配3
     */
    @ApiModelProperty(value = "材料搭配3")
    @Column(name = "material_str3")
    private String materialStr3;
    /**
     * 材料搭配4
     */
    @ApiModelProperty(value = "材料搭配4")
    @Column(name = "material_str4")
    private String materialStr4;
    /**
     * 材料搭配5
     */
    @ApiModelProperty(value = "材料搭配5")
    @Column(name = "material_str5")
    private String materialStr5;
    /**
     * 材料搭配6
     */
    @ApiModelProperty(value = "材料搭配6")
    @Column(name = "material_str6")
    private String materialStr6;
    /**
     * 材料搭配7
     */
    @ApiModelProperty(value = "材料搭配7")
    @Column(name = "material_str7")
    private String materialStr7;
    /**
     * 材料搭配8
     */
    @ApiModelProperty(value = "材料搭配8")
    @Column(name = "material_str8")
    private String materialStr8;
    /**
     * 材料搭配9
     */
    @ApiModelProperty(value = "材料搭配9")
    @Column(name = "material_str9")
    private String materialStr9;
    /**
     * 材料搭配10
     */
    @ApiModelProperty(value = "材料搭配10")
    @Column(name = "material_str10")
    private String materialStr10;

    /**
     * 材料搭配配置1
     */
    @ApiModelProperty(value = "材料搭配配置1")
    @Column(name = "material_match1")
    private Long materialMatch1;
    /**
     * 材料搭配配置2
     */
    @ApiModelProperty(value = "材料搭配配置2")
    @Column(name = "material_match2")
    private Long materialMatch2;
    /**
     * 材料搭配配置3
     */
    @ApiModelProperty(value = "材料搭配配置3")
    @Column(name = "material_match3")
    private Long materialMatch3;
    /**
     * 材料搭配配置4
     */
    @ApiModelProperty(value = "材料搭配配置4")
    @Column(name = "material_match4")
    private Long materialMatch4;
    /**
     * 材料搭配配置5
     */
    @ApiModelProperty(value = "材料搭配配置5")
    @Column(name = "material_match5")
    private Long materialMatch5;
    /**
     * 材料搭配配置6
     */
    @ApiModelProperty(value = "材料搭配配置6")
    @Column(name = "material_match6")
    private Long materialMatch6;
    /**
     * 材料搭配配置7
     */
    @ApiModelProperty(value = "材料搭配配置7")
    @Column(name = "material_match7")
    private Long materialMatch7;
    /**
     * 材料搭配配置8
     */
    @ApiModelProperty(value = "材料搭配配置8")
    @Column(name = "material_match8")
    private Long materialMatch8;
    /**
     * 材料搭配配置9
     */
    @ApiModelProperty(value = "材料搭配配置9")
    @Column(name = "material_match9")
    private Long materialMatch9;
    /**
     * 材料搭配配置10
     */
    @ApiModelProperty(value = "材料搭配配置10")
    @Column(name = "material_match10")
    private Long materialMatch10;

    /**
     * 1月投产效率
     */
    @ApiModelProperty(value = "1月投产效率")
    @Column(name = "efficiency_m1")
    private BigDecimal efficiencyM1;

    /**
     * 2月投产效率
     */
    @ApiModelProperty(value = "2月投产效率")
    @Column(name = "efficiency_m2")
    private BigDecimal efficiencyM2;

    /**
     * 3月投产效率
     */
    @ApiModelProperty(value = "3月投产效率")
    @Column(name = "efficiency_m3")
    private BigDecimal efficiencyM3;

    /**
     * 4月投产效率
     */
    @ApiModelProperty(value = "4月投产效率")
    @Column(name = "efficiency_m4")
    private BigDecimal efficiencyM4;

    /**
     * 5月投产效率
     */
    @ApiModelProperty(value = "5月投产效率")
    @Column(name = "efficiency_m5")
    private BigDecimal efficiencyM5;

    /**
     * 6月投产效率
     */
    @ApiModelProperty(value = "6月投产效率")
    @Column(name = "efficiency_m6")
    private BigDecimal efficiencyM6;

    /**
     * 7月投产效率
     */
    @ApiModelProperty(value = "7月投产效率")
    @Column(name = "efficiency_m7")
    private BigDecimal efficiencyM7;

    /**
     * 8月投产效率
     */
    @ApiModelProperty(value = "8月投产效率")
    @Column(name = "efficiency_m8")
    private BigDecimal efficiencyM8;

    /**
     * 9月投产效率
     */
    @ApiModelProperty(value = "9月投产效率")
    @Column(name = "efficiency_m9")
    private BigDecimal efficiencyM9;

    /**
     * 10月投产效率
     */
    @ApiModelProperty(value = "10月投产效率")
    @Column(name = "efficiency_m10")
    private BigDecimal efficiencyM10;

    /**
     * 11月投产效率
     */
    @ApiModelProperty(value = "11月投产效率")
    @Column(name = "efficiency_m11")
    private BigDecimal efficiencyM11;

    /**
     * 12月投产效率
     */
    @ApiModelProperty(value = "12月投产效率")
    @Column(name = "efficiency_m12")
    private BigDecimal efficiencyM12;

    /**
     * 1月竖装功率
     */
    @ApiModelProperty(value = "1月竖装功率")
    @Column(name = "vertical_power_m1")
    private BigDecimal verticalPowerM1;

    /**
     * 2月竖装功率
     */
    @ApiModelProperty(value = "2月竖装功率")
    @Column(name = "vertical_power_m2")
    private BigDecimal verticalPowerM2;

    /**
     * 3月竖装功率
     */
    @ApiModelProperty(value = "3月竖装功率")
    @Column(name = "vertical_power_m3")
    private BigDecimal verticalPowerM3;

    /**
     * 4月竖装功率
     */
    @ApiModelProperty(value = "4月竖装功率")
    @Column(name = "vertical_power_m4")
    private BigDecimal verticalPowerM4;

    /**
     * 5月竖装功率
     */
    @ApiModelProperty(value = "5月竖装功率")
    @Column(name = "vertical_power_m5")
    private BigDecimal verticalPowerM5;

    /**
     * 6月竖装功率
     */
    @ApiModelProperty(value = "6月竖装功率")
    @Column(name = "vertical_power_m6")
    private BigDecimal verticalPowerM6;

    /**
     * 7月竖装功率
     */
    @ApiModelProperty(value = "7月竖装功率")
    @Column(name = "vertical_power_m7")
    private BigDecimal verticalPowerM7;

    /**
     * 8月竖装功率
     */
    @ApiModelProperty(value = "8月竖装功率")
    @Column(name = "vertical_power_m8")
    private BigDecimal verticalPowerM8;

    /**
     * 9月竖装功率
     */
    @ApiModelProperty(value = "9月竖装功率")
    @Column(name = "vertical_power_m9")
    private BigDecimal verticalPowerM9;

    /**
     * 10月竖装功率
     */
    @ApiModelProperty(value = "10月竖装功率")
    @Column(name = "vertical_power_m10")
    private BigDecimal verticalPowerM10;

    /**
     * 11月竖装功率
     */
    @ApiModelProperty(value = "11月竖装功率")
    @Column(name = "vertical_power_m11")
    private BigDecimal verticalPowerM11;

    /**
     * 12月竖装功率
     */
    @ApiModelProperty(value = "12月竖装功率")
    @Column(name = "vertical_power_m12")
    private BigDecimal verticalPowerM12;

    /**
     * 1月横装功率
     */
    @ApiModelProperty(value = "1月横装功率")
    @Column(name = "horizontal_power_m1")
    private BigDecimal horizontalPowerM1;

    /**
     * 2月横装功率
     */
    @ApiModelProperty(value = "2月横装功率")
    @Column(name = "horizontal_power_m2")
    private BigDecimal horizontalPowerM2;

    /**
     * 3月横装功率
     */
    @ApiModelProperty(value = "3月横装功率")
    @Column(name = "horizontal_power_m3")
    private BigDecimal horizontalPowerM3;

    /**
     * 4月横装功率
     */
    @ApiModelProperty(value = "4月横装功率")
    @Column(name = "horizontal_power_m4")
    private BigDecimal horizontalPowerM4;

    /**
     * 5月横装功率
     */
    @ApiModelProperty(value = "5月横装功率")
    @Column(name = "horizontal_power_m5")
    private BigDecimal horizontalPowerM5;

    /**
     * 6月横装功率
     */
    @ApiModelProperty(value = "6月横装功率")
    @Column(name = "horizontal_power_m6")
    private BigDecimal horizontalPowerM6;

    /**
     * 7月横装功率
     */
    @ApiModelProperty(value = "7月横装功率")
    @Column(name = "horizontal_power_m7")
    private BigDecimal horizontalPowerM7;

    /**
     * 8月横装功率
     */
    @ApiModelProperty(value = "8月横装功率")
    @Column(name = "horizontal_power_m8")
    private BigDecimal horizontalPowerM8;

    /**
     * 9月横装功率
     */
    @ApiModelProperty(value = "9月横装功率")
    @Column(name = "horizontal_power_m9")
    private BigDecimal horizontalPowerM9;

    /**
     * 10月横装功率
     */
    @ApiModelProperty(value = "10月横装功率")
    @Column(name = "horizontal_power_m10")
    private BigDecimal horizontalPowerM10;

    /**
     * 11月横装功率
     */
    @ApiModelProperty(value = "11月横装功率")
    @Column(name = "horizontal_power_m11")
    private BigDecimal horizontalPowerM11;

    /**
     * 12月横装功率
     */
    @ApiModelProperty(value = "12月横装功率")
    @Column(name = "horizontal_power_m12")
    private BigDecimal horizontalPowerM12;

    /**
     * 1月竖装投产比
     */
    @ApiModelProperty(value = "1月竖装投产比")
    @Column(name = "vertical_percent_m1")
    private BigDecimal verticalPercentM1;

    /**
     * 2月竖装投产比
     */
    @ApiModelProperty(value = "2月竖装投产比")
    @Column(name = "vertical_percent_m2")
    private BigDecimal verticalPercentM2;

    /**
     * 3月竖装投产比
     */
    @ApiModelProperty(value = "3月竖装投产比")
    @Column(name = "vertical_percent_m3")
    private BigDecimal verticalPercentM3;

    /**
     * 4月竖装投产比
     */
    @ApiModelProperty(value = "4月竖装投产比")
    @Column(name = "vertical_percent_m4")
    private BigDecimal verticalPercentM4;

    /**
     * 5月竖装投产比
     */
    @ApiModelProperty(value = "5月竖装投产比")
    @Column(name = "vertical_percent_m5")
    private BigDecimal verticalPercentM5;

    /**
     * 6月竖装投产比
     */
    @ApiModelProperty(value = "6月竖装投产比")
    @Column(name = "vertical_percent_m6")
    private BigDecimal verticalPercentM6;

    /**
     * 7月竖装投产比
     */
    @ApiModelProperty(value = "7月竖装投产比")
    @Column(name = "vertical_percent_m7")
    private BigDecimal verticalPercentM7;

    /**
     * 8月竖装投产比
     */
    @ApiModelProperty(value = "8月竖装投产比")
    @Column(name = "vertical_percent_m8")
    private BigDecimal verticalPercentM8;

    /**
     * 9月竖装投产比
     */
    @ApiModelProperty(value = "9月竖装投产比")
    @Column(name = "vertical_percent_m9")
    private BigDecimal verticalPercentM9;

    /**
     * 10月竖装投产比
     */
    @ApiModelProperty(value = "10月竖装投产比")
    @Column(name = "vertical_percent_m10")
    private BigDecimal verticalPercentM10;

    /**
     * 11月竖装投产比
     */
    @ApiModelProperty(value = "11月竖装投产比")
    @Column(name = "vertical_percent_m11")
    private BigDecimal verticalPercentM11;

    /**
     * 12月竖装投产比
     */
    @ApiModelProperty(value = "12月竖装投产比")
    @Column(name = "vertical_percent_m12")
    private BigDecimal verticalPercentM12;

    /**
     * 1月横装投产比
     */
    @ApiModelProperty(value = "1月横装投产比")
    @Column(name = "horizontal_percent_m1")
    private BigDecimal horizontalPercentM1;

    /**
     * 2月横装投产比
     */
    @ApiModelProperty(value = "2月横装投产比")
    @Column(name = "horizontal_percent_m2")
    private BigDecimal horizontalPercentM2;

    /**
     * 3月横装投产比
     */
    @ApiModelProperty(value = "3月横装投产比")
    @Column(name = "horizontal_percent_m3")
    private BigDecimal horizontalPercentM3;

    /**
     * 4月横装投产比
     */
    @ApiModelProperty(value = "4月横装投产比")
    @Column(name = "horizontal_percent_m4")
    private BigDecimal horizontalPercentM4;

    /**
     * 5月横装投产比
     */
    @ApiModelProperty(value = "5月横装投产比")
    @Column(name = "horizontal_percent_m5")
    private BigDecimal horizontalPercentM5;

    /**
     * 6月横装投产比
     */
    @ApiModelProperty(value = "6月横装投产比")
    @Column(name = "horizontal_percent_m6")
    private BigDecimal horizontalPercentM6;

    /**
     * 7月横装投产比
     */
    @ApiModelProperty(value = "7月横装投产比")
    @Column(name = "horizontal_percent_m7")
    private BigDecimal horizontalPercentM7;

    /**
     * 8月横装投产比
     */
    @ApiModelProperty(value = "8月横装投产比")
    @Column(name = "horizontal_percent_m8")
    private BigDecimal horizontalPercentM8;

    /**
     * 9月横装投产比
     */
    @ApiModelProperty(value = "9月横装投产比")
    @Column(name = "horizontal_percent_m9")
    private BigDecimal horizontalPercentM9;

    /**
     * 10月横装投产比
     */
    @ApiModelProperty(value = "10月横装投产比")
    @Column(name = "horizontal_percent_m10")
    private BigDecimal horizontalPercentM10;

    /**
     * 11月横装投产比
     */
    @ApiModelProperty(value = "11月横装投产比")
    @Column(name = "horizontal_percent_m11")
    private BigDecimal horizontalPercentM11;

    /**
     * 12月横装投产比
     */
    @ApiModelProperty(value = "12月横装投产比")
    @Column(name = "horizontal_percent_m12")
    private BigDecimal horizontalPercentM12;


}
