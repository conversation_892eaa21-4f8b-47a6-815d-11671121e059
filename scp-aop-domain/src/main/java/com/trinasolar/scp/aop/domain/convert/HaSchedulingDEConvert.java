package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.HaScheduling;
import com.trinasolar.scp.aop.domain.dto.HaSchedulingDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * ha产出表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-31 20:07:18
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HaSchedulingDEConvert extends BaseDEConvert<HaSchedulingDTO, HaScheduling> {

    HaSchedulingDEConvert INSTANCE = Mappers.getMapper(HaSchedulingDEConvert.class);

}
