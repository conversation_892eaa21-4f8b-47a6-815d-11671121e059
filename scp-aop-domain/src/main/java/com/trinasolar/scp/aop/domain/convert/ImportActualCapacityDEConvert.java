package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculateActualCapacity;
import com.trinasolar.scp.aop.domain.entity.ImportActualCapacity;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 计算后实际或排产产能表 实体与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:55
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ImportActualCapacityDEConvert extends BaseDEConvert<ImportActualCapacity, CalculateActualCapacity> {

    ImportActualCapacityDEConvert INSTANCE = Mappers.getMapper(ImportActualCapacityDEConvert.class);

}
