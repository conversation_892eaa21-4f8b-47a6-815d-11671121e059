package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 计算数据调整记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:25:24
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_record")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_record SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_record SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateRecord extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "table_name")
    private String tableName;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Column(name = "primary_key")
    private String primaryKey;

    /**
     * 列名
     */
    @ApiModelProperty(value = "列名")
    @Column(name = "column_name")
    private String columnName;

    /**
     * 原数据
     */
    @ApiModelProperty(value = "原数据")
    @Column(name = "origin_data")
    private String originData;

    /**
     * 新数据
     */
    @ApiModelProperty(value = "新数据")
    @Column(name = "new_data")
    private String newData;


}
