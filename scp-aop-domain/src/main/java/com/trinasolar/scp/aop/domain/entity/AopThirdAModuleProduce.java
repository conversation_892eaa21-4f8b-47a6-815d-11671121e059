/**
 * <AUTHOR>
 * @date 2024-11-01 9:47
 */
package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2024/11/1 9:47
 */
@Entity
@ToString
@Data
@Table(name = "aop_third_a_module_produce")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_third_a_module_produce SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_third_a_module_produce SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AopThirdAModuleProduce extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内海外
     */
    @ApiModelProperty(value = "国内海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;
    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private String month;

    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    @ApiModelProperty(value = "平均功率")
    @Column(name = "average_power")
    private String averagePower;

    /**
     * 排产量(WPC)
     */
    @ApiModelProperty(value = "排产量(WPC)")
    @Column(name = "production_output_wpc")
    private String productionOutputWpc;


    /**
     * 排产量(MW)
     */
    @ApiModelProperty(value = "排产量(MW)")
    @Column(name = "production_output_mw")
    private String productionOutputMw;

}
