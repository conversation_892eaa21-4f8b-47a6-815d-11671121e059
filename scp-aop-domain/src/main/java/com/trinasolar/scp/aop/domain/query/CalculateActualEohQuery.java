package com.trinasolar.scp.aop.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 计算实际EOH表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:14:21
 */
@Data
@ApiModel(value = "CalculateActualEoh查询条件", description = "查询条件")
@Accessors(chain = true)
public class CalculateActualEohQuery extends PageDTO implements Serializable {
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
