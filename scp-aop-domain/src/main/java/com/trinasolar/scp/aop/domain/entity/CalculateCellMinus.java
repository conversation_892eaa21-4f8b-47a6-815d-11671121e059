package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池A-计算
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_cell_a_minus")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_cell_a_minus SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_cell_a_minus SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateCellMinus extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;


    @ApiModelProperty(value = "调整版本")
    @Column(name = "data_version")
    private String dataVersion;
    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 生产基地
     */
    @ApiModelProperty(value = "生产基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;
    
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;
    /**
     * 供应方
     */
    @ApiModelProperty(value = "供应方")
    @Column(name = "supplier")
    private String supplier;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;
    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    @Column(name = "fragment_type")
    private String fragmentType;
    /**
     * 电池类型
     */
    @ApiModelProperty(value = "电池类型")
    @Column(name = "cell_type")
    private String cellType;

    /**
     * 产品来源：自产/外购
     */
    @ApiModelProperty(value = "产品来源：自产/外购")
    @Column(name = "product_from")
    private String productFrom;


    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 一季度
     */
    @ApiModelProperty(value = "一季度")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 二季度
     */
    @ApiModelProperty(value = "二季度")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 三季度
     */
    @ApiModelProperty(value = "三季度")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 四季度
     */
    @ApiModelProperty(value = "四季度")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    @ApiModelProperty(value = "是否生效")
    @Column(name = "status")
    private String status;

}
