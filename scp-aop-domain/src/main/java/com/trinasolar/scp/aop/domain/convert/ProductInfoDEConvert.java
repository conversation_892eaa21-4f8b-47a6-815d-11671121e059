package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductInfoDTO;
import com.trinasolar.scp.aop.domain.dto.ProductInfoExportExcelDTO;
import com.trinasolar.scp.aop.domain.entity.ProductInfo;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * VIEW DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 14:10:20
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductInfoDEConvert extends BaseDEConvert<ProductInfoDTO, ProductInfo> {

    ProductInfoDEConvert INSTANCE = Mappers.getMapper(ProductInfoDEConvert.class);

    List<ProductInfoExportExcelDTO> toProductInfoExcel(List<ProductInfoDTO> content);
}
