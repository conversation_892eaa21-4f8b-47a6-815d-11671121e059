package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateVerifyInventoryDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateVerifyInventory;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 硅片验证库存 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:16:17
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateVerifyInventoryDEConvert extends BaseDEConvert<CalculateVerifyInventoryDTO, CalculateVerifyInventory> {

    CalculateVerifyInventoryDEConvert INSTANCE = Mappers.getMapper(CalculateVerifyInventoryDEConvert.class);

}
