package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 组件良率行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:58
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_module_good")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_module_good SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_module_good SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigModuleGood extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 组ID：非Q1/工原损/组件良率/投产比//分档损失/Powerloss（含良率）维度
     */
    @ApiModelProperty(value = "组ID：非Q1/工原损/组件良率/投产比//分档损失/Powerloss（含良率）维度")
    @Column(name = "group_id")
    private String groupId;

    /**
     * 满产/排产
     */
    @ApiModelProperty(value = "满产/排产")
    @Column(name = "parameter_type")
    private String parameterType;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 晶体型号;单晶多晶
     */
    @ApiModelProperty(value = "晶体型号;单晶多晶")
    @Column(name = "crystal_spec")
    private String crystalSpec;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 指标类型：非Q1/工原损/组件良率/投产比//分档损失/Powerloss（含良率）
     */
    @ApiModelProperty(value = "指标类型：非Q1/工原损/组件良率/投产比//分档损失/Powerloss（含良率）")
    @Column(name = "index_type")
    private String indexType;

    /**
     * 1月数
     */
    @ApiModelProperty(value = "1月数")
    @Column(name = "quantity_m1")
    private BigDecimal quantityM1;

    /**
     * 2月数
     */
    @ApiModelProperty(value = "2月数")
    @Column(name = "quantity_m2")
    private BigDecimal quantityM2;

    /**
     * 3月数
     */
    @ApiModelProperty(value = "3月数")
    @Column(name = "quantity_m3")
    private BigDecimal quantityM3;

    /**
     * 4月数
     */
    @ApiModelProperty(value = "4月数")
    @Column(name = "quantity_m4")
    private BigDecimal quantityM4;

    /**
     * 5月数
     */
    @ApiModelProperty(value = "5月数")
    @Column(name = "quantity_m5")
    private BigDecimal quantityM5;

    /**
     * 6月数
     */
    @ApiModelProperty(value = "6月数")
    @Column(name = "quantity_m6")
    private BigDecimal quantityM6;

    /**
     * 7月数
     */
    @ApiModelProperty(value = "7月数")
    @Column(name = "quantity_m7")
    private BigDecimal quantityM7;

    /**
     * 8月数
     */
    @ApiModelProperty(value = "8月数")
    @Column(name = "quantity_m8")
    private BigDecimal quantityM8;

    /**
     * 9月数
     */
    @ApiModelProperty(value = "9月数")
    @Column(name = "quantity_m9")
    private BigDecimal quantityM9;

    /**
     * 10月数
     */
    @ApiModelProperty(value = "10月数")
    @Column(name = "quantity_m10")
    private BigDecimal quantityM10;

    /**
     * 11月数
     */
    @ApiModelProperty(value = "11月数")
    @Column(name = "quantity_m11")
    private BigDecimal quantityM11;

    /**
     * 12月数
     */
    @ApiModelProperty(value = "12月数")
    @Column(name = "quantity_m12")
    private BigDecimal quantityM12;


}
