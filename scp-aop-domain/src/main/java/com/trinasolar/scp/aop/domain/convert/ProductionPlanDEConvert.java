package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductionPlanDTO;
import com.trinasolar.scp.aop.domain.entity.ProductionPlan;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17 11:42
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductionPlanDEConvert extends BaseDEConvert<ProductionPlan, ProductionPlanDTO> {
    ProductionPlanDEConvert INSTANCE = Mappers.getMapper(ProductionPlanDEConvert.class);

    /**
     * List<ProductionPlanDTO> 转为 List<ProductionPlan>
     *
     * @param productionPlanDTOs 排产计划DTO集合
     * @return 排产计划实体集合
     */
    List<ProductionPlan> toProductionPlans(List<ProductionPlanDTO> productionPlanDTOs);

    /**
     * List<ProductionPlan> 转为 List<ProductionPlanDTO>
     *
     * @param productionPlans 排产计划DTO集合
     * @return 排产计划实体集合
     */
    List<ProductionPlanDTO> toProductionPlanDTOs(List<ProductionPlan> productionPlans);
}
