package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.PurchaseDemand;
import com.trinasolar.scp.aop.domain.dto.PurchaseDemandDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 通合电池、外购、OEM基础表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-08 14:58:25
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PurchaseDemandDEConvert extends BaseDEConvert<PurchaseDemandDTO, PurchaseDemand> {

    PurchaseDemandDEConvert INSTANCE = Mappers.getMapper(PurchaseDemandDEConvert.class);

}
