package com.trinasolar.scp.aop.domain.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.util.StringUtils;
import com.trinasolar.scp.aop.domain.enums.ExcelDefaultValue;
import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * IE原始产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-25 10:03:46
 */
@Entity
@ToString
@Data
@Table(name = "aop_origin_capacity_ie")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_origin_capacity_ie SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_origin_capacity_ie SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false,of = {"countryFlag","year","productSeries","productGroup","unit","workshop"})
@AllArgsConstructor
@NoArgsConstructor
public class OriginCapacityIe extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    @ExcelProperty(value = {"国内/海外"}, index = 0)
    private String countryFlag;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    @ExcelProperty(value = {"数据版本号"}, index = 1)
    private String dataVersion;

    /**
     * 最新数据标记，Y：是，N：否
     */
    @ApiModelProperty(value = "最新数据标记，Y：是，N：否")
    @Column(name = "latest_flag")
    @ExcelProperty(value = {"数据版本号"}, index = 2)
    private String latestFlag;

    /**
     * 业务场景：RMA/OTHER
     */
    @ApiModelProperty(value = "业务场景：RMA/OTHER")
    @Column(name = "business_type")
    @ExcelProperty(value = {"业务场景"}, index = 3)
    private String businessType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    @ExcelProperty(value = {"产品来源"}, index = 4)
    private String productFrom;

    /**
     * 产品类型：电池/组件自产/硅片/组件兼容
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片/组件兼容")
    @Column(name = "product_type")
    @ExcelProperty(value = {"产品类型"}, index = 5)
    private String productType;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    @Column(name = "data_from")
    @ExcelProperty(value = {"数据来源"}, index = 6)
    private String dataFrom;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    @ExcelProperty(value = {"车间"}, index = 7)
    private String workshop;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    @ExcelProperty(value = {"产品系列"}, index = 8)
    private String productSeries;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "分片方式")
    @Column(name = "fragment_type")
    @ExcelProperty(value = {"分片方式"}, index = 9)
    private String fragmentType;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    @ExcelProperty(value = {"产品族"}, index = 10)
    private String productGroup;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    @ExcelProperty(value = {"单位"}, index = 11)
    private String unit;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    @ExcelProperty(value = {"年份"}, index = 12)
    private Integer year;

    /**
     * ie是否确认
     */
    @ApiModelProperty(value = "ie是否确认")
    @Column(name = "is_ie_confirm")
    private String isIEConfirm;

    /**
     * 1月产能
     */
    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_quantity")
    @ExcelProperty(value = {"1月产能"}, index = 13)
    private BigDecimal m1Quantity;

    /**
     * 2月产能
     */
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_quantity")
    @ExcelProperty(value = {"2月产能"}, index = 14)
    private BigDecimal m2Quantity;

    /**
     * 3月产能
     */
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_quantity")
    @ExcelProperty(value = {"3月产能"}, index = 15)
    private BigDecimal m3Quantity;

    /**
     * 4月产能
     */
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_quantity")
    @ExcelProperty(value = {"4月产能"}, index = 16)
    private BigDecimal m4Quantity;

    /**
     * 5月产能
     */
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_quantity")
    @ExcelProperty(value = {"5月产能"}, index = 17)
    private BigDecimal m5Quantity;

    /**
     * 6月产能
     */
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_quantity")
    @ExcelProperty(value = {"6月产能"}, index = 18)
    private BigDecimal m6Quantity;

    /**
     * 7月产能
     */
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_quantity")
    @ExcelProperty(value = {"7月产能"}, index = 19)
    private BigDecimal m7Quantity;

    /**
     * 8月产能
     */
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_quantity")
    @ExcelProperty(value = {"8月产能"}, index = 20)
    private BigDecimal m8Quantity;

    /**
     * 9月产能
     */
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_quantity")
    @ExcelProperty(value = {"9月产能"}, index = 21)
    private BigDecimal m9Quantity;

    /**
     * 10月产能
     */
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_quantity")
    @ExcelProperty(value = {"10月产能"}, index = 22)
    private BigDecimal m10Quantity;

    /**
     * 11月产能
     */
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_quantity")
    @ExcelProperty(value = {"11月产能"}, index = 23)
    private BigDecimal m11Quantity;

    /**
     * 12月产能
     */
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_quantity")
    @ExcelProperty(value = {"12月产能"}, index = 24)
    private BigDecimal m12Quantity;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    @Column(name = "remark")
    @ExcelProperty(value = {"备注"}, index = 25)
    private String remark;

    /**
     * 数据类型：满产/公布
     */
    @ApiModelProperty(value = "数据类型：满产/公布")
    @Column(name = "data_type")
    @ExcelProperty(value = {"数据类型"}, index = 26)
    private String dataType;

    @Transient
    private String dataTypeName;

    @Transient
    private String productTypeName;

    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_wquantity")
    private BigDecimal m1Wquantity;
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_wquantity")
    private BigDecimal m2Wquantity;
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_wquantity")
    private BigDecimal m3Wquantity;
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_wquantity")
    private BigDecimal m4Wquantity;
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_wquantity")
    private BigDecimal m5Wquantity;
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_wquantity")
    private BigDecimal m6Wquantity;
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_wquantity")
    private BigDecimal m7Wquantity;
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_wquantity")
    private BigDecimal m8Wquantity;
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_wquantity")
    private BigDecimal m9Wquantity;
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_wquantity")
    private BigDecimal m10Wquantity;
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_wquantity")
    private BigDecimal m11Wquantity;
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_wquantity")
    private BigDecimal m12Wquantity;

    /**
     * 设置实体值
     *
     * @param originCapacityIe
     */
    public void setEntity(OriginCapacityIe originCapacityIe) {
        originCapacityIe.setCountryFlag(originCapacityIe.getCountryFlag());
        originCapacityIe.setLatestFlag(originCapacityIe.getLatestFlag());
        originCapacityIe.setBusinessType(originCapacityIe.getBusinessType());
        originCapacityIe.setProductFrom(originCapacityIe.getProductFrom());
        originCapacityIe.setProductType(originCapacityIe.getProductType());
        originCapacityIe.setDataFrom(originCapacityIe.getDataFrom());
        originCapacityIe.setDataType(originCapacityIe.getDataType());
        originCapacityIe.setWorkshop(originCapacityIe.getWorkshop());
        originCapacityIe.setProductSeries(originCapacityIe.getProductSeries());
        originCapacityIe.setProductGroup(originCapacityIe.getProductGroup());
        originCapacityIe.setUnit(originCapacityIe.getUnit());
        originCapacityIe.setYear(originCapacityIe.getYear());

        originCapacityIe.setM1Quantity(originCapacityIe.getM1Quantity());
        originCapacityIe.setM2Quantity(originCapacityIe.getM2Quantity());
        originCapacityIe.setM3Quantity(originCapacityIe.getM3Quantity());
        originCapacityIe.setM4Quantity(originCapacityIe.getM4Quantity());
        originCapacityIe.setM5Quantity(originCapacityIe.getM5Quantity());
        originCapacityIe.setM6Quantity(originCapacityIe.getM6Quantity());
        originCapacityIe.setM7Quantity(originCapacityIe.getM7Quantity());
        originCapacityIe.setM8Quantity(originCapacityIe.getM8Quantity());
        originCapacityIe.setM9Quantity(originCapacityIe.getM9Quantity());
        originCapacityIe.setM10Quantity(originCapacityIe.getM10Quantity());
        originCapacityIe.setM11Quantity(originCapacityIe.getM11Quantity());
        originCapacityIe.setM12Quantity(originCapacityIe.getM12Quantity());
    }

    /**
     * 设置初始值
     *
     * @param originCapacityIe
     */
    public String setInitialValue(OriginCapacityIe originCapacityIe) {
        String errorlog = "";
        if (
                StringUtils.isBlank(originCapacityIe.getCountryFlag()) || StringUtils.isBlank(originCapacityIe.getProductSeries()) ||
                        StringUtils.isBlank(originCapacityIe.getUnit()) || ObjectUtils.isEmpty(originCapacityIe.getYear()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM1Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM2Quantity()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM3Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM4Quantity()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM5Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM6Quantity()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM7Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM8Quantity()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM9Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM10Quantity()) ||
                        ObjectUtils.isEmpty(originCapacityIe.getM11Quantity()) || ObjectUtils.isEmpty(originCapacityIe.getM12Quantity())
        ) {
            return errorlog + "数据异常，请检查是否有空字段";
        }
        setCountryFlag(originCapacityIe.getCountryFlag());
        setProductType(originCapacityIe.getProductType());
        setProductSeries(originCapacityIe.getProductSeries());
        setProductGroup(originCapacityIe.getProductGroup());
        setUnit(originCapacityIe.getUnit());
        setYear(originCapacityIe.getYear());

        setM1Quantity(originCapacityIe.getM1Quantity());
        setM2Quantity(originCapacityIe.getM2Quantity());
        setM3Quantity(originCapacityIe.getM3Quantity());
        setM4Quantity(originCapacityIe.getM4Quantity());
        setM5Quantity(originCapacityIe.getM5Quantity());
        setM6Quantity(originCapacityIe.getM6Quantity());
        setM7Quantity(originCapacityIe.getM7Quantity());
        setM8Quantity(originCapacityIe.getM8Quantity());
        setM9Quantity(originCapacityIe.getM9Quantity());
        setM10Quantity(originCapacityIe.getM10Quantity());
        setM11Quantity(originCapacityIe.getM11Quantity());
        setM12Quantity(originCapacityIe.getM12Quantity());
        return errorlog;
    }

    /**
     * 设置LovValue值
     *
     * @param lovMap
     */
    public String setLovValue(Map<String, LovLineDTO> lovMap, String dataType, String productType) {
        //国内/海外
        if (StringUtils.isNotBlank(getCountryFlag())) {
            if (lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()) == null) {
                return "国内/海外(CountryFlag) 值错误";
            }
            setCountryFlag(lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()).getLovValue());
        }

        //产品类型：电池/组件自产/硅片/组件兼容
        if (ObjectUtils.isEmpty(lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + productType))) {
            return "产品类型(ProductType) 值错误";
        }
        setProductType(lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + productType).getLovValue());

        //数据类型
        String code = ExcelDefaultValue.getCode(dataType);
        if (StringUtils.isNotBlank(code)) {
            setDataType(code);
        } else {
            return "数据类型(DataType) 值错误";
        }


        if (StringUtils.isNotBlank(getUnit())) {
            //单位
            if (lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit()) == null) {
                return "|" + "单位(Unit) 值错误";
            }
            setUnit(lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit()).getLovValue());
        }



        /*//车间
        if (lovMap.get(LovHeaderCodeConstant.WORK_SHOP + getWorkshop()) == null) {
            return "车间(WorkShop) 值错误";
        }
        //产品系列
        if (lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_SERIES + getProductSeries()) == null) {
            return "产品系列(productSeries) 值错误";
        }
        //产品族
        if (StringUtils.isNotBlank(getProductGroup())) {
            if (lovMap.get(LovHeaderCodeConstant.AOP_FAMILY_CODE + getProductGroup()) == null) {
                return "产品族(productGroup) 值错误";
            }
        }*/
        return null;
    }
}
