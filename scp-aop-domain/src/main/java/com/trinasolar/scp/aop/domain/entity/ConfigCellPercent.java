package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池占比表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:04
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_cell_percent")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_cell_percent SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_cell_percent SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ConfigCellPercent extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "is_oversea")
    private String isOversea;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private String year;

    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    @Column(name = "batch_no")
    private String batchNo;

    /**
     * 产品来源：自产/外购
     */
    @ApiModelProperty(value = "产品来源：自产/外购")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 1月占比
     */
    @ApiModelProperty(value = "1月占比")
    @Column(name = "percent_m1")
    private BigDecimal percentM1;

    /**
     * 2月占比
     */
    @ApiModelProperty(value = "2月占比")
    @Column(name = "percent_m2")
    private BigDecimal percentM2;

    /**
     * 3月占比
     */
    @ApiModelProperty(value = "3月占比")
    @Column(name = "percent_m3")
    private BigDecimal percentM3;

    /**
     * 4月占比
     */
    @ApiModelProperty(value = "4月占比")
    @Column(name = "percent_m4")
    private BigDecimal percentM4;

    /**
     * 5月占比
     */
    @ApiModelProperty(value = "5月占比")
    @Column(name = "percent_m5")
    private BigDecimal percentM5;

    /**
     * 6月占比
     */
    @ApiModelProperty(value = "6月占比")
    @Column(name = "percent_m6")
    private BigDecimal percentM6;

    /**
     * 7月占比
     */
    @ApiModelProperty(value = "7月占比")
    @Column(name = "percent_m7")
    private BigDecimal percentM7;

    /**
     * 8月占比
     */
    @ApiModelProperty(value = "8月占比")
    @Column(name = "percent_m8")
    private BigDecimal percentM8;

    /**
     * 9月占比
     */
    @ApiModelProperty(value = "9月占比")
    @Column(name = "percent_m9")
    private BigDecimal percentM9;

    /**
     * 10月占比
     */
    @ApiModelProperty(value = "10月占比")
    @Column(name = "percent_m10")
    private BigDecimal percentM10;

    /**
     * 11月占比
     */
    @ApiModelProperty(value = "11月占比")
    @Column(name = "percent_m11")
    private BigDecimal percentM11;

    /**
     * 12月占比
     */
    @ApiModelProperty(value = "12月占比")
    @Column(name = "percent_m12")
    private BigDecimal percentM12;


}
