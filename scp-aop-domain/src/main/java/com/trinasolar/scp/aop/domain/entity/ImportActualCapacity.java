package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 导入后实际或排产产能表
 */
@Entity
@ToString
@Data
@Table(name = "aop_import_actual_capacity")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_import_actual_capacity SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_import_actual_capacity SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImportActualCapacity extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 计算版本号
     */
    @ApiModelProperty(value = "计算版本号")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 产品大类
     */
    @ApiModelProperty(value = "产品大类")
    @Column(name = "product_type")
    private String productType;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 1月产能
     */
    @ApiModelProperty(value = "1月产能")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月产能
     */
    @ApiModelProperty(value = "2月产能")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月产能
     */
    @ApiModelProperty(value = "3月产能")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月产能
     */
    @ApiModelProperty(value = "4月产能")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月产能
     */
    @ApiModelProperty(value = "5月产能")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月产能
     */
    @ApiModelProperty(value = "6月产能")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月产能
     */
    @ApiModelProperty(value = "7月产能")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月产能
     */
    @ApiModelProperty(value = "8月产能")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月产能
     */
    @ApiModelProperty(value = "9月产能")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月产能
     */
    @ApiModelProperty(value = "10月产能")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月产能
     */
    @ApiModelProperty(value = "11月产能")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月产能
     */
    @ApiModelProperty(value = "12月产能")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 实际月份
     */
    @ApiModelProperty(value = "实际月份")
    @Column(name = "actual_month")
    private Integer actualMonth;

    /**
     * 排产月份
     */
    @ApiModelProperty(value = "排产月份")
    @Column(name = "plan_month")
    private Integer planMonth;

    @ApiModelProperty(value = "供应类型")
    @Column(name = "product_from")
    private String productFrom;

    public void initZero(){
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }
}
