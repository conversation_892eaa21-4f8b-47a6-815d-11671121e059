package com.trinasolar.scp.aop.domain.convert;


import com.trinasolar.scp.aop.domain.dto.PercentGlassDTO;
import com.trinasolar.scp.aop.domain.dto.PreSaleDTO;
import com.trinasolar.scp.aop.domain.entity.OriginSale;
import com.trinasolar.scp.aop.domain.entity.PercentGlass;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 单双波配比
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PercentGlassDEConvert  extends BaseDEConvert<PercentGlassDTO, PercentGlass> {

    PercentGlassDEConvert INSTANCE = Mappers.getMapper(PercentGlassDEConvert.class);
}
