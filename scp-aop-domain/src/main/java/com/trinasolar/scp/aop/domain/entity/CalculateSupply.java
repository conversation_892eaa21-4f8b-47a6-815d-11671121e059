package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 计算后理论供应目标
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:27:06
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_supply")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_supply SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_supply SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateSupply extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 业务场景：RMA/OTHER
     */
    @ApiModelProperty(value = "业务场景：RMA/OTHER")
    @Column(name = "business_type")
    private String businessType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品类型：电池/组件/硅片/RMA
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片/RMA")
    @Column(name = "product_type")
    private String productType;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 区域细分
     */
    @ApiModelProperty(value = "区域细分")
    @Column(name = "sub_area")
    private String subArea;
    /**
     * 计算方式：正算/反算
     */
    @ApiModelProperty(value = "计算方式：正算/反算")
    @Column(name = "calculate_type")
    private String calculateType;
    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月理论供应目标
     */
    @ApiModelProperty(value = "1月理论供应目标")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月理论供应目标
     */
    @ApiModelProperty(value = "2月理论供应目标")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月理论供应目标
     */
    @ApiModelProperty(value = "3月理论供应目标")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月理论供应目标
     */
    @ApiModelProperty(value = "4月理论供应目标")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月理论供应目标
     */
    @ApiModelProperty(value = "5月理论供应目标")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月理论供应目标
     */
    @ApiModelProperty(value = "6月理论供应目标")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月理论供应目标
     */
    @ApiModelProperty(value = "7月理论供应目标")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月理论供应目标
     */
    @ApiModelProperty(value = "8月理论供应目标")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月理论供应目标
     */
    @ApiModelProperty(value = "9月理论供应目标")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月理论供应目标
     */
    @ApiModelProperty(value = "10月理论供应目标")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月理论供应目标
     */
    @ApiModelProperty(value = "11月理论供应目标")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月理论供应目标
     */
    @ApiModelProperty(value = "12月理论供应目标")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 原ID，调整用
     */
    @ApiModelProperty(value = "原ID，调整用")
    @Column(name = "old_id")
    private Long oldId;
    /**
     * 一季度理论供应目标
     */
    @ApiModelProperty(value = "一季度理论供应目标")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;
    /**
     * 二季度理论供应目标
     */
    @ApiModelProperty(value = "二季度理论供应目标")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;
    /**
     * 三季度理论供应目标
     */
    @ApiModelProperty(value = "三季度理论供应目标")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;
    /**
     * 四季度理论供应目标
     */
    @ApiModelProperty(value = "四季度理论供应目标")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    @Transient
    private String cellModel;

    @Transient
    private Integer mfgPriority;
    @Transient
    private String cellShard;
    @Transient
    private String shardPriority;

    public void round() {
        if (ObjectUtils.isNotEmpty(this.getM1Quantity())) {
            this.setM1Quantity(this.getM1Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM2Quantity())) {
            this.setM2Quantity(this.getM2Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM3Quantity())) {
            this.setM3Quantity(this.getM3Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM4Quantity())) {
            this.setM4Quantity(this.getM4Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM5Quantity())) {
            this.setM5Quantity(this.getM5Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM6Quantity())) {
            this.setM6Quantity(this.getM6Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM7Quantity())) {
            this.setM7Quantity(this.getM7Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM8Quantity())) {
            this.setM8Quantity(this.getM8Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM9Quantity())) {
            this.setM9Quantity(this.getM9Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM10Quantity())) {
            this.setM10Quantity(this.getM10Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM11Quantity())) {
            this.setM11Quantity(this.getM11Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM12Quantity())) {
            this.setM12Quantity(this.getM12Quantity().setScale(6, RoundingMode.HALF_UP));
        }
    }

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
        this.setQ1Quantity(BigDecimal.ZERO);
        this.setQ2Quantity(BigDecimal.ZERO);
        this.setQ3Quantity(BigDecimal.ZERO);
        this.setQ4Quantity(BigDecimal.ZERO);
    }

    public void setNegativeZero() {
        if (this.getM1Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (this.getM2Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (this.getM3Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (this.getM4Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (this.getM5Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (this.getM6Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (this.getM7Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (this.getM8Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (this.getM9Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (this.getM10Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (this.getM11Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (this.getM12Quantity().compareTo(BigDecimal.ZERO) < 0) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }

    public void initSum() {
        this.setQ1Quantity(this.getM1Quantity().add(this.getM2Quantity()).add(this.getM3Quantity()));
        this.setQ2Quantity(this.getM4Quantity().add(this.getM5Quantity()).add(this.getM6Quantity()));
        this.setQ3Quantity(this.getM7Quantity().add(this.getM8Quantity()).add(this.getM9Quantity()));
        this.setQ4Quantity(this.getM10Quantity().add(this.getM11Quantity()).add(this.getM12Quantity()));
    }
}
