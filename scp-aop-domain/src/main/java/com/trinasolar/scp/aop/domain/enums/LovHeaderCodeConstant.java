package com.trinasolar.scp.aop.domain.enums;

/**
 * <AUTHOR>
 * @date 2022/6/21 16:45
 * @apiNote
 */
public class LovHeaderCodeConstant {
    /**
     * 国内/海外
     */
    public final static String AOP_COUNTRY_FLAG = "AOP_COUNTRY_FLAG";
    /**
     * 最新数据标识
     */
    public final static String AOP_DATA_IDENTIFICATION = "yes_or_no";

    /**
     * 数据来源
     */
    public final static String AOP_SALES_DATA_SOURCE = "AOP_SALES_DATA_SOURCE";

    /**
     * 数据类型
     */
    public final static String AOP_DATA_TYPE = "data_type";

    /**
     * 业务类型
     */
    public final static String AOP_BUSINESS_TYPE = "AOP_BUSINESS_TYPE";

    /**
     * 产品来源
     */
    public final static String AOP_PRODUCT_FROM = "AOP_PRODUCT_FROM";

    /**
     * 产品类型（大类）
     */
    public final static String AOP_PRODUCT_TYPE = "AOP_PRODUCT_TYPE";

    /**
     * 区域
     */
    public final static String AOP_REGION = "Aop_Region";

    /**
     * 区域细分
     */
    public final static String AOP_SUB_REGION = "SubRegion";

    /**
     * 项目地
     */
    public final static String AOP_PROJECT_PLACE = "project_location";

    /**
     * 项目地国家
     */
    public final static String AOP_COUNTRY = "country";

    /**
     * 销售渠道
     */
    public final static String AOP_SALES_CHANNEL = "AOP_SALES_CHANNEL";

    /**
     * 产品系列
     */
    public final static String AOP_PRODUCT_SERIES = "AOP_PRODUCT_SERIES";

    /**
     * 产品族
     */
    public final static String AOP_FAMILY_CODE = "Family_Code";

    /**
     * 组件类型
     */
    public final static String AOP_MODULE_TYPE = "AOP_MODULE_TYPE";

    /**
     * 来源类型
     */
    public final static String AOP_FROM_TYPE = "from_type";

    /**
     * 单位
     */
    public final static String AOP_UOM = "UOM";

    /**
     * 版本类型
     */
    public final static String VERSION_TYPE = "version_type";
    /**
     * 车间
     */
    public final static String WORK_SHOP = "work_shop";

    /**
     * 物料大类
     */
    public final static String AOP_MATERIAL_TYPE = "AOP_MATERIAL_TYPE";
    /**
     * 电池分片方式
     */
    public static final String AOP_CELL_SHARD = "aop_cell_shard";

    /**
     * 电池分片方式
     */
    public static final String AOP_W_AESTHETICS = "aop_w_aesthetics";
    /**
     * 电池产品分类对应电池型号
     */
    public static final String AOP_PRODUCT_CLASSIFY = "aop_product_classify";
    /**
     * 天玑期初库存区域对照lov
     */
    public static final String AOP_BOH_AREA_MAPPING_TJ = "AOP_BOH_AREA_MAPPING_TJ";
    /**
     * 天玑渠道映射表
     */
    public static final String AOP_CHANNEL_MAPPING_TJ = "AOP_CHANNEL_MAPPING_TJ";
    /**
     * (aop)天玑区域映射表
     */
    public static final String AOP_AREA_MAPPING_TJ = "AOP_AREA_MAPPING_TJ";

    public final static String REGION = "Region";

    public final static String AOP_POWER_EFFICIENCY_SUPPLIER = "aps_power_efficiency_supplier";

    public final static String AOP_CELL_SERIES = "aop_cell_series";

    public final static String APS_POWER_CELL_PROD_MAP = "aps_power_cell_prod_map";

    public final static String BASE_PLACE = "base_place";

    public final static String MAIN_GRID = "6A00100100108";

    public final static String APS_POWER_CELL_SERIES_TYPE = "APS_POWER_CELL_SERIES_TYPE";

    public final static String AOP_PRODUCT_SEQUENCE = "AOP_PRODUCT_SEQUENCE";

    public static final String LOV_AOP_PRODUCT_SERIES = "aop_product_series";

    public static final String LOV_AOP_CELL_SERIES = "aop_cell_series";

    public static final String LOV_AOP_WAFER_SERIES = "aop_wafer_series";

    /**
     * 转运方式
     */
    public static final String LOV_AOP_AOP_TRANSPORT = "aop_transport";

    /**
     * 海外A-片分配
     */
    public static final String LOV_AOP_AMINUS_ALLOCATION = "AOP_AMINUS_ALLOCATION";

    /**
     * 横竖装
     */
    public static final String LOV_AOP_INSTALL_TYPE = "6A001066";

    public final static String LOV_AOP_BATTERY_LEVEL = "6A00100100136";
    // 0416 仲华要求更改新的LOV
    public final static String LOV_AOP_BATTERY_LEVEL_NEW = "5A00100100103";

    // 货源账套
    public static final String INVENTORY_ORGANIZATION = "inventory_organization";
}
