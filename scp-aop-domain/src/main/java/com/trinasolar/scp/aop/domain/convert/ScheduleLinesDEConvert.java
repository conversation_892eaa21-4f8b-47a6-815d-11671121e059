package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ScheduleLinesDTO;
import com.trinasolar.scp.aop.domain.entity.ScheduleLines;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * DP实际排产 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-17 12:43:25
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ScheduleLinesDEConvert extends BaseDEConvert<ScheduleLinesDTO, ScheduleLines> {

    ScheduleLinesDEConvert INSTANCE = Mappers.getMapper(ScheduleLinesDEConvert.class);

}
