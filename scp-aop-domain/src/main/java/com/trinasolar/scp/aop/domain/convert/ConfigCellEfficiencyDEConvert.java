package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigCellEfficiencyDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigCellEfficiency;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 制造参数管理-电池效率 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 09:17:06
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigCellEfficiencyDEConvert extends BaseDEConvert<ConfigCellEfficiencyDTO, ConfigCellEfficiency> {

    ConfigCellEfficiencyDEConvert INSTANCE = Mappers.getMapper(ConfigCellEfficiencyDEConvert.class);

}
