package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.OriginCapacityIeDTO;
import com.trinasolar.scp.aop.domain.dto.OriginCapacityIeImportExcelDTO;
import com.trinasolar.scp.aop.domain.entity.OriginCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @className OriginalCapacityIeConvert
 * @description 转换
 * @date 2022-06-10
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginCapacityIeDEConvert extends BaseDEConvert<OriginCapacityIeDTO, OriginCapacityIe> {
    OriginCapacityIeDEConvert INSTANCE = Mappers.getMapper(OriginCapacityIeDEConvert.class);

    List<OriginCapacityIe> toCapacityIe(List<OriginCapacityIeImportExcelDTO> originCapacityIeImportExcelDTOList);
}
