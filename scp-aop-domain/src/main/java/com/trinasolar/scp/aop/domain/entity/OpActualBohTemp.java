package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 其它实际数据
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:17:36
 */
@Entity
@ToString
@Data
@Table(name = "op_actual_boh_temp")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_actual_other SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_actual_other SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class OpActualBohTemp extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 电池/硅片/组件
     */
    @ApiModelProperty(value = "电池/硅片/组件")
    @Column(name = "item_type")
    private String itemType;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @Column(name = "item")
    private String item;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 月份
     */
    @ApiModelProperty(value = "月份")
    @Column(name = "month")
    private Integer month;

    /**
     * 实际产出量
     */
    @ApiModelProperty(value = "实际产出量")
    @Column(name = "quantity")
    private BigDecimal quantity;

    /**
     * prebling标识
     */
    @ApiModelProperty(value = "prebling标识")
    @Column(name = "flag")
    private String flag;

    @Transient
    private String cellShard;
}
