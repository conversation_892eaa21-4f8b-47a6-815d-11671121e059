package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池备货调整记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-13 08:53:09
 */
@Entity
@ToString
@Data
@Table(name = "aop_cell_stock_up_record")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_cell_stock_up_record SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_cell_stock_up_record SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CellStockUpRecord extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;
    /**
     * 计算版本号
     */
    @ApiModelProperty(value = "计算版本号")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 组件调整标识;自产/OEM
     */
    @ApiModelProperty(value = "组件调整标识;自产/OEM")
    @Column(name = "adjust_type")
    private String adjustType;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 组件产出/OEM的id
     */
    @ApiModelProperty(value = "组件产出/OEM的id")
    @Column(name = "adjust_id")
    private Long adjustId;

    /**
     * 组件产品系列
     */
    @ApiModelProperty(value = "组件产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_module")
    private String cellModule;

    /**
     * 调整的字段
     */
    @ApiModelProperty(value = "调整的字段")
    @Column(name = "adjust_field")
    private String adjustField;
    /**
     * 总消耗备货量
     */
    @ApiModelProperty(value = "总消耗备货量")
    @Column(name = "adjust_quantity")
    private BigDecimal adjustQuantity;

    /**
     * 1月
     */
    @ApiModelProperty(value = "1月")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月
     */
    @ApiModelProperty(value = "2月")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月
     */
    @ApiModelProperty(value = "3月")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月
     */
    @ApiModelProperty(value = "4月")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月
     */
    @ApiModelProperty(value = "5月")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月
     */
    @ApiModelProperty(value = "6月")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月
     */
    @ApiModelProperty(value = "7月")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月
     */
    @ApiModelProperty(value = "8月")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月
     */
    @ApiModelProperty(value = "9月")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月
     */
    @ApiModelProperty(value = "10月")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月
     */
    @ApiModelProperty(value = "11月")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月
     */
    @ApiModelProperty(value = "12月")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 标识是否有消耗备货
     */
    @Transient
    Boolean influenceStockUp;

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
        this.setAdjustQuantity(BigDecimal.ZERO);
    }

    public void sumAdjustQuantity(){
        this.setAdjustQuantity(add(this.getM1Quantity(),this.getM2Quantity(),this.getM3Quantity(),this.getM4Quantity(),this.getM5Quantity(),this.getM6Quantity(),this.getM7Quantity(),this.getM8Quantity(),this.getM9Quantity(),this.getM10Quantity(),this.getM11Quantity(),this.getM12Quantity()));
    }

    private BigDecimal add(BigDecimal... args){
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal arg : args) {
            if (ObjectUtils.isNotEmpty(arg)){
                result = result.add(arg);
            }
        }
        return result;
    }
}
