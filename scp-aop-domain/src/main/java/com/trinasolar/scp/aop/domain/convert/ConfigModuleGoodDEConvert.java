package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigModuleGoodDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigModuleGood;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 组件良率行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:58
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigModuleGoodDEConvert extends BaseDEConvert<ConfigModuleGoodDTO, ConfigModuleGood> {

    ConfigModuleGoodDEConvert INSTANCE = Mappers.getMapper(ConfigModuleGoodDEConvert.class);

}
