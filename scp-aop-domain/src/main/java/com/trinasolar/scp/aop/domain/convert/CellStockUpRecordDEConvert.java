package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.CellStockUpRecord;
import com.trinasolar.scp.aop.domain.dto.CellStockUpRecordDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池备货调整记录 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-13 08:53:09
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellStockUpRecordDEConvert extends BaseDEConvert<CellStockUpRecordDTO, CellStockUpRecord> {

    CellStockUpRecordDEConvert INSTANCE = Mappers.getMapper(CellStockUpRecordDEConvert.class);

}
