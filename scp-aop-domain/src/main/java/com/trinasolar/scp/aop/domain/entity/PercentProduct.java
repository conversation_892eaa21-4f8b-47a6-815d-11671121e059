package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品族配比
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:31:43
 */
@Entity
@ToString
@Data
@Table(name = "aop_percent_product")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_percent_product SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_percent_product SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PercentProduct extends BasePO implements Serializable,Comparable<PercentProduct> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 产地,LOV:国内/国外
     */
    @ApiModelProperty(value = "产地,LOV:国内/国外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 原始销售版本
     */
    @ApiModelProperty(value = "原始销售版本")
    @Column(name = "sale_version")
    private String saleVersion;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * Q1比例
     */
    @ApiModelProperty(value = "Q1比例")
    @Column(name = "q1_percent")
    private BigDecimal q1Percent;

    /**
     * Q2比例
     */
    @ApiModelProperty(value = "Q2比例")
    @Column(name = "q2_percent")
    private BigDecimal q2Percent;

    /**
     * Q3比例
     */
    @ApiModelProperty(value = "Q3比例")
    @Column(name = "q3_percent")
    private BigDecimal q3Percent;

    /**
     * Q4比例
     */
    @ApiModelProperty(value = "Q4比例")
    @Column(name = "q4_percent")
    private BigDecimal q4Percent;

    @Transient
    private Integer priority;

    @Override
    public int compareTo(PercentProduct info) {
        return this.getPriority().compareTo(info.getPriority());
    }
}
