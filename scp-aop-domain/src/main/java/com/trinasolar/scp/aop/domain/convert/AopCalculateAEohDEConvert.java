package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ACellSupplySummaryInnerDTO;
import com.trinasolar.scp.aop.domain.entity.AopCalculateAEoh;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AopCalculateAEohDEConvert extends BaseDEConvert<ACellSupplySummaryInnerDTO, AopCalculateAEoh> {
    AopCalculateAEohDEConvert INSTANCE = Mappers.getMapper(AopCalculateAEohDEConvert.class);
}
