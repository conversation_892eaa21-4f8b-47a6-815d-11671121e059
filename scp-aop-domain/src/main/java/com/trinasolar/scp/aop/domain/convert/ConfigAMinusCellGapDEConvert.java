package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigAMinusCellGapDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigAMinusCellGap;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * A-效率gap行表 DTO与实体转换器
 *
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigAMinusCellGapDEConvert extends BaseDEConvert<ConfigAMinusCellGapDTO, ConfigAMinusCellGap> {

    ConfigAMinusCellGapDEConvert INSTANCE = Mappers.getMapper(ConfigAMinusCellGapDEConvert.class);

}
