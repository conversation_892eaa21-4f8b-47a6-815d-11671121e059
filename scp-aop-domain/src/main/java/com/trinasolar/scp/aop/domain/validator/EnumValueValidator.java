package com.trinasolar.scp.aop.domain.validator;

import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class EnumValueValidator implements ConstraintValidator<EnumValue, Object> {

    private String[] strValues;
    private int[] intValues;
    private Class<? extends Enum<?>>[] enumClasses;
    private String enumMethod;

    @Override
    public void initialize(EnumValue enumValue) {
        this.strValues = enumValue.strValues();
        this.intValues = enumValue.intValues();
        this.enumMethod = enumValue.enumMethod();
        this.enumClasses = enumValue.enumClasses();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext constraintValidatorContext) {
        if (strValues.length > 0) {
            if (value instanceof String) {
                for (String s : strValues) {
                    if (s.equals(value)) {
                        return true;
                    }
                }
            }
        }
        if (intValues.length > 0) {
            if (value instanceof Integer) {
                for (Integer i : intValues) {
                    if (i == value) {
                        return true;
                    }
                }
            }
        }
        if (enumClasses.length > 0 && StringUtils.isNotBlank(enumMethod)) {
            if (value == null) {
                return false;
            }
            Class<?> valueClass = value.getClass();
            for (Class<? extends Enum<?>> clazz : enumClasses) {
                try {
                    Method method = clazz.getMethod(enumMethod, valueClass);
                    if (!Boolean.TYPE.equals(method.getReturnType()) && !Boolean.class.equals(method.getReturnType())) {
                        throw new RuntimeException(String.format("%s method return is not boolean type in the %s class", enumMethod, clazz));
                    }
                    if (!Modifier.isStatic(method.getModifiers())) {
                        throw new RuntimeException(String.format("%s method is not static method in the %s class", enumMethod, clazz));
                    }
                    //静态方法obj为null非实例方法
                    Boolean result = (Boolean) method.invoke(null, value);
                    //如果检验通过这返回，否者继续下一个枚举类检验直到所有枚举类检验完毕
                    if (result) {
                        return true;
                    }
                } catch (NoSuchMethodException | SecurityException e) {
                    throw new RuntimeException(String.format("This %s(%s) method does not exist in the %s", enumMethod, valueClass, clazz), e);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }

        }
        return false;
    }
}
