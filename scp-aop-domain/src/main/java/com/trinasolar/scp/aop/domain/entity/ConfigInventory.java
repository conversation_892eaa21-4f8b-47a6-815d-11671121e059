package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

/**
 * 库存周转天数配置
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 17:31:09
 */
@Entity
@ToString
@Data
@Table(name = "aop_config_inventory")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_config_inventory SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_config_inventory SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false, of = {"countryFlag", "productType", "area", "subArea", "projectPlace", "salesChannel", "productSeries"})
@AllArgsConstructor
@NoArgsConstructor
public class ConfigInventory extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;
    @Transient
    private String countryFlagName;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 区域细分
     */
    @ApiModelProperty(value = "区域细分")
    @Column(name = "sub_area")
    private String subArea;

    /**
     * 项目地
     */
    @ApiModelProperty(value = "项目地")
    @Column(name = "project_place")
    private String projectPlace;

    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;


    /**
     * 销售渠道
     */
    @ApiModelProperty(value = "销售渠道")
    @Column(name = "sales_channel")
    private String salesChannel;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月周转天数
     */
    @ApiModelProperty(value = "1月周转天数")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月周转天数
     */
    @ApiModelProperty(value = "2月周转天数")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月周转天数
     */
    @ApiModelProperty(value = "3月周转天数")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 1季度周转天数
     */
    @ApiModelProperty(value = "1季度周转天数")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 4月周转天数
     */
    @ApiModelProperty(value = "4月周转天数")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月周转天数
     */
    @ApiModelProperty(value = "5月周转天数")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月周转天数
     */
    @ApiModelProperty(value = "6月周转天数")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 2季度周转天数
     */
    @ApiModelProperty(value = "2季度周转天数")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 7月周转天数
     */
    @ApiModelProperty(value = "7月周转天数")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月周转天数
     */
    @ApiModelProperty(value = "8月周转天数")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月周转天数
     */
    @ApiModelProperty(value = "9月周转天数")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 3季度周转天数
     */
    @ApiModelProperty(value = "3季度周转天数")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 10月周转天数
     */
    @ApiModelProperty(value = "10月周转天数")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月周转天数
     */
    @ApiModelProperty(value = "11月周转天数")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月周转天数
     */
    @ApiModelProperty(value = "12月周转天数")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 4季度周转天数
     */
    @ApiModelProperty(value = "4季度周转天数")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    /**
     * 通过LOV编码和EXCEL值设置LovValue
     *
     * @param lovMap
     */
    public String fillLovValue(Map<String, LovLineDTO> lovMap, String productType) {
        LovLineDTO areaLov = lovMap.get(LovHeaderCodeConstant.AOP_REGION + getArea());
        LovLineDTO projectPlaceLov = lovMap.get(LovHeaderCodeConstant.AOP_PROJECT_PLACE + getProjectPlace());
        LovLineDTO salesChannelLov = lovMap.get(LovHeaderCodeConstant.AOP_SALES_CHANNEL + getSalesChannel());
        LovLineDTO subAreaLov = lovMap.get(LovHeaderCodeConstant.AOP_SUB_REGION + getSubArea());
        if (ProductTypeEnum.MODULE.getCode().equals(productType)) {
            //区域
            if (areaLov == null) {
                return "区域的值集不存在";
            }
            setArea(areaLov.getLovValue());
            //项目地
            if (StringUtils.isNotBlank(getProjectPlace()) && projectPlaceLov == null) {
                return "项目地的值集不存在";
            } else {
                if (projectPlaceLov != null) {
                    setProjectPlace(projectPlaceLov.getLovValue());
                }

            }

            //销售渠道
            if (StringUtils.isNotBlank(getSalesChannel()) && salesChannelLov == null) {
                return "销售渠道的值集不存在";
            } else {
                if (salesChannelLov != null) {
                    setSalesChannel(salesChannelLov.getLovValue());
                }

            }

        }

        if (ObjectUtils.isNotEmpty(getCountryFlag())) {
            LovLineDTO countryFlagTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());
            //国内or海外
            if (countryFlagTypeLov == null) {
                return "国内or海外的值集不存在";
            }
            setCountryFlag(countryFlagTypeLov.getLovValue());
        }
        //产品类型 如果传入类型为组件类型，则寻值设值
//        if (!ProductTypeEnum.MODULE.getCode().equals(productType)) {
//            LovLineDTO productTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType());
//            if (productTypeLov == null) {
//                return "产品类型的值集不存在";
//            }
//            setProductType(productTypeLov.getLovValue());
//        }
        return null;
    }

    public String selectQuery(ConfigInventory dto, String productType) {
        if (!ProductTypeEnum.MODULE.getCode().equals(productType)) {
            if (
                    StringUtils.isBlank(productType)|| StringUtils.isBlank(dto.getCountryFlag()) ||
                            ObjectUtils.isEmpty(dto.getYear()) || StringUtils.isBlank(dto.getProductSeries())
            ) {
                return "数据异常";
            }
            setProductType(productType);
            setCountryFlag(dto.getCountryFlag());
            setYear(dto.getYear());
            setProductSeries(dto.getProductSeries());
            setIsDeleted(DeleteEnum.NO.getCode());
            setBasePlace(dto.getBasePlace());
        } else {
            if (
                    ObjectUtils.isEmpty(dto.getYear()) || ObjectUtils.isEmpty(dto.getArea())
            ) {
                return "数据异常";
            }
            setProductType("MODULE");
            setYear(dto.getYear());
            setArea(dto.getArea());
            setProjectPlace(dto.getProjectPlace());
            setSalesChannel(dto.getSalesChannel());
            setIsDeleted(DeleteEnum.NO.getCode());
        }
        return null;
    }

    public void setMothData(ConfigInventory dto, String productType) {
        //设置默认值
        this.setDefaultValue();

        if (Objects.equals(ProductTypeEnum.MODULE.getCode(), productType)) {

            if (dto.getQ1Quantity() != null) {
                setQ1Quantity(dto.getQ1Quantity());
            }
            if (dto.getQ2Quantity() != null) {
                setQ2Quantity(dto.getQ2Quantity());
            }
            if (dto.getQ3Quantity() != null) {
                setQ3Quantity(dto.getQ3Quantity());
            }
            if (dto.getQ4Quantity() != null) {
                setQ4Quantity(dto.getQ4Quantity());
            }
            setProductType("MODULE");

        } else {

            setProductType(productType);

            if (dto.getM1Quantity() != null) {
                setM1Quantity(dto.getM1Quantity());
            }
            if (dto.getM2Quantity() != null) {
                setM2Quantity(dto.getM2Quantity());
            }
            if (dto.getM3Quantity() != null) {
                setM3Quantity(dto.getM3Quantity());
            }
            if (dto.getM4Quantity() != null) {
                setM4Quantity(dto.getM4Quantity());
            }
            if (dto.getM5Quantity() != null) {
                setM5Quantity(dto.getM5Quantity());
            }
            if (dto.getM6Quantity() != null) {
                setM6Quantity(dto.getM6Quantity());
            }
            if (dto.getM7Quantity() != null) {
                setM7Quantity(dto.getM7Quantity());
            }
            if (dto.getM8Quantity() != null) {
                setM8Quantity(dto.getM8Quantity());
            }
            if (dto.getM9Quantity() != null) {
                setM9Quantity(dto.getM9Quantity());
            }
            if (dto.getM10Quantity() != null) {
                setM10Quantity(dto.getM10Quantity());
            }
            if (dto.getM11Quantity() != null) {
                setM11Quantity(dto.getM11Quantity());
            }
            if (dto.getM12Quantity() != null) {
                setM12Quantity(dto.getM12Quantity());
            }
            this.aggregateCalculation(dto);

            if (ObjectUtils.isNotEmpty(dto.getSubArea())) {
                setSubArea(dto.getSubArea());
            }
        }
    }

    /**
     * 设置默认值
     */
    public void setDefaultValue() {

    }

    /**
     * 汇总计算
     *
     * @param dto 传入值
     */
    public void aggregateCalculation(ConfigInventory dto) {
        setQ1Quantity(dto.getM1Quantity().add(dto.getM2Quantity()).add(dto.getM3Quantity()));
        setQ2Quantity(dto.getM4Quantity().add(dto.getM5Quantity()).add(dto.getM6Quantity()));
        setQ3Quantity(dto.getM7Quantity().add(dto.getM8Quantity()).add(dto.getM9Quantity()));
        setQ4Quantity(dto.getM10Quantity().add(dto.getM11Quantity()).add(dto.getM12Quantity()));
    }


}
