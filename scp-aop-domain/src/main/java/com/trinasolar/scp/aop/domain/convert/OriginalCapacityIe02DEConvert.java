package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.OriginCapacityIe;
import com.trinasolar.scp.aop.domain.entity.PreCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/14 20:26
 * @apiNote OriginCapacityIe、PreCapacityIe转换工具类
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OriginalCapacityIe02DEConvert extends BaseDEConvert<OriginCapacityIe, PreCapacityIe> {
    OriginalCapacityIe02DEConvert INSTANCE = Mappers.getMapper(OriginalCapacityIe02DEConvert.class);

    /**
     * List<OriginCapacityIe> 转为 List<PreCapacityIe>
     *
     * @param originCapacityIe IE原始产能 集合
     * @return List<PreCapacityIe> 预处理后IE产能 集合
     */
    List<PreCapacityIe> toPreCapacityIeList(List<OriginCapacityIe> originCapacityIe);
}