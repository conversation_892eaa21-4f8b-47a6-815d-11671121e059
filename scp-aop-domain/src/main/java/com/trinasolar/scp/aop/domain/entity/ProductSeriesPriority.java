package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 产品系列优先级
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-08-26 10:33:09
 */
@Entity
@ToString
@Data
@Table(name = "aop_product_series_priority")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_product_series_priority SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_product_series_priority SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProductSeriesPriority extends BasePO implements Serializable{
    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;

    /**
     * 组件产品系列
     */
    @ApiModelProperty(value = "组件产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Column(name = "priority_level")
    private Integer priorityLevel;


    /**
     * mfg优先级
     */
    @ApiModelProperty(value = "mfg优先级")
    @Column(name = "mfg_priority")
    private Integer mfgPriority;
}
