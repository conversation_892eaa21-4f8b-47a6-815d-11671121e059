package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculateSale;
import com.trinasolar.scp.aop.domain.entity.PreSale;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version v1.0
 * @className PreCalculateSaleDEConvert
 * @description 预处理销售数据和计算销售数据转换
 * @date 2022-07-20
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PreCalculateSaleDEConvert extends BaseDEConvert<PreSale, CalculateSale> {
    PreCalculateSaleDEConvert INSTANCE = Mappers.getMapper(PreCalculateSaleDEConvert.class);

    List<CalculateSale> toCalculate(List<PreSale> list);
}
