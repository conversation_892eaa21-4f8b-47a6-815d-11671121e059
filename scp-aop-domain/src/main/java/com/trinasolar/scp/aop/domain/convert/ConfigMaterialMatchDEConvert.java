package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigMaterialMatchDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigMaterialMatch;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * 制造参数材料搭配设置表 
 *
 * <AUTHOR>
 * @date 2022-9-28
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigMaterialMatchDEConvert extends BaseDEConvert<ConfigMaterialMatchDTO, ConfigMaterialMatch> {
}