package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 物料目标反馈
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-06-09 18:07:00
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_material_demand")
@Where(clause = " is_deleted=0 ")
//@SQLDelete(sql = "UPDATE aop_calculate_material_demand SET is_deleted = 1 WHERE id = ?")
//@SQLDeleteAll(sql = "UPDATE aop_calculate_material_demand SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false, of = {"materialType", "fromType", "year"})
@AllArgsConstructor
@NoArgsConstructor
public class CalculateMaterialDemand extends BasePO implements Serializable, Comparable<CalculateMaterialDemand> {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 物料大类
     */
    @ApiModelProperty(value = "物料大类")
    @Column(name = "material_type")
    private String materialType;

    /**
     * 来源类型：需求/供应
     */
    @ApiModelProperty(value = "来源类型：需求/供应")
    @Column(name = "from_type")
    private String fromType;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;
    /**
     * 是否计算
     */
    @ApiModelProperty(value = "是否计算")
    @Column(name = "not_calculate")
    private String notCalculate;

    /**
     * 1月需求量
     */
    @ApiModelProperty(value = "1月需求量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月需求量
     */
    @ApiModelProperty(value = "2月需求量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月需求量
     */
    @ApiModelProperty(value = "3月需求量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;
    /**
     * q1需求量
     */
    @ApiModelProperty(value = "q1需求量")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;

    /**
     * 4月需求量
     */
    @ApiModelProperty(value = "4月需求量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月需求量
     */
    @ApiModelProperty(value = "5月需求量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月需求量
     */
    @ApiModelProperty(value = "6月需求量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * q2需求量
     */
    @ApiModelProperty(value = "q2需求量")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;

    /**
     * 7月需求量
     */
    @ApiModelProperty(value = "7月需求量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月需求量
     */
    @ApiModelProperty(value = "8月需求量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月需求量
     */
    @ApiModelProperty(value = "9月需求量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * q3需求量
     */
    @ApiModelProperty(value = "q3需求量")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;

    /**
     * 10月需求量
     */
    @ApiModelProperty(value = "10月需求量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月需求量
     */
    @ApiModelProperty(value = "11月需求量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月需求量
     */
    @ApiModelProperty(value = "12月需求量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * q4需求量
     */
    @ApiModelProperty(value = "q4需求量")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;


    @Transient
    private BigDecimal totalQuantity;
    @Transient
    private Integer priority;


    public void sum() {
        this.setQ1Quantity(add(this.getM1Quantity(), this.getM2Quantity(), this.getM3Quantity()));
        this.setQ2Quantity(add(this.getM4Quantity(), this.getM5Quantity(), this.getM6Quantity()));
        this.setQ3Quantity(add(this.getM7Quantity(), this.getM8Quantity(), this.getM9Quantity()));
        this.setQ4Quantity(add(this.getM10Quantity(), this.getM11Quantity(), this.getM12Quantity()));
        this.setTotalQuantity(add(this.getQ1Quantity(), this.getQ2Quantity(), this.getQ3Quantity(), this.getQ4Quantity()));
    }

    private BigDecimal add(BigDecimal... args) {
        BigDecimal result = BigDecimal.ZERO;
        for (BigDecimal arg : args) {
            if (ObjectUtils.isNotEmpty(arg)) {
                result = result.add(arg);
            }
        }
        return result;
    }

    /**
     * 设置默认值
     */
    public void setDefaultValue() {
        //默认Y
        this.setLatestFlag("Y");
    }


    public String selectQuery(CalculateMaterialDemand dto) {
        if (
                StringUtils.isBlank(dto.getCountryFlag()) || StringUtils.isBlank(dto.getMaterialType()) ||
                        StringUtils.isBlank(dto.getFromType()) || ObjectUtils.isEmpty(dto.getYear()) ||
                        StringUtils.isBlank(dto.getUnit()) || StringUtils.isBlank(dto.getCalculateVersion())
        ) {
            return "数据异常";
        }

        setCountryFlag(dto.getCountryFlag());
        setMaterialType(dto.getMaterialType());
        setFromType(dto.getFromType());
        setYear(dto.getYear());
        setUnit(dto.getUnit());
        setCalculateVersion(dto.getCalculateVersion());
        return null;
    }

    public void setMothData(CalculateMaterialDemand dto) {
        //设置默认值
        this.setDefaultValue();

        setCalculateVersion(dto.getCalculateVersion());
        setUnit(dto.getUnit());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());

        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());

        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());

        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());

        this.aggregateCalculation(dto);
    }

    /**
     * 汇总计算
     *
     * @param dto 传入值
     */
    public void aggregateCalculation(CalculateMaterialDemand dto) {
        setQ1Quantity(dto.getM1Quantity().add(dto.getM2Quantity()).add(dto.getM3Quantity()));
        setQ2Quantity(dto.getM4Quantity().add(dto.getM5Quantity()).add(dto.getM6Quantity()));
        setQ3Quantity(dto.getM7Quantity().add(dto.getM8Quantity()).add(dto.getM9Quantity()));
        setQ4Quantity(dto.getM10Quantity().add(dto.getM11Quantity()).add(dto.getM12Quantity()));
    }

    public String fillLovValue(Map<String, LovLineDTO> lovMap) {
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());
        LovLineDTO materialTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_MATERIAL_TYPE + getMaterialType());
        LovLineDTO fromTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_FROM_TYPE + getFromType());
        LovLineDTO unitLov = lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit());

        //国内or海外
        if (countryFlagLov == null) {
            return String.format("物料大类的值集不存在-->{%s}", getMaterialType());
        }

        //材料名称
        if (materialTypeLov == null) {
            return String.format("物料大类的值集不存在-->{%s}", getMaterialType());
        }
        //来源类型
        if (fromTypeLov == null) {
            return String.format("来源类型值集不存在-->{%s}", getFromType());
        }
        //单位
        if (unitLov == null) {
            return String.format("单位的值集不存在---->{%s}", getUnit());
        }

        setCountryFlag(countryFlagLov.getLovValue());

        setMaterialType(materialTypeLov.getLovValue());
        setFromType(fromTypeLov.getLovValue());
        setUnit(unitLov.getLovValue());
        return null;
    }

    public void fillLovName(Map<String, LovLineDTO> lovMap) {
        LovLineDTO materialTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_MATERIAL_TYPE + getMaterialType());
        LovLineDTO fromTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_FROM_TYPE + getFromType());
        LovLineDTO unitLov = lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit());
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());

        //材料名称
        if (materialTypeLov != null) {
            setMaterialType(materialTypeLov.getLovName());
        }

        //来源类型
        if (fromTypeLov != null) {
            setFromType(fromTypeLov.getLovName());
        }
        //单位
        if (unitLov != null) {
            setUnit(unitLov.getLovName());
        }
        //单位
        if (countryFlagLov != null) {
            setCountryFlag(countryFlagLov.getLovName());
        }
    }

    @Override
    public int compareTo(CalculateMaterialDemand inner) {
        return this.getPriority().compareTo(inner.getPriority());
    }
}
