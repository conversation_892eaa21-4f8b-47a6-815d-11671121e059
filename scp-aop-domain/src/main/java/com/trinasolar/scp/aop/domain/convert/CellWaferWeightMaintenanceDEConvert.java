package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceDTO;
import com.trinasolar.scp.aop.domain.entity.CellWaferWeightMaintenance;
import com.trinasolar.scp.aop.domain.excel.CellWaferWeightMaintenanceImportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.CellWeightMaintenanceExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.WafterWeightMaintenanceExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 电池硅片权重维护 DTO与实体转换器
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellWaferWeightMaintenanceDEConvert extends BaseDEConvert<CellWaferWeightMaintenanceDTO, CellWaferWeightMaintenance> {

    CellWaferWeightMaintenanceDEConvert INSTANCE = Mappers.getMapper(CellWaferWeightMaintenanceDEConvert.class);

    List<CellWeightMaintenanceExportExcelDTO> toCellExportExcelDTO(List<CellWaferWeightMaintenanceDTO> dtos);

    List<WafterWeightMaintenanceExportExcelDTO> toWafterExportExcelDTO(List<CellWaferWeightMaintenanceDTO> dtos);

    CellWaferWeightMaintenanceDTO importExcelToDto(CellWaferWeightMaintenanceImportExcelDTO importExcelDTO);
}
