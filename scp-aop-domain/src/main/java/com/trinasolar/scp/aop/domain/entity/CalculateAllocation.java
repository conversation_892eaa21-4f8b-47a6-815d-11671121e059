package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算后库存分配
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:21:01
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_allocation")
@Where(clause = " is_deleted=0 ")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculateAllocation extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 产品来源：自产/OEM/采购/国内转海外等
     */
    @ApiModelProperty(value = "产品来源：自产/OEM/采购/国内转海外等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 大类：电池/组件/硅片
     */
    @ApiModelProperty(value = "大类：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;
    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;
    /**
     * q1Boh
     */
    @ApiModelProperty(value = "q1Boh")
    @Column(name = "q1_boh_quantity")
    private BigDecimal q1BohQuantity;

    /**
     * 1月份数量
     */
    @ApiModelProperty(value = "1月份数量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月份数量
     */
    @ApiModelProperty(value = "2月份数量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月份数量
     */
    @ApiModelProperty(value = "3月份数量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月份数量
     */
    @ApiModelProperty(value = "4月份数量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月份数量
     */
    @ApiModelProperty(value = "5月份数量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月份数量
     */
    @ApiModelProperty(value = "6月份数量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月份数量
     */
    @ApiModelProperty(value = "7月份数量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月份数量
     */
    @ApiModelProperty(value = "8月份数量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月份数量
     */
    @ApiModelProperty(value = "9月份数量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月份数量
     */
    @ApiModelProperty(value = "10月份数量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月份数量
     */
    @ApiModelProperty(value = "11月份数量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月份数量
     */
    @ApiModelProperty(value = "12月份数量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 一季度理论供应目标
     */
    @ApiModelProperty(value = "一季度理论供应目标")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;
    /**
     * 二季度理论供应目标
     */
    @ApiModelProperty(value = "二季度理论供应目标")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;
    /**
     * 三季度理论供应目标
     */
    @ApiModelProperty(value = "三季度理论供应目标")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;
    /**
     * 四季度理论供应目标
     */
    @ApiModelProperty(value = "四季度理论供应目标")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;
    /**
     * 一季度理论供应目标
     */
    @ApiModelProperty(value = "一季度销售")
    @Column(name = "q1_sale")
    private BigDecimal q1Sale;
    /**
     * 二季度理论供应目标
     */
    @ApiModelProperty(value = "二季度销售")
    @Column(name = "q2_sale")
    private BigDecimal q2Sale;
    /**
     * 三季度理论供应目标
     */
    @ApiModelProperty(value = "三季度销售")
    @Column(name = "q3_sale")
    private BigDecimal q3Sale;
    /**
     * 四季度理论供应目标
     */
    @ApiModelProperty(value = "四季度销售")
    @Column(name = "q4_sale")
    private BigDecimal q4Sale;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 来源（计算/调整）
     */
    @ApiModelProperty(value = "来源")
    @Column(name = "source")
    private String source;

    public void initZero() {
        if (ObjectUtils.isEmpty(q1BohQuantity)) {
            this.setQ1BohQuantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ1Quantity())) {
            this.setQ1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ2Quantity())) {
            this.setQ2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ3Quantity())) {
            this.setQ3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ4Quantity())) {
            this.setQ4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ1Sale())) {
            this.setQ1Sale(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ2Sale())) {
            this.setQ2Sale(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ3Sale())) {
            this.setQ3Sale(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ4Sale())) {
            this.setQ4Sale(BigDecimal.ZERO);
        }
    }

    public void initRmaZero(){
        this.setQ1Quantity(BigDecimal.ZERO);
        this.setQ2Quantity(BigDecimal.ZERO);
        this.setQ3Quantity(BigDecimal.ZERO);
        this.setQ4Quantity(BigDecimal.ZERO);
    }

}
