package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ACellSupplySummaryInnerDTO;
import com.trinasolar.scp.aop.domain.entity.AopCalculateASupply;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.control.DeepClone;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AopCalculateASupplyDEConvert extends BaseDEConvert<ACellSupplySummaryInnerDTO, AopCalculateASupply> {
    AopCalculateASupplyDEConvert INSTANCE = Mappers.getMapper(AopCalculateASupplyDEConvert.class);
}
