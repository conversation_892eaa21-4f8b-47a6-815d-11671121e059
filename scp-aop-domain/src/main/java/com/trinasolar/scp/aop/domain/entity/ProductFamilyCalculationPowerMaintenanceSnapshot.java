package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 产品族测算功率维护
 *
 * <AUTHOR>
 * @email
 * @date 2025-01-07
 */
@Entity
@ToString
@Data
@Table(name = "aop_product_family_calculation_power_maintenance_snapshot")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_product_family_calculation_power_maintenance_snapshot SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_product_family_calculation_power_maintenance_snapshot SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ProductFamilyCalculationPowerMaintenanceSnapshot extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;

    /**
     * 横竖装
     */
    @ApiModelProperty(value = "横竖装")
    @Column(name = "install_type")
    private String installType;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 1月权重
     */
    @ApiModelProperty(value = "1月权重")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月权重
     */
    @ApiModelProperty(value = "2月权重")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月权重
     */
    @ApiModelProperty(value = "3月权重")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月权重
     */
    @ApiModelProperty(value = "4月权重")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月权重
     */
    @ApiModelProperty(value = "5月权重")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月权重
     */
    @ApiModelProperty(value = "6月权重")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月权重
     */
    @ApiModelProperty(value = "7月权重")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月权重
     */
    @ApiModelProperty(value = "8月权重")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月权重
     */
    @ApiModelProperty(value = "9月权重")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月权重
     */
    @ApiModelProperty(value = "10月权重")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月权重
     */
    @ApiModelProperty(value = "11月权重")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月权重
     */
    @ApiModelProperty(value = "12月权重")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

}
