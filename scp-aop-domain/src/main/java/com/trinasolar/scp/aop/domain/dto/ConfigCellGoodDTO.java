package com.trinasolar.scp.aop.domain.dto;

import com.trinasolar.scp.aop.domain.constant.QPConstant;
import com.trinasolar.scp.common.api.annotation.ExportConvert;
import com.trinasolar.scp.common.api.annotation.ImportConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;


/**
 * 电池良率行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "ConfigCellGoodDTO对象", description = "DTO对象")
public class ConfigCellGoodDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 国内/海外
     */
    @ExportConvert(lovCode = QPConstant.Lov.DOMESTIC_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOversea;
    /**
     * 国内/海外
     */
    @ImportConvert(lovCode = QPConstant.Lov.DOMESTIC_OVERSEA)
    @ApiModelProperty(value = "国内/海外")
    private String isOverseaName;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;
    /**
     * 导入批次号
     */
    @ApiModelProperty(value = "导入批次号")
    private String batchNo;
    /**
     * 组ID：电池良率/电池功率维度
     */
    @ApiModelProperty(value = "组ID：电池良率/电池功率维度")
    private String groupId;
    /**
     * 满产/排产
     */
    @ExportConvert(lovCode = QPConstant.Lov.EQS_PARAMETER_TYPE)
    @ApiModelProperty(value = "满产/排产")
    private String parameterType;
    /**
     * 满产/排产
     */
    @ImportConvert(lovCode = QPConstant.Lov.EQS_PARAMETER_TYPE)
    @ApiModelProperty(value = "满产/排产")
    private String parameterTypeName;
    /**
     * 产品分类
     */
    @ExportConvert(lovCode = QPConstant.Lov.CELL_CLASSIFICATION)
    @ApiModelProperty(value = "产品分类")
    private String productType;
    /**
     * 产品分类
     */
    @ImportConvert(lovCode = QPConstant.Lov.CELL_CLASSIFICATION)
    @ApiModelProperty(value = "产品分类")
    private String productTypeName;
    /**
     * 品类
     */
    @ExportConvert(lovCode = QPConstant.Lov.CELL_CATEGORY)
    @ApiModelProperty(value = "品类")
    private String productCategory;
    /**
     * 品类
     */
    @ImportConvert(lovCode = QPConstant.Lov.CELL_CATEGORY)
    @ApiModelProperty(value = "品类")
    private String productCategoryName;
    /**
     * 主栅
     */
    @ExportConvert(lovCode = QPConstant.Lov.CELL_MAIN_GATE)
    @ApiModelProperty(value = "主栅")
    private String mainGrid;
    /**
     * 主栅
     */
    @ImportConvert(lovCode = QPConstant.Lov.CELL_MAIN_GATE)
    @ApiModelProperty(value = "主栅")
    private String mainGridName;
    /**
     * 分片方式
     */
    @ExportConvert(lovCode = QPConstant.Lov.CELL_SHARD_WAY)
    @ApiModelProperty(value = "分片方式")
    private String fragmentType;
    /**
     * 分片方式
     */
    @ImportConvert(lovCode = QPConstant.Lov.CELL_SHARD_WAY)
    @ApiModelProperty(value = "分片方式")
    private String fragmentTypeName;
    /**
     * 晶体类型
     */
    @ExportConvert(lovCode = QPConstant.Lov.CRYSTAL_TYPE)
    @ApiModelProperty(value = "晶体类型")
    private String crystalType;
    /**
     * 晶体类型
     */
    @ImportConvert(lovCode = QPConstant.Lov.CRYSTAL_TYPE)
    @ApiModelProperty(value = "晶体类型")
    private String crystalTypeName;
    /**
     * 晶体型号;单晶多晶
     */
    @ExportConvert(lovCode = QPConstant.Lov.EQS_CRYSTAL)
    @ApiModelProperty(value = "晶体型号;单晶多晶")
    private String crystalSpec;
    /**
     * 晶体型号;单晶多晶
     */
    @ImportConvert(lovCode = QPConstant.Lov.CRYSTAL_TYPE)
    @ApiModelProperty(value = "晶体型号;单晶多晶")
    private String crystalSpecName;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private BigDecimal cellHigh;
    /**
     * 厚度
     */
    @ApiModelProperty(value = "厚度")
    private String cellHighName;
    /**
     * 厚度单位
     */
    @ApiModelProperty(value = "厚度单位")
    private String highUnit;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    private String workshopName;
    /**
     * 指标类型：电池良率/电池功率
     */
    @ApiModelProperty(value = "指标类型：电池良率/电池功率")
    private String indexType;
    /**
     * 指标类型：电池良率/电池功率
     */
    @ApiModelProperty(value = "指标类型：电池良率/电池功率")
    private String indexTypeName;
    /**
     * 1月数
     */
    @ApiModelProperty(value = "1月数")
    private BigDecimal quantityM1;
    /**
     * 2月数
     */
    @ApiModelProperty(value = "2月数")
    private BigDecimal quantityM2;
    /**
     * 3月数
     */
    @ApiModelProperty(value = "3月数")
    private BigDecimal quantityM3;
    /**
     * 4月数
     */
    @ApiModelProperty(value = "4月数")
    private BigDecimal quantityM4;
    /**
     * 5月数
     */
    @ApiModelProperty(value = "5月数")
    private BigDecimal quantityM5;
    /**
     * 6月数
     */
    @ApiModelProperty(value = "6月数")
    private BigDecimal quantityM6;
    /**
     * 7月数
     */
    @ApiModelProperty(value = "7月数")
    private BigDecimal quantityM7;
    /**
     * 8月数
     */
    @ApiModelProperty(value = "8月数")
    private BigDecimal quantityM8;
    /**
     * 9月数
     */
    @ApiModelProperty(value = "9月数")
    private BigDecimal quantityM9;
    /**
     * 10月数
     */
    @ApiModelProperty(value = "10月数")
    private BigDecimal quantityM10;
    /**
     * 11月数
     */
    @ApiModelProperty(value = "11月数")
    private BigDecimal quantityM11;
    /**
     * 12月数
     */
    @ApiModelProperty(value = "12月数")
    private BigDecimal quantityM12;

    public String sign() {
        return isOversea.concat(year).concat(productType).concat(productCategory).concat(mainGrid).concat(crystalType).concat(crystalSpec);
    }
}
