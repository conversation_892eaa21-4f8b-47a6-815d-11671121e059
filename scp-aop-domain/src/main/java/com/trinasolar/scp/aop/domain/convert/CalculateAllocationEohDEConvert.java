package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateAllocationEohDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateAllocationEoh;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 计算库存EOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-28 16:00:20
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateAllocationEohDEConvert extends BaseDEConvert<CalculateAllocationEohDTO, CalculateAllocationEoh> {

    CalculateAllocationEohDEConvert INSTANCE = Mappers.getMapper(CalculateAllocationEohDEConvert.class);

}
