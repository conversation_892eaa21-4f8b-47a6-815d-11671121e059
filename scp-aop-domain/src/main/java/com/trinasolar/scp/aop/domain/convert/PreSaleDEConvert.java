package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.PreSaleDTO;
import com.trinasolar.scp.aop.domain.entity.PreSale;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/14 9:30
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PreSaleDEConvert extends BaseDEConvert<PreSaleDTO, PreSale> {
    PreSaleDEConvert INSTANCE = Mappers.getMapper(PreSaleDEConvert.class);

    /**
     * List<PreSale> 转为 List<PreSaleDTO>
     *
     * @param preSale 预处理后销售目标
     * @return 预处理后销售目标DTO
     */
    List<PreSaleDTO> toPreSaleDTOList(List<PreSale> preSale);
}
