package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigHalftoneDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigHalftone;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 网版行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:46
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigHalftoneDEConvert extends BaseDEConvert<ConfigHalftoneDTO, ConfigHalftone> {

    ConfigHalftoneDEConvert INSTANCE = Mappers.getMapper(ConfigHalftoneDEConvert.class);

}
