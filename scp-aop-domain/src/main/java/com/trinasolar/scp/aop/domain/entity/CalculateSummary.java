package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算后summary
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:26:31
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_summary")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_summary SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_summary SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateSummary extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 大类：电池/组件/硅片
     */
    @ApiModelProperty(value = "大类：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月份数量
     */
    @ApiModelProperty(value = "1月份数量")
    @Column(name = "m1_quantity")
    private String m1Quantity;

    /**
     * 2月份数量
     */
    @ApiModelProperty(value = "2月份数量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月份数量
     */
    @ApiModelProperty(value = "3月份数量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月份数量
     */
    @ApiModelProperty(value = "4月份数量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月份数量
     */
    @ApiModelProperty(value = "5月份数量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月份数量
     */
    @ApiModelProperty(value = "6月份数量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月份数量
     */
    @ApiModelProperty(value = "7月份数量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月份数量
     */
    @ApiModelProperty(value = "8月份数量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月份数量
     */
    @ApiModelProperty(value = "9月份数量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月份数量
     */
    @ApiModelProperty(value = "10月份数量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月份数量
     */
    @ApiModelProperty(value = "11月份数量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月份数量
     */
    @ApiModelProperty(value = "12月份数量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


}
