package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算后备货需求
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:22:46
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_demand")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_demand SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_demand SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class CalculateDemand extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;

    /**
     * 预处理版本
     */
    @ApiModelProperty(value = "预处理版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 业务场景：RMA/OTHER
     */
    @ApiModelProperty(value = "业务场景：RMA/OTHER")
    @Column(name = "business_type")
    private String businessType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 1月RMA备货需求量
     */
    @ApiModelProperty(value = "1月RMA备货需求量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月RMA备货需求量
     */
    @ApiModelProperty(value = "2月RMA备货需求量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月RMA备货需求量
     */
    @ApiModelProperty(value = "3月RMA备货需求量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月RMA备货需求量
     */
    @ApiModelProperty(value = "4月RMA备货需求量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月RMA备货需求量
     */
    @ApiModelProperty(value = "5月RMA备货需求量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月RMA备货需求量
     */
    @ApiModelProperty(value = "6月RMA备货需求量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月RMA备货需求量
     */
    @ApiModelProperty(value = "7月RMA备货需求量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月RMA备货需求量
     */
    @ApiModelProperty(value = "8月RMA备货需求量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月RMA备货需求量
     */
    @ApiModelProperty(value = "9月RMA备货需求量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月RMA备货需求量
     */
    @ApiModelProperty(value = "10月RMA备货需求量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月RMA备货需求量
     */
    @ApiModelProperty(value = "11月RMA备货需求量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月RMA备货需求量
     */
    @ApiModelProperty(value = "12月RMA备货需求量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

//
//    /**
//     * 一季度备货需求
//     */
//    @Transient
//    private BigDecimal q1Quantity;
//    @Transient
//    private BigDecimal q2Quantity;
//    @Transient
//    private BigDecimal q3Quantity;
//    @Transient
//    private BigDecimal q4Quantity;

    public void initZero(){
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }
}
