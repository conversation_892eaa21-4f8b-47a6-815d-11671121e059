package com.trinasolar.scp.aop.domain.convert;


import com.trinasolar.scp.aop.domain.dto.PercentProductDTO;
import com.trinasolar.scp.aop.domain.entity.PercentProduct;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 单双波配比
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PercentProductDEConvert extends BaseDEConvert<PercentProductDTO, PercentProduct> {

    PercentProductDEConvert INSTANCE = Mappers.getMapper(PercentProductDEConvert.class);
}
