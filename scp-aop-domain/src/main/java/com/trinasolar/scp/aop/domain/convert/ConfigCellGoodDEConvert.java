package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigCellGoodDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigCellGood;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池良率行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:55
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigCellGoodDEConvert extends BaseDEConvert<ConfigCellGoodDTO, ConfigCellGood> {

    ConfigCellGoodDEConvert INSTANCE = Mappers.getMapper(ConfigCellGoodDEConvert.class);

}
