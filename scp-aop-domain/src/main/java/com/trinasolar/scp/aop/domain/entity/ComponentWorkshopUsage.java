/**
 * <AUTHOR>
 * @date 2024-11-01 9:47
 */
package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2024/11/1 9:47
 */
@Entity
@ToString
@Data
@Table(name = "aop_component_workshop_usage_a_minus")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_component_workshop_usage_a_minus SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_component_workshop_usage_a_minus SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class ComponentWorkshopUsage extends BasePO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    @ApiModelProperty(value = "版本号")
    @Column(name = "data_version")
    private String dataVersion;
    @ApiModelProperty(value = "国内国外")
    @Column(name = "country_flag")
    private String countryFlag;
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_family")
    private String productFamily;
    /**
     * 基地
     */
    @ApiModelProperty(value = "基地")
    @Column(name = "base_place")
    private String basePlace;
    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 电池车间
     */
    @ApiModelProperty(value = "电池车间")
    @Column(name = "cell_workshop")
    private String cellWorkshop;

    @ApiModelProperty(value = "分配明细")
    @Column(name = "allot_detail")
    private String allotDetail;

    /**
     * 组件车间
     */
    @ApiModelProperty(value = "组件车间")
    @Column(name = "module_workshop")
    private String moduleWorkshop;

    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;

    /**
     * 分片方式
     */
    @ApiModelProperty(value = "分片方式")
    @Column(name = "fragment_type")
    private String fragmentType;

    /**
     * 1月组件车间A-使用量
     */
    @ApiModelProperty(value = "1月组件车间A-使用量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月组件车间A-使用量
     */
    @ApiModelProperty(value = "2月组件车间A-使用量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月组件车间A-使用量
     */
    @ApiModelProperty(value = "3月组件车间A-使用量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月组件车间A-使用量
     */
    @ApiModelProperty(value = "4月组件车间A-使用量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月组件车间A-使用量
     */
    @ApiModelProperty(value = "5月组件车间A-使用量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月组件车间A-使用量
     */
    @ApiModelProperty(value = "6月组件车间A-使用量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月组件车间A-使用量
     */
    @ApiModelProperty(value = "7月组件车间A-使用量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月组件车间A-使用量
     */
    @ApiModelProperty(value = "8月组件车间A-使用量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月组件车间A-使用量
     */
    @ApiModelProperty(value = "9月组件车间A-使用量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月组件车间A-使用量
     */
    @ApiModelProperty(value = "10月组件车间A-使用量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月组件车间A-使用量
     */
    @ApiModelProperty(value = "11月组件车间A-使用量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月组件车间A-使用量
     */
    @ApiModelProperty(value = "12月组件车间A-使用量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 一季度利用率
     */
    @ApiModelProperty(value = "一季度利用率")
    @Column(name = "q1_quantity")
    private BigDecimal q1Quantity;
    /**
     * 二季度利用率
     */
    @ApiModelProperty(value = "二季度利用率")
    @Column(name = "q2_quantity")
    private BigDecimal q2Quantity;
    /**
     * 三季度利用率
     */
    @ApiModelProperty(value = "三季度利用率")
    @Column(name = "q3_quantity")
    private BigDecimal q3Quantity;
    /**
     * 四季度利用率
     */
    @ApiModelProperty(value = "四季度利用率")
    @Column(name = "q4_quantity")
    private BigDecimal q4Quantity;

    public void initZero(){
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
        this.setQ1Quantity(BigDecimal.ZERO);
        this.setQ2Quantity(BigDecimal.ZERO);
        this.setQ3Quantity(BigDecimal.ZERO);
        this.setQ4Quantity(BigDecimal.ZERO);
    }
}
