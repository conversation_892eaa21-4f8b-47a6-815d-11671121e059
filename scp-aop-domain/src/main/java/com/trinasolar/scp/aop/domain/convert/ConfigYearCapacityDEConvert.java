package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigYearCapacityDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigYearCapacity;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 制造参数管理-满产产能/排产产能 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-29 09:11:41
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigYearCapacityDEConvert extends BaseDEConvert<ConfigYearCapacityDTO, ConfigYearCapacity> {

    ConfigYearCapacityDEConvert INSTANCE = Mappers.getMapper(ConfigYearCapacityDEConvert.class);

}
