package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculateEoh;
import com.trinasolar.scp.aop.domain.entity.PreEoh;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12 16:12
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PreCalculateEohDEConvert extends BaseDEConvert<PreEoh, CalculateEoh> {

    PreCalculateEohDEConvert INSTANCE = Mappers.getMapper(PreCalculateEohDEConvert.class);

    List<CalculateEoh> toCalculate(List<PreEoh> preEohList);

}
