package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigCellPercentDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigCellPercent;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池占比表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:04
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigCellPercentDEConvert extends BaseDEConvert<ConfigCellPercentDTO, ConfigCellPercent> {

    ConfigCellPercentDEConvert INSTANCE = Mappers.getMapper(ConfigCellPercentDEConvert.class);

}
