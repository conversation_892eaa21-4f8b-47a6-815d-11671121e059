package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculatePurchaseDemand;
import com.trinasolar.scp.aop.domain.entity.PurchaseDemand;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17 13:56
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface OemPurchaseCalculateDEConvert extends BaseDEConvert<PurchaseDemand, CalculatePurchaseDemand> {
    OemPurchaseCalculateDEConvert INSTANCE = Mappers.getMapper(OemPurchaseCalculateDEConvert.class);

    List<CalculatePurchaseDemand> toCalculate(List<PurchaseDemand> purchaseDemandList);
}
