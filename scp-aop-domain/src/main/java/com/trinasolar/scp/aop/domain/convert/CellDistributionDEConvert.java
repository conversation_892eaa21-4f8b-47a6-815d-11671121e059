package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.CellDistribution;
import com.trinasolar.scp.aop.domain.dto.CellDistributionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池分配 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-09-30 14:32:03
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellDistributionDEConvert extends BaseDEConvert<CellDistributionDTO, CellDistribution> {

    CellDistributionDEConvert INSTANCE = Mappers.getMapper(CellDistributionDEConvert.class);

}
