package com.trinasolar.scp.aop.domain.query;

import com.trinasolar.scp.common.api.base.PageDTO;
import com.trinasolar.scp.common.api.util.ExcelPara;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 背银行表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:49
 */
@Data
@ApiModel(value = "ConfigBackSilver查询条件", description = "查询条件")
@Accessors(chain = true)
public class ConfigBackSilverQuery extends PageDTO implements Serializable {


    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    private String isOversea;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "满产/排产")
    private String parameterType;

    @ApiModelProperty(value = "产品系列")
    private String productSeries;

    @ApiModelProperty(value = "单晶/多晶")
    private String crystalSpec;

    @ApiModelProperty(value = "车间")
    private String workshop;
    /**
     * 导出列映射顺序对象
     */
    @ApiModelProperty(value = "导出列映射顺序对象")
    private ExcelPara excelPara;
}
