package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import com.trinasolar.scp.aop.domain.entity.HaCapacity;
import com.trinasolar.scp.aop.domain.dto.HaCapacityDTO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * ha产能表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-26 11:25:01
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HaCapacityDEConvert extends BaseDEConvert<HaCapacityDTO, HaCapacity> {

    HaCapacityDEConvert INSTANCE = Mappers.getMapper(HaCapacityDEConvert.class);

}
