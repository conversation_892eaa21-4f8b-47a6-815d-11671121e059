package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ActualOutputDTO;
import com.trinasolar.scp.aop.domain.entity.ActualOutput;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17 10:12
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActualOutputDEConvert extends BaseDEConvert<ActualOutputDTO, ActualOutput> {

    ActualOutputDEConvert INSTANCE = Mappers.getMapper(ActualOutputDEConvert.class);

    /**
     * List<ActualOutput> 转为 List<ActualOutputDTO>
     *
     * @param actualOutputs 实际产出实体数据集合
     * @return List<ActualOutputDTO> 实际产出DTO数据集合
     */
    List<ActualOutputDTO> toActualOutputDTOs(List<ActualOutput> actualOutputs);

    /**
     * List<ActualOutputDTO> 转为 List<ActualOutput>
     *
     * @param actualOutputDTOs
     * @return
     */
    List<ActualOutput> toActualOutputs(List<ActualOutputDTO> actualOutputDTOs);
}
