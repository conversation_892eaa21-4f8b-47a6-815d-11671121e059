package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 电池硅片权重
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
@Entity
@ToString
@Data
@Table(name = "aop_average_power_snapshot")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_average_power_snapshot SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_average_power_snapshot SET is_deleted = 1 WHERE id = ?")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class AveragePowerSnapshot extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;


    /**
     * 电池型号
     */
    @ApiModelProperty(value = "电池型号")
    @Column(name = "cell_model")
    private String cellModel;

    /**
     * 电池分片方式
     */
    @ApiModelProperty(value = "电池分片方式")
    @Column(name = "cell_shard")
    private String cellShard;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Column(name = "data_version")
    private String dataVersion;


    @ApiModelProperty(value = "1月平均功率")
    private BigDecimal averagePower1;

    @ApiModelProperty(value = "2月平均功率")
    private BigDecimal averagePower2;

    @ApiModelProperty(value = "3月平均功率")
    private BigDecimal averagePower3;

    @ApiModelProperty(value = "4月平均功率")
    private BigDecimal averagePower4;

    @ApiModelProperty(value = "5月平均功率")
    private BigDecimal averagePower5;

    @ApiModelProperty(value = "6月平均功率")
    private BigDecimal averagePower6;

    @ApiModelProperty(value = "7月平均功率")
    private BigDecimal averagePower7;

    @ApiModelProperty(value = "8月平均功率")
    private BigDecimal averagePower8;

    @ApiModelProperty(value = "9月平均功率")
    private BigDecimal averagePower9;

    @ApiModelProperty(value = "10月平均功率")
    private BigDecimal averagePower10;

    @ApiModelProperty(value = "11月平均功率")
    private BigDecimal averagePower11;

    @ApiModelProperty(value = "12月平均功率")
    private BigDecimal averagePower12;

}
