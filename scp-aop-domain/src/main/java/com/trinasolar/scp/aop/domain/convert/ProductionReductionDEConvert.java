package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ProductionReductionDTO;
import com.trinasolar.scp.aop.domain.entity.ProductionReduction;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 减产量表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:13:57
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ProductionReductionDEConvert extends BaseDEConvert<ProductionReductionDTO, ProductionReduction> {

    ProductionReductionDEConvert INSTANCE = Mappers.getMapper(ProductionReductionDEConvert.class);

}
