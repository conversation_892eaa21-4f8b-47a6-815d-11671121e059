package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.CalculatePurchaseDemand;
import com.trinasolar.scp.aop.domain.entity.PrePurchaseDemand;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12 16:12
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculatePurchaseDEConvert extends BaseDEConvert<CalculatePurchaseDemand, PrePurchaseDemand> {

    CalculatePurchaseDEConvert INSTANCE = Mappers.getMapper(CalculatePurchaseDEConvert.class);

    List<CalculatePurchaseDemand> toCalculate(List<PrePurchaseDemand> prePurchaseDemandList);
}
