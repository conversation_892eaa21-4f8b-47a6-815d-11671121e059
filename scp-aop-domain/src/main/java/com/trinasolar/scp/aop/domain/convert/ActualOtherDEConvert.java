package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ActualOtherDTO;
import com.trinasolar.scp.aop.domain.entity.ActualOther;
import com.trinasolar.scp.aop.domain.excel.ActualOtherImportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ActualOtherPurchaseExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.ActualOtherTakeoutExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/12 16:12
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActualOtherDEConvert extends BaseDEConvert<ActualOtherDTO, ActualOther> {

    ActualOtherDEConvert INSTANCE = Mappers.getMapper(ActualOtherDEConvert.class);

    List<ActualOtherTakeoutExportExcelDTO> toTakeoutExcel(List<ActualOtherDTO> content);

    List<ActualOtherPurchaseExportExcelDTO> toPurchaseExcel(List<ActualOtherDTO> content);

    ActualOtherDTO importExcelToDto(ActualOtherImportExcelDTO importExcelDTO);
}
