package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.PreCapacityIePreprocessingDTO;
import com.trinasolar.scp.aop.domain.entity.PreCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/15 15:18
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PreCapacityIePreprocessingDEConvert extends BaseDEConvert<PreCapacityIePreprocessingDTO, PreCapacityIe> {
    PreCapacityIePreprocessingDEConvert INSTANCE = Mappers.getMapper(PreCapacityIePreprocessingDEConvert.class);

    /**
     * List<PreCapacityIe> 转为 List<PreCapacityIePreprocessingDTO>
     *
     * @param preCapacityIes 预处理后IE产能数据集合
     * @return List<PreCapacityIePreprocessingDTO> 预处理页面回显DTO集合
     */
    List<PreCapacityIePreprocessingDTO> toPreCapacityIePreprocessingDTO(List<PreCapacityIe> preCapacityIes);
}
