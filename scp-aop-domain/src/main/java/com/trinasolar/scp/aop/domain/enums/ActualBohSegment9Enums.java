package com.trinasolar.scp.aop.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
@AllArgsConstructor
public enum ActualBohSegment9Enums {
    PERC("PERC", "P"),
    TOPCON("TOPCON", "N"),
    HJT("HJT", "H"),
    TBC("TBC", "B");

    private String code;
    private String desc;

    public static String getDescByCode(String code) {
        if (StringUtils.isEmpty(code)) return null;
        ActualBohSegment9Enums[] values = ActualBohSegment9Enums.values();
        for (ActualBohSegment9Enums value : values) {
            if (code.equals(value.getCode())) {
                return value.getDesc();
            }
        }
        return null;
    }
}
