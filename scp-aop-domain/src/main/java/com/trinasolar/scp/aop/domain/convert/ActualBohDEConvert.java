package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ActualBohDTO;
import com.trinasolar.scp.aop.domain.dto.ActualOtherDTO;
import com.trinasolar.scp.aop.domain.entity.ActualBoh;
import com.trinasolar.scp.aop.domain.entity.ActualOther;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 期初库存 转换器
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActualBohDEConvert extends BaseDEConvert<ActualBohDTO, ActualBoh> {
    ActualBohDEConvert INSTANCE = Mappers.getMapper(ActualBohDEConvert.class);
}
