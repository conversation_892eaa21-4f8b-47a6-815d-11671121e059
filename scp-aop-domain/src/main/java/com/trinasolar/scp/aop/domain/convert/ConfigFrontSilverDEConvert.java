package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigFrontSilverDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigFrontSilver;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 正银行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:50:52
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigFrontSilverDEConvert extends BaseDEConvert<ConfigFrontSilverDTO, ConfigFrontSilver> {

    ConfigFrontSilverDEConvert INSTANCE = Mappers.getMapper(ConfigFrontSilverDEConvert.class);

}
