package com.trinasolar.scp.aop.domain.priority;

import com.trinasolar.scp.aop.domain.enums.ComponentTypeEnum;
import com.trinasolar.scp.aop.domain.enums.SalesChannelEnum;
import com.trinasolar.scp.aop.domain.enums.CountryFlagEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version v1.0
 * @className SalePriority
 * @description 优先级配置权重，使用时根据使用的优先级个数适当扩大倍数
 * @data 2022-11-21
 */
public class Priority {
    /**
     * 区域优先级
     */
    @AllArgsConstructor
    @Getter
    public enum Area {
        NA("NA", 1),
        LAC("LAC", 2),
        EMNA("EMNA", 3),
        EU("EU", 4),
        APEC("APEC", 5),
        JPN("JPN", 6),
        APAC("APAC", 7),
        MENA("MENA", 8),
        CHN("CHN", 9);

        public static Integer getByCode(String code) {
            Area[] values = Area.values();
            if (StringUtils.isNotBlank(code)) {
                for (Area value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }

        private String code;
        private Integer priority;
    }

    /**
     * 国内/海外 优先级
     */
    @AllArgsConstructor
    @Getter
    public enum Country {
        OVERSEA(CountryFlagEnum.OVERSEA.getCode(), 1),
        INLAND(CountryFlagEnum.INLAND.getCode(), 2);

        private String code;
        private Integer priority;


        public static Integer getByCode(String code) {
            Country[] values = Country.values();
            if (StringUtils.isNotBlank(code)) {
                for (Country value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 组件产品系列优先级
     */
    @AllArgsConstructor
    @Getter
    public enum Module {
        P210_400("400W-210-P", 1),
        P210_500("500W-210-P", 2),
        P210_550("550W-210-P", 3),
        P210_600("600W-210-P", 4),
        P210_660("660W-210-P", 5),
        P210_260("260W-210-P", 6),
        N210_620("620W-210-N", 7),
        N210_680("680W-210-N", 8),
        RP210_430("430W-210R-P", 9),
        RP210_580("580W-210R-P", 10),
        NP210_440("440W-210R-N", 11),
        NP210_590("590W-210R-N", 12),
        P182_550("550W-182-P", 13),
        P_166("166", 14),
        MP156_270("270W-156-MP", 15),
        MP156_320("320W-156-MP", 16);

        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            Module[] values = Module.values();
            if (StringUtils.isNotBlank(code)) {
                for (Module value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 电池型号优先级
     */
    @AllArgsConstructor
    @Getter
    public enum Cell {
        P210("210-P", 1),
        N210("210-N", 2),
        RP210("210R-P", 3),
        RN210("210R-N", 4),
        P182("182", 5),
        P166("166", 6),
        P156("156", 7);

        private String code;
        private Integer priority;


        public static Integer getByCode(String code) {
            Cell[] values = Cell.values();
            if (StringUtils.isNotBlank(code)) {
                for (Cell value : values) {
                    if (code.contains(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 硅片型号优先级
     */
    @AllArgsConstructor
    @Getter
    public enum Wafer {
        P210("210-P型", 1),
        N210("210-N型", 2),
        RP210("210R-P型", 3),
        RN210("210R-N型", 4),
        P182("182-P型", 5);

        private String code;
        private Integer priority;


        public static Integer getByCode(String code) {
            Wafer[] values = Wafer.values();
            if (StringUtils.isNotBlank(code)) {

                for (Wafer value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 基地优先级（ps:组件基地和电池基地重合率高，电池基地比组件基地多了【青海】，少了【义乌、大丰、常州、内蒙】）
     */
    @AllArgsConstructor
    @Getter
    public enum BasePlace {
        YN("越南", 1),
        TG("泰国", 2),
        YW("义乌", 3),
        SQ("宿迁", 4),
        YC("盐城", 5),
        DF("大丰", 6),
        CZ("常州", 7),
        QH("青海", 8),
        HA("淮安", 9),
        NM("内蒙", 10),
        XJ("新建", 11);


        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            BasePlace[] values = BasePlace.values();
            if (StringUtils.isNotBlank(code)) {
                for (BasePlace value : values) {
                    if (code.contains(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 车间优先级（ps：按车间上code的1，2，3，4，5·····排序）
     */
    @AllArgsConstructor
    @Getter
    public enum Workshop {
        L1("1", 1),
        L2("2", 2),
        L3("3", 3),
        L4("4", 4),
        L5("5", 5),
        L6("6", 6),
        L7("7", 7),
        L8("8", 8),
        L9("9", 9),
        L10("10", 10),
        L11("11", 11),
        L12("12", 12),
        L13("13", 13),
        L14("14", 14),
        L15("15", 15),
        L16("16", 16),
        L17("17", 17),
        L18("18", 18),
        L19("19", 19),
        L20("20", 20),
        L21("21", 21),
        L22("22", 22),
        L23("23", 23),
        L24("24", 24),
        L25("25", 25),
        L26("26", 26),
        L27("27", 27),
        L28("28", 28),
        L29("29", 29),
        L30("30", 30),
        L31("31", 31),
        L32("32", 32),
        L33("33", 33),
        L34("34", 34),
        L35("35", 35),
        L36("36", 36),
        L37("37", 37),
        L38("38", 38),
        L39("39", 39),
        L40("40", 40),
        L41("41", 41),
        L42("42", 42),
        L43("43", 43),
        L44("44", 44),
        L45("45", 45),
        L46("46", 46),
        L47("47", 47),
        L48("48", 48),
        L49("49", 49),
        L50("50", 50),
        L51("51", 51),
        L52("52", 52),
        L53("53", 53),
        L54("54", 54),
        L55("55", 55),
        L56("56", 56),
        L57("57", 57),
        L58("58", 58),
        L59("59", 59),
        L60("60", 60),
        L61("61", 61),
        L62("62", 62),
        L63("63", 63),
        L64("64", 64),
        L65("65", 65),
        L66("66", 66),
        L67("67", 67),
        L68("68", 68),
        L69("69", 69),
        L70("70", 70),
        L71("71", 71),
        L72("72", 72),
        L73("73", 73),
        L74("74", 74),
        L75("75", 75),
        L76("76", 76),
        L77("77", 77),
        L78("78", 78),
        L79("79", 79),
        L80("80", 80),
        L81("81", 81),
        L82("82", 82),
        L83("83", 83),
        L84("84", 84),
        L85("85", 85),
        L86("86", 86),
        L87("87", 87),
        L88("88", 88),
        L89("89", 89),
        L90("90", 90),
        L91("91", 91),
        L92("92", 92),
        L93("93", 93),
        L94("94", 94),
        L95("95", 95),
        L96("96", 96),
        L97("97", 97),
        L98("98", 98),
        L99("99", 99),
        L100("10", 100);
        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            Workshop[] values = Workshop.values();
            if (StringUtils.isNotBlank(code)) {
                for (Workshop value : values) {
                    if (code.contains(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum CellShard {
        THC("3-cut", 1),
        TWC("Half-cut", 2);
        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            CellShard[] values = CellShard.values();
            if (StringUtils.isNotBlank(code)) {
                for (CellShard value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 销售渠道
     */
    @AllArgsConstructor
    @Getter
    public enum SalesChannel {
        NONUTILITY(SalesChannelEnum.NONUTILITY.getCode(), 1),
        UTILITY(SalesChannelEnum.UTILITY.getCode(), 2);
        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            SalesChannel[] values = SalesChannel.values();
            if (StringUtils.isNotBlank(code)) {
                for (SalesChannel value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 销售渠道
     */
    @AllArgsConstructor
    @Getter
    public enum Component {
        BIFACIALDUALGLASS(ComponentTypeEnum.BIFACIALDUALGLASS.getCode(), 1),
        MONOFACIALDUALGLASS(ComponentTypeEnum.MONOFACIALDUALGLASS.getCode(), 2),
        BACKSHEET(ComponentTypeEnum.BACKSHEET.getCode(), 3);
        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            Component[] values = Component.values();
            if (StringUtils.isNotBlank(code)) {
                for (Component value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + 2;
        }
    }

    /**
     * 辅料能力反馈排序
     */
    @AllArgsConstructor
    @Getter
    public enum Material {
        GLASS("玻璃", Module.values().length + 3),
        PROFILE("型材", Module.values().length + 4),
        WIREBOX("接线盒", Module.values().length + 5),
        BACKSHEET("背板", Module.values().length + 6),
        EVA("EVA", Module.values().length + 7),
        TIN("涂锡带", Module.values().length + 8),
        SILICAGEL("硅胶", Module.values().length + 9),
        BACK("包材", Module.values().length + 10);

        private String code;
        private Integer priority;

        public static Integer getByCode(String code) {
            Material[] values = Material.values();
            if (StringUtils.isNotBlank(code)) {
                for (Material value : values) {
                    if (code.equals(value.getCode())) {
                        return value.getPriority();
                    }
                }
            }
            return values.length + Module.values().length + 5;
        }
    }
}
