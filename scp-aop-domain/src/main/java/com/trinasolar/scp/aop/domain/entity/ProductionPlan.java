package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.util.BizException;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * 排产计划
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:40:58
 */
@Entity
@ToString
@Data
@Table(name = "aop_production_plan")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_production_plan SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_production_plan SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false, of = {"productType", "countryFlag", "productSeries", "workshop", "year"})
@AllArgsConstructor
@NoArgsConstructor
public class ProductionPlan extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 大类：电池/组件/硅片
     */
    @ApiModelProperty(value = "大类：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;
    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Transient
    private String countryFlagName;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 产品族
     */
    @ApiModelProperty(value = "产品族")
    @Column(name = "product_group")
    private String productGroup;
    /**
     * 基地
     */
    @ApiModelProperty("基地")
    @Column(name = "base_place")
    private String basePlace;

    /**
     * 车间
     */
    @ApiModelProperty(value = "车间")
    @Column(name = "workshop")
    private String workshop;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 1月份排产量
     */
    @ApiModelProperty(value = "1月份排产量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月份排产量
     */
    @ApiModelProperty(value = "2月份排产量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月份排产量
     */
    @ApiModelProperty(value = "3月份排产量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月份排产量
     */
    @ApiModelProperty(value = "4月份排产量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月份排产量
     */
    @ApiModelProperty(value = "5月份排产量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月份排产量
     */
    @ApiModelProperty(value = "6月份排产量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月份排产量
     */
    @ApiModelProperty(value = "7月份排产量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月份排产量
     */
    @ApiModelProperty(value = "8月份排产量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月份排产量
     */
    @ApiModelProperty(value = "9月份排产量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月份排产量
     */
    @ApiModelProperty(value = "10月份排产量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月份排产量
     */
    @ApiModelProperty(value = "11月份排产量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月份排产量
     */
    @ApiModelProperty(value = "12月份排产量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;


    /**
     * 第一季度销量
     */
    @Transient
    @ApiModelProperty(value = "第一季度销量")
    private BigDecimal q1Quantity;

    /**
     * 第二季度销量
     */
    @Transient
    @ApiModelProperty(value = "第二季度销量")
    private BigDecimal q2Quantity;

    /**
     * 第三季度销量
     */
    @Transient
    @ApiModelProperty(value = "第三季度销量")
    private BigDecimal q3Quantity;

    /**
     * 第四季度销量
     */
    @Transient
    @ApiModelProperty(value = "第四季度销量")
    private BigDecimal q4Quantity;

    /**
     * 汇总
     */
    @Transient
    @ApiModelProperty(value = "汇总")
    private BigDecimal summary;

    @Transient
    private Integer actualMonth;

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }

    public String selectQuery(ProductionPlan dto) {
        if (StringUtils.isBlank(dto.getProductType()) || StringUtils.isBlank(dto.getProductSeries()) ||
                StringUtils.isBlank(dto.getWorkshop()) || StringUtils.isBlank(dto.getCountryFlag()) ||
                ObjectUtils.isEmpty(dto.getYear())) {
            return "数据异常";
        }

        setProductType(dto.getProductType());
        setWorkshop(dto.getWorkshop());
        setProductSeries(dto.getProductSeries());
        setYear(dto.getYear());
        setCountryFlag(dto.getCountryFlag());
        setArea(dto.getArea());
        return null;
    }

    public void setMothData(ProductionPlan entity) {
        if (getM1Quantity() == null) {
            setM1Quantity(entity.getM1Quantity());
        }
        if (getM2Quantity() == null) {
            setM2Quantity(entity.getM2Quantity());
        }
        if (getM3Quantity() == null) {
            setM3Quantity(entity.getM3Quantity());
        }
        if (getM4Quantity() == null) {
            setM4Quantity(entity.getM4Quantity());
        }
        if (getM5Quantity() == null) {
            setM5Quantity(entity.getM5Quantity());
        }
        if (getM6Quantity() == null) {
            setM6Quantity(entity.getM6Quantity());
        }
        if (getM7Quantity() == null) {
            setM7Quantity(entity.getM7Quantity());
        }
        if (getM8Quantity() == null) {
            setM8Quantity(entity.getM8Quantity());
        }
        if (getM9Quantity() == null) {
            setM9Quantity(entity.getM9Quantity());
        }
        if (getM10Quantity() == null) {
            setM10Quantity(entity.getM10Quantity());
        }
        if (getM11Quantity() == null) {
            setM11Quantity(entity.getM11Quantity());
        }
        if (getM12Quantity() == null) {
            setM12Quantity(entity.getM12Quantity());
        }
        if (StringUtils.isNotBlank(getRemark())) {
            setRemark(entity.getRemark());
        }


//        setProductGroup(dto.getProductGroup());
    }

    public String fillLovValue(Map<String, LovLineDTO> lovMap) {
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlagName());
//        LovLineDTO productTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_SERIES + getProductSeries());
//        //产品类型
//        if (productTypeLov == null) {
//            return String.format("产品类型的值集不存在-->{%s}", getProductType());
//        }
        //国内/海外
        if (countryFlagLov == null) {
            return String.format("国内/海外的值集不存在-->{%s}", getCountryFlagName());
        }

//        setProductType(productTypeLov.getLovValue());
        setCountryFlag(countryFlagLov.getLovValue());
        return null;
    }

    /**
     * 设置LovName值
     *
     * @param lovMap
     * <AUTHOR>
     */
    public void setLovName(Map<String, LovLineDTO> lovMap) {
        //国内/海外
        if (lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()) != null) {
            setCountryFlag(lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()).getLovName());
        }
        //产品类型
        if (lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()) != null) {
            setProductType(lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()).getLovName());
        }
    }

    /**
     * 设置LovValue值
     *
     * @param lovMap
     */
    public void setLovValue(Map<String, LovLineDTO> lovMap) {
        //国内/海外
        if (lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()) == null) {
            throw new BizException(String.format("国内/海外(CountryFlag) 值错误---{%s}", getCountryFlag()));
        }
        setCountryFlag(lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()).getLovValue());

        //产品类型：电池/组件自产/硅片/组件兼容
        if (lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()) == null) {
            throw new BizException(String.format("产品类型(ProductType) 值错误---{%s}", getProductType()));
        }
        setProductType(lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()).getLovValue());

    }
}
