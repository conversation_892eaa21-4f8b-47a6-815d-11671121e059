package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.ActualSale;
import com.trinasolar.scp.aop.domain.entity.OriginSale;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/4 8:39
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ActualSaleDEConveret extends BaseDEConvert<ActualSale, OriginSale> {
    ActualSaleDEConveret INSTANCE = Mappers.getMapper(ActualSaleDEConveret.class);

    /**
     * originSaleList 转为 actualSaleList
     *
     * @param originSaleList 原始销售量集合
     * @return actualSaleList 实际销售量集合
     * <AUTHOR>
     */
    List<ActualSale> toActualSaleList(List<OriginSale> originSaleList);

    /**
     * actualSaleList 转为 originSaleList
     *
     * @param actualSaleList 实际销售量集合
     * @return originSaleList 原始销售量集合
     * <AUTHOR>
     */
    List<OriginSale> toOriginSaleList(List<ActualSale> actualSaleList);

}
