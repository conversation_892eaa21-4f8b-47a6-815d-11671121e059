package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigCellSizeDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigCellSize;
import com.trinasolar.scp.aop.domain.save.ConfigCellSizeSaveDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池尺寸行表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-25 19:51:01
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigCellSizeDEConvert extends BaseDEConvert<ConfigCellSizeDTO, ConfigCellSize> {

    ConfigCellSizeDEConvert INSTANCE = Mappers.getMapper(ConfigCellSizeDEConvert.class);

    ConfigCellSize toEntity(ConfigCellSizeSaveDTO saveDTO);

}
