package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CellWaferWeightDTO;
import com.trinasolar.scp.aop.domain.entity.CellWaferWeight;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池硅片权重 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-27 14:54:45
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellWaferWeightDEConvert extends BaseDEConvert<CellWaferWeightDTO, CellWaferWeight> {

    CellWaferWeightDEConvert INSTANCE = Mappers.getMapper(CellWaferWeightDEConvert.class);

}
