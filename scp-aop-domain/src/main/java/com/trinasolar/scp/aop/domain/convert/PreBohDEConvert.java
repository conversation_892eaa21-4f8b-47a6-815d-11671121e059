package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.ActualBoh;
import com.trinasolar.scp.aop.domain.entity.PreBoh;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/17 13:56
 * @apiNote
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PreBohDEConvert extends BaseDEConvert<PreBoh, ActualBoh> {
    PreBohDEConvert INSTANCE = Mappers.getMapper(PreBohDEConvert.class);

    /**
     * List<ActualBoh> 转为 List<PreBoh>
     *
     * @param actualBohs 实际BOH集合
     * @return 预处理BOH集合
     */
    List<PreBoh> toPreBohs(List<ActualBoh> actualBohs);

    /**
     * List<PreBoh> 转为 List<ActualBoh>
     *
     * @param preBohs 预处理BOH集合
     * @return 实际BOH集合
     */
    List<ActualBoh> toActualBoh(List<PreBoh> preBohs);
}
