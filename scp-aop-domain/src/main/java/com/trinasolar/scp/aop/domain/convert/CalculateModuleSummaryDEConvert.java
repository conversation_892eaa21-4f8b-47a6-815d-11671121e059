package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CellModuleAllocateSummaryDTO;
import com.trinasolar.scp.aop.domain.dto.CellModuleAllocateSummaryExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * <AUTHOR>
 * @Date 2024/1/4
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateModuleSummaryDEConvert extends BaseDEConvert<CellModuleAllocateSummaryExportExcelDTO, CellModuleAllocateSummaryDTO> {
}
