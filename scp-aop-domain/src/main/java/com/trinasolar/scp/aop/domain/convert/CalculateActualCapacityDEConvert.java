package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CalculateActualEohDTO;
import com.trinasolar.scp.aop.domain.entity.CalculateActualCapacity;
import com.trinasolar.scp.aop.domain.entity.CalculateActualEoh;
import com.trinasolar.scp.aop.domain.entity.PreCapacityIe;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 计算实际EOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-20 17:14:21
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CalculateActualCapacityDEConvert extends BaseDEConvert<PreCapacityIe, CalculateActualCapacity> {

    CalculateActualCapacityDEConvert INSTANCE = Mappers.getMapper(CalculateActualCapacityDEConvert.class);

    List<CalculateActualCapacity> toActual(List<PreCapacityIe> list);
}
