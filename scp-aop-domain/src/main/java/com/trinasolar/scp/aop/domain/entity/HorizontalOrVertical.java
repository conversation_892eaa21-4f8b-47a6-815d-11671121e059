package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/6/29 13:47
 * @apiNote
 */
@Entity
@ToString
@Data
@Where(clause = " is_deleted=0 ")
@Table(name = "aop_horizontal_or_vertical")
@SQLDelete(sql = "UPDATE aop_horizontal_or_vertical SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_horizontal_or_vertical SET is_deleted = 1 WHERE id = ?")
public class HorizontalOrVertical extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 产地：OVERSEA/INLAND
     */
    @ApiModelProperty(value = "产地：OVERSEA/INLAND")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品系列
     */
    @Column(name = "product_series")
    @ApiModelProperty("产品系列")
    private String productSeries;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * Q1比例
     */
    @ApiModelProperty(value = "Q1比例")
    @Column(name = "q1_percent")
    private BigDecimal q1Percent;

    /**
     * Q2比例
     */
    @ApiModelProperty(value = "Q2比例")
    @Column(name = "q2_percent")
    private BigDecimal q2Percent;

    /**
     * Q3比例
     */
    @ApiModelProperty(value = "Q3比例")
    @Column(name = "q3_percent")
    private BigDecimal q3Percent;

    /**
     * Q4比例
     */
    @ApiModelProperty(value = "Q4比例")
    @Column(name = "q4_percent")
    private BigDecimal q4Percent;

    @ApiModelProperty(value = "S&OP版本号")
    @Column(name = "data_version")
    private String dataVersion;

    public void perfect() {
        if (ObjectUtils.isEmpty(this.getQ1Percent())) {
            this.setQ1Percent(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ2Percent())) {
            this.setQ2Percent(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ3Percent())) {
            this.setQ3Percent(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getQ4Percent())) {
            this.setQ4Percent(BigDecimal.ZERO);
        }
    }
}
