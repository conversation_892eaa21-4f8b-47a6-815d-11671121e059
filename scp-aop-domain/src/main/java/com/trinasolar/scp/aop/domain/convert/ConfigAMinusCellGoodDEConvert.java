package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ConfigAMinusCellGoodDTO;
import com.trinasolar.scp.aop.domain.entity.ConfigAMinusCellGood;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * A-良率行表 DTO与实体转换器
 *
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ConfigAMinusCellGoodDEConvert extends BaseDEConvert<ConfigAMinusCellGoodDTO, ConfigAMinusCellGood> {

    ConfigAMinusCellGoodDEConvert INSTANCE = Mappers.getMapper(ConfigAMinusCellGoodDEConvert.class);

}
