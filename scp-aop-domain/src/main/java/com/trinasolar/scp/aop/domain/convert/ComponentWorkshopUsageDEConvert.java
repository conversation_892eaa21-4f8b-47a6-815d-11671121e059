package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.ComponentWorkshopUsageDTO;
import com.trinasolar.scp.aop.domain.entity.ComponentWorkshopUsage;
import com.trinasolar.scp.aop.domain.excel.ComponentWorkshopUsageExportExcelDTO;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 期初库存 转换器
 */
@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ComponentWorkshopUsageDEConvert extends BaseDEConvert<ComponentWorkshopUsageDTO, ComponentWorkshopUsage> {
    ComponentWorkshopUsageDEConvert INSTANCE = Mappers.getMapper(ComponentWorkshopUsageDEConvert.class);


    @Mappings({
            @Mapping(source = "moduleWorkshop", target = "componentWorkshop")
    })
    ComponentWorkshopUsageDTO toDto(ComponentWorkshopUsage bean);

    List<ComponentWorkshopUsageDTO> toDto(List<ComponentWorkshopUsage> list);

    ComponentWorkshopUsageExportExcelDTO toExportExcelDTO(ComponentWorkshopUsage dtoList);

    List<ComponentWorkshopUsageExportExcelDTO> toExportExcelDTO(List<ComponentWorkshopUsage> dtoList);
}
