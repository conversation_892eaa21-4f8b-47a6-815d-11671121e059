package com.trinasolar.scp.aop.domain.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Constraint(validatedBy = EnumValueValidator.class)
public @interface EnumValue {

    // 错误是的消息
    String message() default "参数必须为指定的值";

    // 分组检验
    Class<?>[] groups() default {};

    // 负载
    Class<? extends Payload>[] payload() default {};

    // 字符类型的指定值时
    String[] strValues() default {};

    // 数值类型的指定值时
    int[] intValues() default {};

    //枚举类
    Class<? extends Enum<?>>[] enumClasses() default {};

    //枚举类静态验证方法
    String enumMethod() default "isValidValue";
}
