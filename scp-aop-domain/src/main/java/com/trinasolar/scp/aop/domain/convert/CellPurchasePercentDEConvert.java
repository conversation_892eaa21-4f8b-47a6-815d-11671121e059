package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.CellPurchasePercentDTO;
import com.trinasolar.scp.aop.domain.entity.CellPurchasePercent;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 电池外购占比 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-15 11:18:25
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface CellPurchasePercentDEConvert extends BaseDEConvert<CellPurchasePercentDTO, CellPurchasePercent> {

    CellPurchasePercentDEConvert INSTANCE = Mappers.getMapper(CellPurchasePercentDEConvert.class);

}
