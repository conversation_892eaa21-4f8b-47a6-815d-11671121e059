package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.entity.HaCapacity;
import com.trinasolar.scp.aop.domain.entity.HaScheduling;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * ha的EOH表 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-10-31 20:07:37
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface HaCapacitySchedulingDEConvert extends BaseDEConvert<HaScheduling, HaCapacity> {

    HaCapacitySchedulingDEConvert INSTANCE = Mappers.getMapper(HaCapacitySchedulingDEConvert.class);

    List<HaScheduling> toScheduling(List<HaCapacity> haCapacityList);
}
