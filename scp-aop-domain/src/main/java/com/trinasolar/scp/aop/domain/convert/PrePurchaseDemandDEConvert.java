package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.PrePurchaseDemandDTO;
import com.trinasolar.scp.aop.domain.entity.PrePurchaseDemand;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 预处理外购目标/反馈/指定 DTO与实体转换器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-06 14:51:23
 */
@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface PrePurchaseDemandDEConvert extends BaseDEConvert<PrePurchaseDemandDTO, PrePurchaseDemand> {

    PrePurchaseDemandDEConvert INSTANCE = Mappers.getMapper(PrePurchaseDemandDEConvert.class);

}
