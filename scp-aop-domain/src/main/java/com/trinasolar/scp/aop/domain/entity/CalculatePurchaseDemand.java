package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.common.api.base.BasePO;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLDeleteAll;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 外购目标
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-27 11:24:53
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_purchase_demand")
@Where(clause = " is_deleted=0 ")
@SQLDelete(sql = "UPDATE aop_calculate_purchase_demand SET is_deleted = 1 WHERE id = ?")
@SQLDeleteAll(sql = "UPDATE aop_calculate_purchase_demand SET is_deleted = 1 WHERE id = ?")
@EqualsAndHashCode(callSuper = false, of = {"countryFlag", "productSeries", "productType", "year", "fromType"})
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculatePurchaseDemand extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig", strategy = GenerationType.SEQUENCE)
    @GenericGenerator(name = "SnowflakeIdGeneratorConfig", strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外
     */
    @ApiModelProperty(value = "国内/海外")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 数据版本号
     */
    @ApiModelProperty(value = "数据版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 最新数据标记，Y/N
     */
    @ApiModelProperty(value = "最新数据标记，Y/N")
    @Column(name = "latest_flag")
    private String latestFlag;

    /**
     * 版本类型
     */
    @ApiModelProperty(value = "版本类型")
    @Column(name = "version_type")
    private String versionType;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    @Column(name = "business_type")
    private String businessType;

    /**
     * 计算版本
     */
    @ApiModelProperty(value = "计算版本")
    @Column(name = "calculate_version")
    private String calculateVersion;

    /**
     * 产品类型：电池/组件/硅片/物料/硅料/
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片/物料/硅料/")
    @Column(name = "product_type")
    private String productType;

    /**
     * 产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等
     */
    @ApiModelProperty(value = "产品来源：IE/OEM/采购/国内转海外/海外转国内/通合电池等")
    @Column(name = "product_from")
    private String productFrom;

    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 1月需求量
     */
    @ApiModelProperty(value = "1月需求量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月需求量
     */
    @ApiModelProperty(value = "2月需求量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月需求量
     */
    @ApiModelProperty(value = "3月需求量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月需求量
     */
    @ApiModelProperty(value = "4月需求量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月需求量
     */
    @ApiModelProperty(value = "5月需求量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月需求量
     */
    @ApiModelProperty(value = "6月需求量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月需求量
     */
    @ApiModelProperty(value = "7月需求量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月需求量
     */
    @ApiModelProperty(value = "8月需求量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月需求量
     */
    @ApiModelProperty(value = "9月需求量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月需求量
     */
    @ApiModelProperty(value = "10月需求量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月需求量
     */
    @ApiModelProperty(value = "11月需求量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月需求量
     */
    @ApiModelProperty(value = "12月需求量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 来源类型：反馈/指定
     */
    @ApiModelProperty(value = "来源类型：反馈/指定/目标")
    @Column(name = "from_type")
    private String fromType;

    /**
     * 原ID，调整用
     */
    @ApiModelProperty(value = "原ID，调整用")
    @Column(name = "old_id")
    private Long oldId;


    @ApiModelProperty(value = "实际月份")
    @Column(name = "actual_month")
    private Integer actualMonth;

    @ApiModelProperty(value = "排产月份")
    @Column(name = "plan_month")
    private Integer planMonth;

    @Transient
    private String cellModel;

    public void selectQuery(CalculatePurchaseDemand dto) {
        //组件OEM 导入
        if (ProductTypeEnum.MODULE.getCode().equals(dto.getProductType())) {
            if (StringUtils.isBlank(dto.getCountryFlag()) || StringUtils.isBlank(dto.getProductSeries()) || dto.getYear() == null) {
                throw new BizException("数据异常");
            }
            setProductFrom(ProductFromEnum.OEM.getCode());
            setProductType(dto.getProductType());
        } else {
            //电池/硅片 导入
            if (StringUtils.isBlank(dto.getProductSeries()) || StringUtils.isBlank(dto.getFromType()) || StringUtils.isBlank(dto.getCountryFlag()) || dto.getYear() == null) {
                throw new BizException("数据异常");
            }
            setProductFrom(ProductFromEnum.PURCHASE.getCode());
            setProductType(dto.getProductType());
        }

        setCountryFlag(dto.getCountryFlag());
        setLatestFlag("Y");
        setFromType(dto.getFromType());
        setCalculateVersion(dto.getCalculateVersion());
        if (ObjectUtils.isNotEmpty(dto.getProductSeries())) {
            setProductSeries(dto.getProductSeries());
        }
        if (ObjectUtils.isNotEmpty(dto.getYear())) {
            setYear(dto.getYear());
        }
        setUnit(dto.getUnit());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());
        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());
        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());
        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());
    }

    public void setMothData(CalculatePurchaseDemand dto) {
        setCountryFlag(dto.getCountryFlag());
        setLatestFlag("Y");
        setFromType(dto.getFromType());
        setCalculateVersion(dto.getCalculateVersion());
        if (ObjectUtils.isNotEmpty(dto.getProductType())) {
            setProductType(dto.getProductType());
        }
        setProductFrom(dto.getProductFrom());
        setProductSeries(dto.getProductSeries());
        setYear(dto.getYear());
        setUnit(dto.getUnit());

        setM1Quantity(dto.getM1Quantity());
        setM2Quantity(dto.getM2Quantity());
        setM3Quantity(dto.getM3Quantity());
        setM4Quantity(dto.getM4Quantity());
        setM5Quantity(dto.getM5Quantity());
        setM6Quantity(dto.getM6Quantity());
        setM7Quantity(dto.getM7Quantity());
        setM8Quantity(dto.getM8Quantity());
        setM9Quantity(dto.getM9Quantity());
        setM10Quantity(dto.getM10Quantity());
        setM11Quantity(dto.getM11Quantity());
        setM12Quantity(dto.getM12Quantity());
    }

    /**
     * 设置LovName值
     *
     * @param lovMap
     * <AUTHOR>
     */
    public void setLovName(Map<String, LovLineDTO> lovMap) {
        //国内/海外
        if (lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()) != null) {
            setCountryFlag(lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag()).getLovName());
        }

        //产品类型
        if (lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()) != null) {
            setProductType(lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType()).getLovName());
        }

        //单位
        if (lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit()) != null) {
            setProductType(lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit()).getLovName());
        }

        //来源类型
        if (lovMap.get(LovHeaderCodeConstant.AOP_FROM_TYPE + getFromType()) != null) {
            setProductType(lovMap.get(LovHeaderCodeConstant.AOP_FROM_TYPE + getFromType()).getLovName());
        }
    }

    public void fillLovValue(Map<String, LovLineDTO> lovMap) {
        LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + getCountryFlag());
        LovLineDTO productTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_PRODUCT_TYPE + getProductType());
        LovLineDTO unitLov = lovMap.get(LovHeaderCodeConstant.AOP_UOM + getUnit());
        LovLineDTO fromTypeLov = lovMap.get(LovHeaderCodeConstant.AOP_FROM_TYPE + getFromType());

        //国内/海外
        if (countryFlagLov == null) {
            throw new BizException(String.format("国内/海外的值集不存在---->{%s}", getFromType()));
        }
        //产品类型
        if (productTypeLov == null) {
            throw new BizException(String.format("产品类型的值集不存在---->{%s}", getProductType()));
        }

        //单位
        if (unitLov == null) {
            throw new BizException(String.format("单位的值集不存在---->{%s}", getUnit()));
        }

        //来源类型
        if (fromTypeLov == null) {
            throw new BizException(String.format("来源类型的值集不存在---->{%s}", getFromType()));
        }

        setCountryFlag(countryFlagLov.getLovValue());
        setProductType(productTypeLov.getLovValue());
        setUnit(unitLov.getLovValue());
        setFromType(fromTypeLov.getLovValue());
    }

    public void round() {
        if (ObjectUtils.isNotEmpty(this.getM1Quantity())) {
            this.setM1Quantity(this.getM1Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM2Quantity())) {
            this.setM2Quantity(this.getM2Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM3Quantity())) {
            this.setM3Quantity(this.getM3Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM4Quantity())) {
            this.setM4Quantity(this.getM4Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM5Quantity())) {
            this.setM5Quantity(this.getM5Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM6Quantity())) {
            this.setM6Quantity(this.getM6Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM7Quantity())) {
            this.setM7Quantity(this.getM7Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM8Quantity())) {
            this.setM8Quantity(this.getM8Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM9Quantity())) {
            this.setM9Quantity(this.getM9Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM10Quantity())) {
            this.setM10Quantity(this.getM10Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM11Quantity())) {
            this.setM11Quantity(this.getM11Quantity().setScale(6, RoundingMode.HALF_UP));
        }
        if (ObjectUtils.isNotEmpty(this.getM12Quantity())) {
            this.setM12Quantity(this.getM12Quantity().setScale(6, RoundingMode.HALF_UP));
        }
    }

    public void perfect() {
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }

    public void initZero() {
        this.setM1Quantity(BigDecimal.ZERO);
        this.setM2Quantity(BigDecimal.ZERO);
        this.setM3Quantity(BigDecimal.ZERO);
        this.setM4Quantity(BigDecimal.ZERO);
        this.setM5Quantity(BigDecimal.ZERO);
        this.setM6Quantity(BigDecimal.ZERO);
        this.setM7Quantity(BigDecimal.ZERO);
        this.setM8Quantity(BigDecimal.ZERO);
        this.setM9Quantity(BigDecimal.ZERO);
        this.setM10Quantity(BigDecimal.ZERO);
        this.setM11Quantity(BigDecimal.ZERO);
        this.setM12Quantity(BigDecimal.ZERO);
    }

    public void setNullZero() {
        if (ObjectUtils.isEmpty(this.getM1Quantity())) {
            this.setM1Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM2Quantity())) {
            this.setM2Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM3Quantity())) {
            this.setM3Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM4Quantity())) {
            this.setM4Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM5Quantity())) {
            this.setM5Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM6Quantity())) {
            this.setM6Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM7Quantity())) {
            this.setM7Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM8Quantity())) {
            this.setM8Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM9Quantity())) {
            this.setM9Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM10Quantity())) {
            this.setM10Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM11Quantity())) {
            this.setM11Quantity(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(this.getM12Quantity())) {
            this.setM12Quantity(BigDecimal.ZERO);
        }
    }

    public boolean judgeZero() {
        if (this.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM2Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM3Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM4Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM5Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM6Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM7Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM8Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM9Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM10Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM11Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        if (this.getM12Quantity().compareTo(BigDecimal.ZERO) != 0) {
            return true;
        }
        return false;
    }
}
