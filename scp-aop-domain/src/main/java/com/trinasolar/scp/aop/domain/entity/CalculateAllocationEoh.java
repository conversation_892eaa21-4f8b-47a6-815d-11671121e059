package com.trinasolar.scp.aop.domain.entity;

import com.trinasolar.scp.common.api.base.BasePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计算库存EOH表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-28 16:00:20
 */
@Entity
@ToString
@Data
@Table(name = "aop_calculate_allocation_eoh")
@Where(clause = " is_deleted=0 ")
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CalculateAllocationEoh extends BasePO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Id
    @GeneratedValue(generator = "SnowflakeIdGeneratorConfig",
            strategy = GenerationType.SEQUENCE)
    @GenericGenerator(
            name = "SnowflakeIdGeneratorConfig",
            strategy = "com.trinasolar.scp.common.api.configure.SnowflakeIdGeneratorConfig")
    @ApiModelProperty(value = "主键")
    @Column(name = "id")
    private Long id;

    /**
     * 国内/海外：inland/oversea
     */
    @ApiModelProperty(value = "国内/海外：inland/oversea")
    @Column(name = "country_flag")
    private String countryFlag;

    /**
     * 产品类型：电池/组件/硅片
     */
    @ApiModelProperty(value = "产品类型：电池/组件/硅片")
    @Column(name = "product_type")
    private String productType;
    /**
     * 产品系列
     */
    @ApiModelProperty(value = "产品系列")
    @Column(name = "product_series")
    private String productSeries;

    /**
     * 区域
     */
    @ApiModelProperty(value = "区域")
    @Column(name = "area")
    private String area;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    @Column(name = "data_version")
    private String dataVersion;

    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    @Column(name = "year")
    private Integer year;

    /**
     * q1Boh
     */
    @ApiModelProperty(value = "q1Boh")
    @Column(name = "q1_boh_quantity")
    private BigDecimal q1BohQuantity;
    /**
     * 1月EOH量
     */
    @ApiModelProperty(value = "1月EOH量")
    @Column(name = "m1_quantity")
    private BigDecimal m1Quantity;

    /**
     * 2月EOH量
     */
    @ApiModelProperty(value = "2月EOH量")
    @Column(name = "m2_quantity")
    private BigDecimal m2Quantity;

    /**
     * 3月EOH量
     */
    @ApiModelProperty(value = "3月EOH量")
    @Column(name = "m3_quantity")
    private BigDecimal m3Quantity;

    /**
     * 4月EOH量
     */
    @ApiModelProperty(value = "4月EOH量")
    @Column(name = "m4_quantity")
    private BigDecimal m4Quantity;

    /**
     * 5月EOH量
     */
    @ApiModelProperty(value = "5月EOH量")
    @Column(name = "m5_quantity")
    private BigDecimal m5Quantity;

    /**
     * 6月EOH量
     */
    @ApiModelProperty(value = "6月EOH量")
    @Column(name = "m6_quantity")
    private BigDecimal m6Quantity;

    /**
     * 7月EOH量
     */
    @ApiModelProperty(value = "7月EOH量")
    @Column(name = "m7_quantity")
    private BigDecimal m7Quantity;

    /**
     * 8月EOH量
     */
    @ApiModelProperty(value = "8月EOH量")
    @Column(name = "m8_quantity")
    private BigDecimal m8Quantity;

    /**
     * 9月EOH量
     */
    @ApiModelProperty(value = "9月EOH量")
    @Column(name = "m9_quantity")
    private BigDecimal m9Quantity;

    /**
     * 10月EOH量
     */
    @ApiModelProperty(value = "10月EOH量")
    @Column(name = "m10_quantity")
    private BigDecimal m10Quantity;

    /**
     * 11月EOH量
     */
    @ApiModelProperty(value = "11月EOH量")
    @Column(name = "m11_quantity")
    private BigDecimal m11Quantity;

    /**
     * 12月EOH量
     */
    @ApiModelProperty(value = "12月EOH量")
    @Column(name = "m12_quantity")
    private BigDecimal m12Quantity;

    /**
     * 来源（计算/调整）
     */
    @ApiModelProperty(value = "来源")
    @Column(name = "source")
    private String source;

}
