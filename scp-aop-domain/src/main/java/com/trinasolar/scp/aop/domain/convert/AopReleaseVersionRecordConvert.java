package com.trinasolar.scp.aop.domain.convert;

import com.trinasolar.scp.aop.domain.dto.AopReleaseVersionRecordResDTO;
import com.trinasolar.scp.aop.domain.entity.AopReleaseVersionRecord;
import com.trinasolar.scp.common.api.convert.BaseDEConvert;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface AopReleaseVersionRecordConvert extends BaseDEConvert<AopReleaseVersionRecordResDTO, AopReleaseVersionRecord> {
}