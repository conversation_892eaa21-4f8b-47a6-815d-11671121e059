package com.trinasolar.scp.aop.api.controller;

import com.trinasolar.scp.aop.domain.dto.ActualBohDTO;
import com.trinasolar.scp.aop.domain.dto.GeneralSummaryDTO;
import com.trinasolar.scp.aop.domain.query.ActualBohQuery;
import com.trinasolar.scp.aop.domain.query.ActualBohSync;
import com.trinasolar.scp.aop.domain.save.ActualBohSaveDTO;
import com.trinasolar.scp.aop.service.service.ActualBohService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.ExcelUtils;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实际BOH 前端控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-25 09:20:35
 */
@RestController
@RequestMapping("/actual-boh")
@Api(value = "actual-boh", tags = "实际BOH操作")
public class ActualBohController {
    @Autowired
    ActualBohService actualBohService;

    /**
     * 组件期初库存页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/module/page")
    @ApiOperation(value = "组件期初库存页列表", notes = "组件期初库存页列表")
    public ResponseEntity<Results<Page<GeneralSummaryDTO>>> queryByModulePage(@RequestBody ActualBohQuery query) {
        query.setProductType(ProductTypeEnum.MODULE);
        return Results.createSuccessRes(actualBohService.queryByModulePage(query));
    }

    /**
     * 电池/硅片 期初库存页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/page")
    @ApiOperation(value = "电池/硅片 期初库存页列表", notes = "电池/硅片 期初库存页列表")
    public ResponseEntity<Results<Page<GeneralSummaryDTO>>> queryByCellPage(@RequestBody ActualBohQuery query) {
        return Results.createSuccessRes(actualBohService.queryByPage(query));
    }


    /**
     * 通过主键查询单条数据
     *
     * @param idDTO 主键
     * @return 单条数据
     */
    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<ActualBohDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(actualBohService.queryById(Long.parseLong(idDTO.getId())));
    }

    /**
     * 新增或更新数据
     *
     * @param saveDTO save实体
     * @return 新增或更新数据结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<ActualBohDTO>> save(@Valid @RequestBody ActualBohSaveDTO saveDTO) {
        return Results.createSuccessRes(actualBohService.save(saveDTO));
    }

    /**
     * 批量逻辑删除数据
     *
     * @param idsDTO 主键
     * @return 删除是否成功
     */
    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        actualBohService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    /**
     * 组件期初库存导出
     *
     * @param query 筛选条件
     */
    @PostMapping("/module/export")
    @ApiOperation(value = "组件期初库存导出", notes = "组件期初库存导出")
    public void moduleExport(@RequestBody ActualBohQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        query.setProductType(ProductTypeEnum.MODULE);
        Page<GeneralSummaryDTO> page = actualBohService.queryByModulePage(query);
        String fileName = "组件期初库存";

        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        List<List<Object>> objList = ExcelUtils.getList(page.getContent(), excelPara);
        ExcelUtils.exportEx(response, fileName, fileName, simpleHeader, objList);
    }

    /**
     * 电池/硅片 期初库存页列表
     *
     * @param query 筛选条件
     * @return 查询结果
     */
    @PostMapping("/cellWafer/export")
    @ApiOperation(value = "电池/硅片 期初库存页列表", notes = "电池/硅片 期初库存页列表")
    public void cellWaferExport(@RequestBody ActualBohQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        Page<GeneralSummaryDTO> page = actualBohService.queryByPage(query);
        String fileName = "";
        if (ProductTypeEnum.WAFER.getCode().equals(query.getProductType().getCode())) {
            fileName = "硅片期初库存";
        } else if (ProductTypeEnum.CELL.getCode().equals(query.getProductType().getCode())) {
            fileName = "电池期初库存";
        }

        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        List<List<Object>> objList = ExcelUtils.getList(page.getContent(), excelPara);
        ExcelUtils.exportEx(response, fileName, fileName, simpleHeader, objList);
    }

    @PostMapping("/module/summary")
    @ApiOperation(value = "组件期初库存汇总", notes = "组件期初库存汇总")
    public ResponseEntity<Results<Page<GeneralSummaryDTO>>> queryModuleSummaryPage(@RequestBody ActualBohQuery query) {
        query.setProductType(ProductTypeEnum.MODULE);
        return Results.createSuccessRes(actualBohService.queryModuleSummaryPage(query));
    }

    @PostMapping("/syncActualBohDataJobHandler")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<Object>> syncTJActualSales2SCP(@RequestBody ActualBohSync actualBohSync) {
        String month = actualBohSync.getMonth();
        String year = actualBohSync.getYear();
        Boolean flag = actualBohSync.getFlag();
        if ((year == null || month == null)) {
            year = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy"));
            month = LocalDate.now().format(DateTimeFormatter.ofPattern("MM"));
        }
        actualBohService.syncActualBohData(year, month, flag);
        return Results.createSuccessRes();
    }

    @GetMapping("/syncActualBohDataNew")
    @ApiOperation(value = "组件期初库存页列表", notes = "组件期初库存页列表")
    @SneakyThrows
    public ResponseEntity<Results<Object>> syncActualBohDataNew(String jobParam) {
        LocalDate inventoryDate = LocalDate.now().withDayOfMonth(1);
        if (!StringUtils.isEmpty(jobParam)) {
            if (!this.isValidDate(jobParam, "yyyyMM") && !this.isValidDate(jobParam, "yyyy-MM")) {
                throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
            }
            DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                    .appendPattern(this.isValidDate(jobParam, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                    .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                    .toFormatter();
            inventoryDate = LocalDate.parse(jobParam, formatter);
        }
        actualBohService.syncActualBohDataNew(inventoryDate);
        return Results.createSuccessRes();
    }
    public static boolean isValidDate(String date, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            formatter.parse(date);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }
}
