package com.trinasolar.scp.aop.api.controller;

import com.trinasolar.scp.aop.domain.dto.CalculateDTO;
import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceCalculateDTO;
import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceCheckDTO;
import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceDTO;
import com.trinasolar.scp.aop.domain.query.CalculateQuery;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.save.CellWaferWeightMaintenanceSaveDTO;
import com.trinasolar.scp.aop.service.service.AdjustService;
import com.trinasolar.scp.aop.service.service.CellWaferWeightMaintenanceService;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.IdDTO;
import com.trinasolar.scp.common.api.base.IdsDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 电池硅片权重维护 前端控制器
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/cell-wafer-weight-maintenance")
@Api(value = "/cell-wafer-weight-maintenance", tags = "电池硅片权重维护")
public class CellWaferWeightMaintenanceController{

    @Autowired
    private CellWaferWeightMaintenanceService cellWaferWeightMaintenanceService;

    @Autowired
    private AdjustService adjustService;

    @PostMapping("/page")
    @ApiOperation(value = "电池硅片权重分页列表", notes = "获得电池硅片权重分页列表")
    public ResponseEntity<Results<Page<CellWaferWeightMaintenanceDTO>>> queryByPage(@RequestBody CellWaferWeightMaintenanceQuery query) {
        return Results.createSuccessRes(cellWaferWeightMaintenanceService.queryByPage(query));
    }

    @PostMapping("/detail")
    @ApiOperation(value = "通过主键查询单条数据")
    public ResponseEntity<Results<CellWaferWeightMaintenanceDTO>> queryById(@RequestBody IdDTO idDTO) {
        return Results.createSuccessRes(cellWaferWeightMaintenanceService.queryById(Long.parseLong(idDTO.getId())));
    }

    @Deprecated
    @PostMapping("/save")
    @ApiOperation(value = "新增或更新数据")
    public ResponseEntity<Results<CellWaferWeightMaintenanceDTO>> save(@Valid @RequestBody CellWaferWeightMaintenanceSaveDTO saveDTO) {
        return Results.createSuccessRes(cellWaferWeightMaintenanceService.save(saveDTO));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "批量逻辑删除数据")
    public ResponseEntity<Results<Object>> batchDelete(@RequestBody IdsDTO idsDTO) {
        cellWaferWeightMaintenanceService.logicDeleteByIds(idsDTO.getIds().stream().map(idDTO -> Long.parseLong(idDTO.getId())).collect(Collectors.toList()));
        return Results.createSuccessRes();
    }

    @PostMapping("/export")
    @ApiOperation(value = "导出")
    public void export(@RequestBody CellWaferWeightMaintenanceQuery query, HttpServletResponse response) {
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        cellWaferWeightMaintenanceService.export(query, response);
    }

    @PostMapping("/checkImport")
    @ApiOperation(value = "校验导入数据")
    public ResponseEntity<Results<CellWaferWeightMaintenanceCheckDTO>> checkImport(@RequestPart("file") MultipartFile file,
                                                                                   @RequestPart(value = "excelPara") ExcelPara excelPara,
                                                                                   @RequestParam(value = "productType") ProductTypeEnum productType) {
        return Results.createSuccessRes(cellWaferWeightMaintenanceService.checkImport(file, excelPara,productType));
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入")
    public ResponseEntity<Results<String>> importData(@RequestPart("file") MultipartFile file,
                                                      @RequestPart(value = "excelPara") ExcelPara excelPara,
                                                      @RequestParam(value = "productType") ProductTypeEnum productType) {
        return Results.createSuccessRes(cellWaferWeightMaintenanceService.importData(file,excelPara, productType));
    }

    @PostMapping("/getAllVersion")
    @ApiOperation(value = "点击计算按钮，查询S&OP版本", notes = "点击计算按钮，查询S&OP版本")
    public ResponseEntity<Results<Page<CalculateDTO>>> getAllVersion() {
        CalculateQuery query = new CalculateQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        return Results.createSuccessRes(adjustService.versionRecord(query));
    }

    @PostMapping("/calculate")
    @ApiOperation(value = "电池硅片权重计算", notes = "获得电池硅片权重计算")
    public ResponseEntity<Results<Object>> calculate(@RequestBody CellWaferWeightMaintenanceCalculateDTO calculateDTO) {
        Map<String, String> calculate = cellWaferWeightMaintenanceService.calculate(calculateDTO);
        if(!calculate.isEmpty()){
            return Results.createSuccessRes(calculate);
        }
        return Results.createSuccessRes();
    }
}
