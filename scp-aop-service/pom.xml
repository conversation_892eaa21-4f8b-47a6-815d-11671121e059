<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>scp-aop</artifactId>
        <groupId>com.trinasolar.scp</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <scp-common-api.version>1.0-SNAPSHOT</scp-common-api.version>
    </properties>

    <artifactId>scp-aop-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.trinasolar.scp</groupId>
            <artifactId>scp-aop-domain</artifactId>
            <version>${parent.version}</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.ibm.dpf</groupId>-->
<!--            <artifactId>dpf-common-rest</artifactId>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi-ooxml</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi-ooxml-schemas</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>poi</artifactId>-->
<!--                    <groupId>org.apache.poi</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.ibm.dpf</groupId>-->
<!--            <artifactId>dpf-common-redis</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.ibm.dpf</groupId>-->
<!--            <artifactId>dpf-common-cloud</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.ibm.dpf</groupId>-->
<!--            <artifactId>dpf-common-jdbc</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>org.springframework.boot</groupId>-->
<!--            <artifactId>spring-boot-starter-freemarker</artifactId>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;<dependency>&ndash;&gt;-->
<!--        &lt;!&ndash;<groupId>javax.mail</groupId>&ndash;&gt;-->
<!--        &lt;!&ndash;<artifactId>javax.mail-api</artifactId>&ndash;&gt;-->
<!--        &lt;!&ndash;</dependency>&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.sun.mail</groupId>-->
<!--            <artifactId>javax.mail</artifactId>-->
<!--            <version>1.6.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>commons-net</groupId>-->
<!--            <artifactId>commons-net</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.sun.activation</groupId>-->
<!--            <artifactId>javax.activation</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>org.apache.axis</groupId>-->
<!--            <artifactId>axis</artifactId>-->
<!--            <version>1.4</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>javax.xml.rpc</groupId>-->
<!--            <artifactId>javax.xml.rpc-api</artifactId>-->
<!--            <version>1.1.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.zxing</groupId>-->
<!--            <artifactId>core</artifactId>-->
<!--            <version>3.3.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.zxing</groupId>-->
<!--            <artifactId>javase</artifactId>-->
<!--            <version>3.3.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.github.qcloudsms</groupId>-->
<!--            <artifactId>qcloudsms</artifactId>-->
<!--            <version>1.0.6</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.github.binarywang</groupId>-->
<!--            <artifactId>weixin-java-cp</artifactId>-->
<!--            <version>3.1.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>net.sf.json-lib</groupId>-->
<!--            <artifactId>json-lib-ext-spring</artifactId>-->
<!--            <version>1.0.2</version>-->
<!--        </dependency>-->
    </dependencies>
</project>
