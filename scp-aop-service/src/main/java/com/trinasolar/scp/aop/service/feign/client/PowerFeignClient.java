package com.trinasolar.scp.aop.service.feign.client;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.enums.GlobalConstant;
import com.trinasolar.scp.aop.service.feign.fallback.PowerFeignClientFallbackFactory;
import com.trinasolar.scp.common.api.util.Results;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/5
 */
@FeignClient(value = GlobalConstant.APS_SERVICE, path = "scp-aps-api", fallbackFactory = PowerFeignClientFallbackFactory.class)
public interface PowerFeignClient {
    @PostMapping("/power-supply-aop/saveAll")
    @ApiOperation(value = "“批量新增或更新数据", notes = "批量新增或更新数据")
    ResponseEntity<Results<PowerSupplyAopDTO>> saveAll(@RequestBody List<PowerSupplyAopSaveDTO> saveDTOs);

    @PostMapping("/mw-coefficient/allList")
    @ApiOperation(value = "“批量查询全量数据", notes = "批量查询全量数据")
    ResponseEntity<Results<List<MwCoefficientDTO>>> allList();

    @PostMapping("/target-efficiency/page")
    @ApiOperation(value = "“根据条件查询", notes = "根据条件查询")
    ResponseEntity<Results<JSONObject>> getTargetEfficiencyPage(@RequestBody TargetEfficiencyQuery query);

    @PostMapping("/push-battery-efficiency/getBatteryEfficiencyForAop")
    @ApiOperation(value = "“根据月份和电池效率查询", notes = "根据年份和电池效率查询")
    ResponseEntity<Results<List<PushBatteryEfficiencyForAopDTO>>> getBatteryEfficiencyForAop(@RequestBody PushBatteryEfficiencyQuery query);

    @PostMapping("/cell-inventory/page")
    @ApiOperation(value = "“获取期初库存", notes = "获取期初库存")
    ResponseEntity<Results<JSONObject>> getObtainInitialInventory(@RequestBody CellInventoryQuery query);

    @PostMapping("/cell-shipping-days/page")
    @ApiOperation(value = "运输天数分页列表", notes = "运输天数分页列表")
    ResponseEntity<Results<JSONObject>> queryByPage(@RequestBody CellShippingDaysQuery query);

    @PostMapping("/mw-coefficient/list")
    @ApiOperation(value = "运输天数分页列表", notes = "运输天数分页列表")
    ResponseEntity<Results<List<MwCoefficientDTO>>> listMW(@RequestBody MwCoefficientQuery mwCoefficientQuery);

    @PostMapping("/actual-efficiency-gap/queryByMonth")
    @ApiOperation(value = "实际功率", notes = "实际功率")
    ResponseEntity<Results<List<ActualEfficiencyGapForAopDTO>>> queryByMonth(@RequestBody ActualEfficiencyGapQuery actualEfficiencyGapQuery);

    @PostMapping("/cell-inventory/getCellInventoryForAAOPList")
    @ApiOperation(value = "电池库存", notes = "电池库存")
    ResponseEntity<Results<List<CellInventoryTjForAAOPDTO>>> getCellInventoryForAAOPList(@RequestBody CellInventoryTjForAAOPQuery cellInventoryTjForAAOPQuery);
}
