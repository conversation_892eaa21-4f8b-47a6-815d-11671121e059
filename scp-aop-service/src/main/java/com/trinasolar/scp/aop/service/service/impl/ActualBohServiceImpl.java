package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.aop.domain.constant.CountryMapping;
import com.trinasolar.scp.aop.domain.constant.TJConstant;
import com.trinasolar.scp.aop.domain.convert.ActualBohDEConvert;
import com.trinasolar.scp.aop.domain.convert.ActualBohExtDEConvert;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.dto.aps.CellRelationDTO;
import com.trinasolar.scp.aop.domain.dto.aps.OpActualBohTempDTO;
import com.trinasolar.scp.aop.domain.dto.tj.TjResponse;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.*;
import com.trinasolar.scp.aop.domain.priority.Priority;
import com.trinasolar.scp.aop.domain.query.*;
import com.trinasolar.scp.aop.domain.save.ActualBohSaveDTO;
import com.trinasolar.scp.aop.service.feign.client.*;
import com.trinasolar.scp.aop.service.repository.ActualBohRepository;
import com.trinasolar.scp.aop.service.repository.ProductGroupRepository;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.aop.service.utils.AdjustConstant;
import com.trinasolar.scp.aop.service.utils.IterableUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.common.api.util.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 实际BOH
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-25 09:20:35
 */
@Slf4j
@Service
public class ActualBohServiceImpl implements ActualBohService {
    @Autowired
    ActualBohRepository repository;
    @PersistenceContext
    EntityManager entityManager;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    ActualBohDEConvert actualBohDEConvert;
    @Autowired
    ActualBohExtDEConvert actualBohExtDEConvert;
    @Autowired
    ApsFeignClient apsFeignClient;

    @Autowired
    @Lazy
    CellWaferWeightService cellWaferWeightService;

    @Resource
    private ESBFeignClient esbFeignClient;

    @Resource
    private ProductGroupRepository productGroupRepository;

    @Resource
    private PowerFeignClient powerFeignClient;

    @Resource
    private BomFeign bomFeign;

    @Resource
    private BigdataApiTypeFactory bigdataApiTypeFactory;

    @Resource
    private CellWaferWeightMaintenanceService cellWaferWeightMaintenanceService;

    private static List<String> MODULE_BOH_BU_LIST = Collections.unmodifiableList(Arrays.asList("SPBU", "HQ", "TBU"));

    /**
     * 电池/硅片分页获取实际BOH
     *
     * @param query 查询对象
     * @return 实际BOH分页对象
     */
    @Override
    public Page<GeneralSummaryDTO> queryByPage(ActualBohQuery query) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        JPAQuery<GeneralSummaryDTO> fromSql = factory.select(Projections.fields(
                        GeneralSummaryDTO.class, actualBoh.year.as("year"),
                        actualBoh.productSeries.as("productSeries"),
                        actualBoh.countryFlag.as("countryFlag"),
                        actualBoh.productType.as("productType"),
                        actualBoh.cellShard.as("cellShard"),
                        actualBoh.m1Quantity.sum().as("m1Quantity"),
                        actualBoh.m2Quantity.sum().as("m2Quantity"),
                        actualBoh.m3Quantity.sum().as("m3Quantity"),
                        actualBoh.m4Quantity.sum().as("m4Quantity"),
                        actualBoh.m5Quantity.sum().as("m5Quantity"),
                        actualBoh.m6Quantity.sum().as("m6Quantity"),
                        actualBoh.m7Quantity.sum().as("m7Quantity"),
                        actualBoh.m8Quantity.sum().as("m8Quantity"),
                        actualBoh.m9Quantity.sum().as("m9Quantity"),
                        actualBoh.m10Quantity.sum().as("m10Quantity"),
                        actualBoh.m11Quantity.sum().as("m11Quantity"),
                        actualBoh.m12Quantity.sum().as("m12Quantity")
                ))
                .from(actualBoh);
        BooleanBuilder builder = new BooleanBuilder();

        setFromSql(query, fromSql, builder);

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        JPAQuery<GeneralSummaryDTO> generalSummaryDTOJPAQuery = fromSql.groupBy(actualBoh.year, actualBoh.productSeries, actualBoh.countryFlag, actualBoh.productType, actualBoh.cellShard);

        List<GeneralSummaryDTO> fetch = generalSummaryDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
//        List<GeneralSummaryDTO> cellList = fetch.stream().filter(item -> ProductTypeEnum.CELL.getCode().equals(item.getProductType())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(cellList)) {
//            fetch.removeAll(cellList);
//            List<ActualBohDTO> actualBohDTOS = actualBohExtDEConvert.toDto(cellList);
//            calculateCell(actualBohDTOS);
//            cellList = actualBohExtDEConvert.toEntity(actualBohDTOS);
//            fetch.addAll(cellList);
//        }
        //值集转换 组件期初库存
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        List<Tuple> list = factory.select(actualBoh.year, actualBoh.productSeries, actualBoh.countryFlag, actualBoh.cellShard)
                .from(actualBoh).groupBy(actualBoh.year, actualBoh.productSeries, actualBoh.countryFlag, actualBoh.cellShard).where(builder).fetch();
        fetch.forEach(generalSummaryDTO -> {
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, generalSummaryDTO.getCountryFlag()));
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SHARD, generalSummaryDTO.getCellShard()));
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(generalSummaryDTO -> {
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, generalSummaryDTO.getCountryFlag()));
            LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, generalSummaryDTO.getCellShard()));
            if (Objects.nonNull(lovLineDTO01) && ObjectUtils.isNotEmpty(generalSummaryDTO.getCountryFlag())) {
                generalSummaryDTO.setCountryFlag(lovLineDTO01.getLovName());
            } else {
                throw new BizException(generalSummaryDTO.getCountryFlag() + "值集列表不存在");
            }

            if (ObjectUtils.isNotEmpty(generalSummaryDTO.getCellShard())) {
                if (Objects.nonNull(lovLineDTO02)) {
                    generalSummaryDTO.setCellShardName(lovLineDTO02.getLovName());
                } else {
                    throw new BizException(generalSummaryDTO.getCellShard() + "值集列表不存在");
                }
            }
        });

        for (GeneralSummaryDTO dto : fetch) {
            dto.calculateQuarter();
        }

        return new PageImpl<>(fetch, pageable, list.size());
    }

    /**
     * 组件分页获取实际BOH
     *
     * @param query 查询对象
     * @return 实际BOH分页对象
     */
    @Override
    public Page<GeneralSummaryDTO> queryByModulePage(ActualBohQuery query) {
        QActualBoh qActualBoh = QActualBoh.actualBoh;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<GeneralSummaryDTO> fromSql = jpaQueryFactory
                .select(Projections.fields(
                        GeneralSummaryDTO.class, qActualBoh.year.as("year"),
                        qActualBoh.productSeries.as("productSeries"),
                        qActualBoh.productGroup.as("productGroup"),
                        qActualBoh.countryFlag.as("countryFlag"),
                        qActualBoh.area.as("area"),
                        qActualBoh.projectPlace.as("projectPlace"),
                        qActualBoh.salesChannel.as("salesChannel"),
                        qActualBoh.m1Quantity.sum().as("m1Quantity"),
                        qActualBoh.m2Quantity.sum().as("m2Quantity"),
                        qActualBoh.m3Quantity.sum().as("m3Quantity"),
                        qActualBoh.m4Quantity.sum().as("m4Quantity"),
                        qActualBoh.m5Quantity.sum().as("m5Quantity"),
                        qActualBoh.m6Quantity.sum().as("m6Quantity"),
                        qActualBoh.m7Quantity.sum().as("m7Quantity"),
                        qActualBoh.m8Quantity.sum().as("m8Quantity"),
                        qActualBoh.m9Quantity.sum().as("m9Quantity"),
                        qActualBoh.m10Quantity.sum().as("m10Quantity"),
                        qActualBoh.m11Quantity.sum().as("m11Quantity"),
                        qActualBoh.m12Quantity.sum().as("m12Quantity")
                ))
                .from(qActualBoh);
        BooleanBuilder builder = new BooleanBuilder();
        setFromSql(query, fromSql, builder);

        JPAQuery<GeneralSummaryDTO> generalSummaryDTOJPAQuery = fromSql.groupBy(qActualBoh.year, qActualBoh.productSeries, qActualBoh.productGroup, qActualBoh.countryFlag, qActualBoh.salesChannel, qActualBoh.projectPlace, qActualBoh.area);

        List<GeneralSummaryDTO> fetch = generalSummaryDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //值集转换 组件期初库存
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        List<Tuple> list = jpaQueryFactory.select(qActualBoh.year, qActualBoh.productSeries, qActualBoh.productGroup, qActualBoh.countryFlag, qActualBoh.salesChannel, qActualBoh.projectPlace, qActualBoh.area).from(qActualBoh).where(builder).groupBy(qActualBoh.year, qActualBoh.productSeries, qActualBoh.productGroup, qActualBoh.countryFlag, qActualBoh.salesChannel, qActualBoh.projectPlace, qActualBoh.area).fetch();
        fetch.forEach(generalSummaryDTO -> {
            //区域
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_REGION, generalSummaryDTO.getArea()));
            //项目地
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_PROJECT_PLACE, generalSummaryDTO.getProjectPlace()));
            //销售渠道
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_SALES_CHANNEL, generalSummaryDTO.getSalesChannel()));
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, generalSummaryDTO.getCountryFlag()));
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(generalSummaryDTO -> {

            if (StringUtils.isNotBlank(generalSummaryDTO.getArea())) {
                LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_REGION, generalSummaryDTO.getArea()));

                if (Objects.nonNull(lovLineDTO01)) {
                    generalSummaryDTO.setArea(lovLineDTO01.getLovName());
                }
            }

            if (StringUtils.isNotBlank(generalSummaryDTO.getProjectPlace())) {
                LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_PROJECT_PLACE, generalSummaryDTO.getProjectPlace()));

                if (Objects.nonNull(lovLineDTO02)) {
                    generalSummaryDTO.setProjectPlace(lovLineDTO02.getLovName());
                }
            }
            if (StringUtils.isNotBlank(generalSummaryDTO.getSalesChannel())) {
                LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_SALES_CHANNEL, generalSummaryDTO.getSalesChannel()));
                if (Objects.nonNull(lovLineDTO03)) {
                    generalSummaryDTO.setSalesChannel(lovLineDTO03.getLovName());
                }
            }
            if (StringUtils.isNotBlank(generalSummaryDTO.getCountryFlag())) {
                LovLineDTO lovLineDTO06 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, generalSummaryDTO.getCountryFlag()));
                if (Objects.nonNull(lovLineDTO06)) {
                    generalSummaryDTO.setCountryFlag(lovLineDTO06.getLovName());
                }
            }
        });

        for (GeneralSummaryDTO dto : fetch) {
            dto.calculateQuarter();
        }

        return new PageImpl<>(fetch, pageable, list.size());
    }

    private void setFromSql(ActualBohQuery query, JPAQuery<GeneralSummaryDTO> fromSql, BooleanBuilder builder) {
        QActualBoh qActualBoh = QActualBoh.actualBoh;
        if (ObjectUtils.isNotEmpty(query.getProductType())) {
            fromSql.where(qActualBoh.productType.eq(query.getProductType().getCode()));
            builder.and(qActualBoh.productType.eq(query.getProductType().getCode()));
        }
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            fromSql.where(qActualBoh.productSeries.eq(query.getProductSeries()));
            builder.and(qActualBoh.productSeries.eq(query.getProductSeries()));
        }
        if (StringUtils.isNotBlank(query.getArea())) {
            fromSql.where(qActualBoh.area.eq(query.getArea()));
            builder.and(qActualBoh.area.eq(query.getArea()));
        }
        if (StringUtils.isNotBlank(query.getSalesChannel())) {
            fromSql.where(qActualBoh.salesChannel.eq(query.getSalesChannel()));
            builder.and(qActualBoh.salesChannel.eq(query.getSalesChannel()));
        }
        if (query.getYear() != null) {
            fromSql.where(qActualBoh.year.eq(query.getYear()));
            builder.and(qActualBoh.year.eq(query.getYear()));
        }
        if (StringUtils.isNotBlank(query.getCountryFlag())) {
            fromSql.where(qActualBoh.countryFlag.eq(query.getCountryFlag()));
            builder.and(qActualBoh.countryFlag.eq(query.getCountryFlag()));
        }

    }

    @Override
    public ActualBohDTO queryById(Long id) {
        ActualBoh queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ActualBohDTO result = new ActualBohDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ActualBohDTO save(ActualBohSaveDTO saveDTO) {
        ActualBoh newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ActualBoh());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.forEach(id -> repository.deleteById(id));
    }

    /**
     * 条件查询实际boh
     *
     * @param query
     * @return
     */
    @Override
    public List<ActualBoh> queryActualBohList(ActualBohQuery query) {
        QActualBoh qActualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (ObjectUtils.isNotEmpty(query.getYear())) {
            booleanBuilder.and(qActualBoh.year.eq(query.getYear()));
        }
        if (ObjectUtils.isNotEmpty(query.getProductType())) {
            booleanBuilder.and(qActualBoh.productType.eq(query.getProductType().getCode()));
        }
        if (ObjectUtils.isNotEmpty(query.getCountryFlag())) {
            booleanBuilder.and(qActualBoh.countryFlag.eq(query.getCountryFlag()));
        }
        List<ActualBoh> fetch = factory.selectFrom(qActualBoh).where(booleanBuilder).fetch();
//        if (query.getProductType() == ProductTypeEnum.CELL) {
//            List<ActualBohDTO> actualBohDTOS = actualBohDEConvert.toDto(fetch);
//            calculateCell(actualBohDTOS);
//            fetch = actualBohDEConvert.toEntity(actualBohDTOS);
//        }
        return fetch;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchSave(List<ActualBohDTO> dtoList, String productType) {
        log.info("--------------excel读取数据------------");
        log.info(dtoList.toString());
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BizException("导入数据不能为空");
        }
        if (IterableUtils.checkListData(dtoList)) {
            throw new BizException("Excel中存在重复数据，请检查!");
        }


        HashSet<LovLineQuery> set = new HashSet<>();
        for (ActualBohDTO actualBohDTO : dtoList) {
            set.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, actualBohDTO.getCountryFlag()));
            set.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_PROJECT_PLACE, actualBohDTO.getProjectPlace()));
            set.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_SALES_CHANNEL, actualBohDTO.getSalesChannel()));
            set.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_REGION, actualBohDTO.getArea()));
        }

        Map<String, LovLineDTO> lovMap = LovService.findLovMap(set);


        long distinctSize = dtoList.stream().map(ActualBohDTO::getYear).distinct().count();
        if (distinctSize > 1) {
            throw new BizException("excel中存在多条年份的数据");
        }
        //获取国内/海外
        List<String> countryFlagLovList = dtoList.stream().map(item -> {
            LovLineDTO lov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + item.getCountryFlag());
            if (lov != null) {
                return lov.getLovValue();
            }
            return null;
        }).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        ActualBohDTO actualBohDTO = dtoList.get(0);

        repository.deleteAllByCountryFlagInAndYearAndProductType(countryFlagLovList, actualBohDTO.getYear(), productType);


        // 存储保存数据的集合
        List<ActualBoh> saveList = Lists.newArrayList();
        List<String> errorList = Lists.newArrayList();
        int count = 0;

        List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();

        //电池/硅片 产品系列
        Set<String> waferSizeSet = productInfoList.stream().map(ProductInfo::getWaferModel).collect(Collectors.toSet());
        Set<String> cellModelSet = productInfoList.stream().map(ProductInfo::getCellModel).collect(Collectors.toSet());
        for (ActualBohDTO dto : dtoList) {
            count++;
            if (ProductTypeEnum.MODULE.getCode().equals(productType)) {
                //区域
                LovLineDTO areaLov = lovMap.get(LovHeaderCodeConstant.AOP_REGION + dto.getArea());
                if (areaLov == null) {
                    errorList.add(" 第" + count + "行：" + "销售区域(area)值  错误！");
                    continue;
                }
                dto.setArea(areaLov.getLovValue());


                //项目地
                if (StringUtils.isNotBlank(dto.getProjectPlace())) {
                    LovLineDTO projectPlaceLov = lovMap.get(LovHeaderCodeConstant.AOP_PROJECT_PLACE + dto.getProjectPlace());
                    if (projectPlaceLov == null) {
                        errorList.add(" 第" + count + "行：" + "项目地(projectPlace)值  错误！");
                        continue;
                    }
                    dto.setProjectPlace(projectPlaceLov.getLovValue());
                }
                //销售渠道
                if (StringUtils.isNotBlank(dto.getSalesChannel())) {
                    LovLineDTO salesChannelLov = lovMap.get(LovHeaderCodeConstant.AOP_SALES_CHANNEL + dto.getSalesChannel());
                    if (salesChannelLov == null) {
                        errorList.add(" 第" + count + "行：" + "销售渠道(salesChannel)值  错误！");
                        continue;
                    }
                    dto.setSalesChannel(salesChannelLov.getLovValue());
                }

            } else {
                //电池/硅片
                // 产品系列
                if (!waferSizeSet.contains(dto.getProductSeries())) {
                    errorList.add(" 第" + count + "行：" + "产品系列(ProductSeries)值  错误！");
                    continue;
                }

                if (!cellModelSet.contains(dto.getProductSeries())) {
                    errorList.add(" 第" + count + "行：" + "产品系列(ProductSeries)值  错误！");
                    continue;
                }

            }
            //检验年份
            if (dto.getYear() == null) {
                errorList.add(" 第" + count + "行：" + "年份(year)值 为空值");
                continue;
            }
            //国内/海外
            LovLineDTO countryFlagLov = lovMap.get(LovHeaderCodeConstant.AOP_COUNTRY_FLAG + dto.getCountryFlag());
            if (countryFlagLov == null) {
                errorList.add(" 第" + count + "行：" + "国内/海外(countryFlag)值  错误！");
                continue;
            }
            dto.setCountryFlag(countryFlagLov.getLovValue());

            dto.setProductType(productType);

            ActualBoh entity = actualBohDEConvert.toEntity(dto);
            saveList.add(entity);

        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(errorList.toString());
        }

        if (CollectionUtils.isNotEmpty(saveList)) {
            List<ActualBoh> configInventories = null;
            List<ActualBoh> newActualBohList = BeanUtil.copyToList(saveList, ActualBoh.class);
            configInventories = repository.saveAll(newActualBohList);
            if (CollectionUtils.isEmpty(configInventories)) {
                throw new BizException("ERROR:导入失败！");
            }
        }
    }

    /**
     * 根据年份和产品大类查询实际BOH
     *
     * @param year        年份
     * @param productType 产品大类
     * @return
     */
    @Override
    public List<ActualBoh> queryByYearType(Integer year, ProductTypeEnum productType) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.selectFrom(actualBoh).where(actualBoh.year.eq(year).and(actualBoh.productType.eq(productType.getCode()))).fetch();

    }

    /**
     * 年份、产品大类、国内/海外 查询实际BOH
     *
     * @param year        年份
     * @param productType 产品大类
     * @param countryFlag 国内/海外
     * @return
     */
    @Override
    public List<ActualBoh> queryByYearTypeCountry(Integer year, ProductTypeEnum productType, CountryFlagEnum countryFlag) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        JPAQuery<ActualBoh> query = factory.selectFrom(actualBoh).where(actualBoh.year.eq(year).and(actualBoh.productType.eq(productType.getCode())));
        if (ObjectUtils.isNotEmpty(countryFlag)) {
            query.where(actualBoh.countryFlag.eq(countryFlag.getCode()));
        }
        List<ActualBoh> fetch = query.fetch();
        List<ActualBoh> collect = fetch.stream().filter(item -> !BusinessTypeEnum.MFG.getCode().equals(item.getArea()) && !BusinessTypeEnum.Q2Q3.getCode().equals(item.getArea()) && !BusinessTypeEnum.RMA.getCode().equals(item.getArea())).collect(Collectors.toList());
//        if (productType == ProductTypeEnum.CELL) {
//            List<ActualBohDTO> actualBohDTOS = actualBohDEConvert.toDto(collect);
//            calculateCell(actualBohDTOS);
//            collect = actualBohDEConvert.toEntity(actualBohDTOS);
//        }
        return collect;
    }

    /**
     * 查询实际MFGBOH
     *
     * @param year
     * @param countryFlag
     * @return
     */
    @Override
    public ActualBoh queryMfgBohByYearCountry(Integer year, String countryFlag) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        List<ActualBoh> fetch = factory.selectFrom(actualBoh).where(actualBoh.year.eq(year).and(actualBoh.productType.eq(ProductTypeEnum.MODULE.getCode()))
                .and(actualBoh.countryFlag.eq(countryFlag))
                .and(actualBoh.area.eq(AreaEnums.MFG.getCode()))).fetch();
        if (CollectionUtils.isNotEmpty(fetch)) {
            return fetch.get(0);
        }
        return null;
    }

    /**
     * 查询实际 q2q3 BOH
     *
     * @param year
     * @param countryFlag
     * @return
     */
    @Override
    public ActualBoh queryQ2Q3BohByYearCountry(Integer year, String countryFlag) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        List<ActualBoh> fetch = factory.selectFrom(actualBoh).where(actualBoh.year.eq(year).and(actualBoh.productType.eq(ProductTypeEnum.MODULE.getCode()))
                .and(actualBoh.countryFlag.eq(countryFlag))
                .and(actualBoh.area.eq(AreaEnums.Q2Q3.getCode()))).fetch();
        if (CollectionUtils.isNotEmpty(fetch)) {
            return fetch.get(0);
        }
        return null;
    }

    /**
     * 通过年份查询RMA的BOH
     *
     * @param year
     * @return
     */
    @Override
    public List<ActualBoh> queryRmaBohByYear(Integer year) {
        QActualBoh actualBoh = QActualBoh.actualBoh;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.selectFrom(actualBoh).where(actualBoh.year.eq(year).and(actualBoh.productType.eq(ProductTypeEnum.MODULE.getCode()))
                .and(actualBoh.area.eq(AreaEnums.RMA.getCode()))).fetch();
    }

    /**
     * 硅片实际期初库存导入
     *
     * @param dtoList
     * @param productType
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSave(List<ActualBohDTO> dtoList, ProductTypeEnum productType) {
        List<Integer> yearList = dtoList.stream().map(ActualBohDTO::getYear).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(yearList) || yearList.size() != 1) {
            throw new BizException("导入年份不唯一");
        }
        List<String> errorList = new ArrayList<>();
        int i = 0;
        List<LovLineDTO> cellShard = LovService.findByLovCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        List<String> waferList = productInfoService.queryAllWafer();
        List<String> cellList = productInfoService.queryAllCell();
        Set<String> countryFlag = new HashSet<>();
        for (ActualBohDTO actualBohDTO : dtoList) {
            actualBohDTO.setProductType(productType.getCode());
            i++;
            String code = CountryFlagEnum.getCode(actualBohDTO.getCountryFlag());
            countryFlag.add(code);
            if (StringUtils.isBlank(code)) {
                errorList.add(String.format("第%s行国内/海外值错误！", i));
            } else {
                actualBohDTO.setCountryFlag(code);
            }
            if (productType == ProductTypeEnum.WAFER && !waferList.contains(actualBohDTO.getProductSeries())) {
                errorList.add(String.format("第%s行硅片型号不存在", i));
            }
            if (productType == ProductTypeEnum.CELL && !cellList.contains(actualBohDTO.getProductSeries())) {
                errorList.add(String.format("第%s行电池型号不存在", i));
            }
            if (ObjectUtils.isEmpty(actualBohDTO.getCellShard()) && ObjectUtils.isNotEmpty(actualBohDTO.getCellShardName())) {
                actualBohDTO.setCellShard(actualBohDTO.getCellShardName());
            }
            if (ObjectUtils.isNotEmpty(actualBohDTO.getCellShard()) && productType == ProductTypeEnum.CELL) {
                LovLineDTO lovLineDTO = cellShard.stream().filter(item -> actualBohDTO.getCellShard().equals(item.getLovName())).findFirst().orElse(null);
                if (ObjectUtils.isNotEmpty(lovLineDTO)) {
                    actualBohDTO.setCellShard(lovLineDTO.getLovValue());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(errorList.toString());
        }
        repository.deleteAllByYearAndProductTypeAndCountryFlagIn(yearList.get(0), productType.getCode(), countryFlag);
        List<ActualBoh> actualBohList = actualBohDEConvert.toEntity(dtoList);
        repository.saveAll(actualBohList);
        return "导入成功";
    }

    /**
     * 定时任务同步天玑实际产出数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncActualBohData(String qYear, String qMonth, Boolean syscFlag) {
        final String defaultKeySeparator = ",";
        List<String> cell = productInfoService.queryAllCell().stream().map(item -> {
            String[] split = item.split("-");
            return split[0];
        }).distinct().collect(Collectors.toList());
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        QActualBoh actualBoh = QActualBoh.actualBoh;

        List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
        Map<String, String> lovMap = LovService.findLovName(LovHeaderCodeConstant.AOP_BOH_AREA_MAPPING_TJ);
        List<OpActualBohTemp> opActualBohTempLists = this.loadActualBohTemp(qYear, qMonth, syscFlag, productInfoList, lovMap);
        List<OpActualBohTempDTO> opActualBohTempList = BeanUtils.instantiateClass(opActualBohTempLists.getClass());
        for (OpActualBohTemp source : opActualBohTempLists) {
            OpActualBohTempDTO destination = new OpActualBohTempDTO();
            // 执行属性复制
            BeanUtils.copyProperties(source, destination);
            // 将转换后的对象添加到目标列表
            opActualBohTempList.add(destination);
        }

        opActualBohTempList.forEach(item -> {
            if (item.getMonth() == 12) {
                item.setYear(item.getYear() + 1);
                item.setMonth(1);
            } else {
                item.setMonth(item.getMonth() + 1);
            }
        });

//        if (!syscFlag) {
//            List<OpActualBohTempDTO> erpDataList = loadRemoteDataFromERP(qYear, qMonth, productInfoList, lovMap);
//            opActualBohTempList.addAll(erpDataList);
//        }

        Integer inYear = opActualBohTempList.get(0).getYear();
        Integer inMonth = opActualBohTempList.get(0).getMonth();
        List<Integer> yearList = opActualBohTempList.stream().map(OpActualBohTempDTO::getYear).distinct().collect(Collectors.toList());
        List<ActualBoh> actualBohList = factory.selectFrom(actualBoh).where(actualBoh.year.in(yearList)).fetch();
        List<String> itemNoList = opActualBohTempList.stream().filter(item -> TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType())).map(OpActualBohTempDTO::getItem).distinct().collect(Collectors.toList());
        //查询组件计划，找到电池料号对应电池型号
        List<CellRelationDTO> cellList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemNoList)) {
            ResponseEntity<Results<List<CellRelationDTO>>> response = apsFeignClient.querySeriesByItemNo(itemNoList);
            cellList = response.getBody().getData();
        }
        //料号匹配设置分片方式和电池型号
        cellList = cellList.stream().filter(item -> item.getDescription().contains("Q1") || item.getDescription().contains("A-")).collect(Collectors.toList());
        cellList.forEach(item -> {
            boolean flag = false;
            for (String s : cell) {
                if (item.getCellType().contains(s)) {
                    flag = true;
                }
            }
            if (item.getCellType().contains("二分")) {
                item.setCellShard("Half-cut");
            } else if (item.getCellType().contains("三分")) {
                item.setCellShard("3-cut");
            }
            if (flag) {
                String[] s = item.getCellType().split("_");
                String str = s[2] + "-" + s[1];
                String replace = str.replace("型", "");
                if (replace.contains("156")) {
                    replace = "156-MP";
                }
                if (replace.contains("158")) {
                    replace = "158";
                    item.setCellShard("Half-cut");
                }
                if (replace.contains("166")) {
                    replace = "166";
                }
                item.setCellType(replace);
            } else {
                item.setCellType(null);
            }
        });
        List<CellRelationDTO> finalCellList = cellList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getCellType())).collect(Collectors.toList());
        //电池数据规整化
        opActualBohTempList.stream().filter(item -> TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getItem())).forEach(item -> {
            CellRelationDTO cellRelationDTO = finalCellList.stream().filter(bean -> item.getItem().equals(bean.getMaterialNo())).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(cellRelationDTO)) {
                item.setProductSeries(cellRelationDTO.getCellType());
                item.setCellShard(cellRelationDTO.getCellShard());
            } else {
                item.setProductSeries(null);
            }
            item.setProductGroup(null);
        });
        opActualBohTempList = opActualBohTempList.stream().filter(item -> (TJConstant.ProductType.MODULE.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getProductSeries()))
                || (TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getCellShard()))
                || (TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getProductSeries()) && item.getProductSeries().contains("156"))
                || (TJConstant.ProductType.PREBILLING.getTjCode().equals(item.getFlag()))
                || (TJConstant.ProductType.CLOSED.getTjCode().equals(item.getFlag()))
        ).collect(Collectors.toList());
        Map<String, List<OpActualBohTempDTO>> actualBohTemp = opActualBohTempList.stream().collect(Collectors.groupingBy(item -> {
            //国内海外
            return this.countryFlagReplace(item.getCountryFlag())
                    //产品系列
                    + defaultKeySeparator + item.getProductSeries()
                    //产品族
                    + defaultKeySeparator + item.getProductGroup()
                    //区域
                    + defaultKeySeparator + item.getArea()
                    //销售渠道
                    + defaultKeySeparator + TJConstant.Channel.getByTjCode(item.getSalesChannel())
                    //年份
                    + defaultKeySeparator + item.getYear()
                    //产品大类
                    + defaultKeySeparator + this.productTypeReplace(item.getItemType())
                    //电池分片方式
                    + defaultKeySeparator + item.getCellShard();
        }));
        //同步数据
        List<ActualBoh> allActualBoh = repository.findAll();
        if (!syscFlag) {
            allActualBoh = allActualBoh.stream().filter(item -> item.getYear().equals(inYear)).collect(Collectors.toList());
            allActualBoh.forEach(item ->
                    ReflectUtil.setFieldValue(item, "m" + Integer.valueOf(inMonth) + "Quantity", BigDecimal.ZERO)
            );
            repository.saveAll(allActualBoh);
        } else {
            if (Integer.parseInt(qYear) < LocalDate.now().getYear()) {
                List<ActualBoh> allActualBoh1 = allActualBoh.stream().filter(item -> item.getYear() == Integer.parseInt(qYear)).collect(Collectors.toList());
                for (int i = Integer.parseInt(qMonth); i <= 12; i++) {
                    int in = i;
                    allActualBoh1.forEach(item ->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                List<ActualBoh> allActualBoh2 = allActualBoh.stream().filter(item -> item.getYear() > Integer.parseInt(qYear) && item.getYear() < LocalDate.now().getYear()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(allActualBoh2)) {
                    for (int i = 1; i <= 12; i++) {
                        int in = i;
                        allActualBoh2.forEach(item ->
                                ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                        );
                    }
                }
                List<ActualBoh> allActualBoh3 = allActualBoh.stream().filter(item -> item.getYear() == LocalDate.now().getYear()).collect(Collectors.toList());
                for (int i = 1; i <= LocalDate.now().getMonth().getValue(); i++) {
                    int in = i;
                    allActualBoh3.forEach(item ->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                repository.saveAll(allActualBoh1);
                repository.saveAll(allActualBoh2);
                repository.saveAll(allActualBoh3);
            } else {
                List<ActualBoh> allActualBoh4 = allActualBoh.stream().filter(item -> item.getYear() == Integer.parseInt(qYear)).collect(Collectors.toList());
                for (int i = Integer.parseInt(qMonth); i <= LocalDate.now().getMonth().getValue(); i++) {
                    int in = i;
                    allActualBoh4.forEach(item ->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                repository.saveAll(allActualBoh4);
            }


        }

        for (Map.Entry<String, List<OpActualBohTempDTO>> stringListEntry : actualBohTemp.entrySet()) {
            String key = stringListEntry.getKey();
            String[] keys = key.split(defaultKeySeparator);
            String s = "null";
            String countryFlag = s.equals(keys[0]) ? null : keys[0];
            String productSeries = s.equals(keys[1]) ? null : keys[1];
            String productGroup = s.equals(keys[2]) ? null : keys[2];
            String area = s.equals(keys[3]) ? null : keys[3];
            String salesChannel = s.equals(keys[4]) ? null : keys[4];
            String year = s.equals(keys[5]) ? null : keys[5];
            String productType = s.equals(keys[6]) ? null : keys[6];
            String cellShard = s.equals(keys[7]) ? null : keys[7];

            List<OpActualBohTempDTO> actualBohTempList = stringListEntry.getValue();
            ActualBoh boh = null;
            if (ProductTypeEnum.MODULE.getCode().equals(productType)) {
                boh = actualBohList.stream().filter(item -> area != null && area.equals(item.getArea()) && ProductTypeEnum.MODULE.getCode().equals(item.getProductType()) && countryFlag != null && countryFlag.equals(item.getCountryFlag()) && productGroup.equals(item.getProductGroup()) && year != null && Integer.valueOf(year).equals(item.getYear()) && productSeries != null && productSeries.equals(item.getProductSeries()) && (salesChannel == null || salesChannel.equals(item.getSalesChannel()))).findFirst().orElse(null);
            } else {
                boh = actualBohList.stream().filter(item -> ProductTypeEnum.CELL.getCode().equals(item.getProductType()) && countryFlag != null && countryFlag.equals(item.getCountryFlag()) && year != null && Integer.valueOf(year).equals(item.getYear()) && productSeries != null && productSeries.equals(item.getProductSeries())).findFirst().orElse(null);
            }
            if (ObjectUtils.isNotEmpty(boh)) {
                //ReflectUtil.setFieldValue(boh, "m" + Integer.valueOf(actualBohTempList.get(0).getMonth()) + "Quantity", null);
                for (OpActualBohTempDTO opActualBohTemp : actualBohTempList) {
                    if (opActualBohTemp.getMonth() == 1) {
                        BigDecimal quantity = boh.getM1Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM1Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 2 ) {
                        BigDecimal quantity = boh.getM2Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM2Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 3 ) {
                        BigDecimal quantity = boh.getM3Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM3Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 4 ) {
                        BigDecimal quantity = boh.getM4Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM4Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 5 ) {
                        BigDecimal quantity = boh.getM5Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM5Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 6 ) {
                        BigDecimal quantity = boh.getM6Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM6Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 7) {
                        BigDecimal quantity = boh.getM7Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM7Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 8 ) {
                        BigDecimal quantity = boh.getM8Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM8Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 9 ) {
                        BigDecimal quantity = boh.getM9Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM9Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 10 ) {
                        BigDecimal quantity = boh.getM10Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM10Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 11 ) {
                        BigDecimal quantity = boh.getM11Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM11Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                    if (opActualBohTemp.getMonth() == 12 ) {
                        BigDecimal quantity = boh.getM12Quantity();
                        if (ObjectUtils.isEmpty(quantity)) {
                            quantity = BigDecimal.ZERO;
                        }
                        boh.setM12Quantity(quantity.add(opActualBohTemp.getQuantity()));
                    }
                }
                repository.save(boh);
            } else {
                //数据库中无则新增数据
                ActualBoh boh1 = new ActualBoh();
                boh1.setCountryFlag(countryFlag);
                boh1.setProductSeries(productSeries);
                boh1.setProductGroup(productGroup);
                boh1.setSalesChannel(salesChannel);
                boh1.setArea(area);
                boh1.setYear(Integer.valueOf(year));
                boh1.setProductType(productType);
                boh1.setCellShard(cellShard);
                for (OpActualBohTempDTO bohTemp : actualBohTempList) {
                    String fieldName = "m" + bohTemp.getMonth() + "Quantity";
                    BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(boh1, fieldName);
                    if (ObjectUtils.isEmpty(value)) {
                        value = BigDecimal.ZERO;
                    }
                    ReflectUtil.setFieldValue(boh1, fieldName, value.add(bohTemp.getQuantity()));
                }
                Map<String, Integer> temp = new HashMap<>();
                for (int i = 1; i < 13; i++) {
                    String fieldName = "m" + i + "Quantity";
                    BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(boh1, fieldName);
                    if (ObjectUtils.isNotEmpty(value)) {
                        temp.put("actualOutput", i);
                    }
                }
                if (temp != null && temp.size() > 0) {
                    for (int m = 1; m < temp.get("actualOutput"); m++) {
                        String fieldName = "m" + m + "Quantity";
                        BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(boh1, fieldName);
                        if (ObjectUtils.isEmpty(value)) {
                            value = BigDecimal.ZERO;
                        }
                        ReflectUtil.setFieldValue(boh1, fieldName, value);

                    }
                }
                repository.save(boh1);
            }
        }
    }

    private List<OpActualBohTemp> loadRemoteDataFromERP(Integer year, Integer month, List<ProductInfo> productInfoList, Map<String, String> lovMap) {
        Set<String> keySet = lovMap.keySet();
        //总共一百多数据全部加載,避免重复调用
        Set<String> codeNameSet = productGroupRepository.findAll().stream().map(ProductGroup::getCodeName).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        List<OpActualBohTemp> preBillingList = new ArrayList<>();
        //获取天玑数据 循环调用每次取1000
        ArGoodsCategoryQuery arGoodsCategoryQuery = new ArGoodsCategoryQuery();
        int pageSize = 1000;
        YearMonth period = YearMonth.of(year, month);
        arGoodsCategoryQuery.setPeriod(period.format(DateTimeFormatter.ofPattern("MM-yyyy")));
        arGoodsCategoryQuery.setPageNum(1);
        arGoodsCategoryQuery.setPageSize(pageSize);
        JSONObject arGoodsCategory;
        do {
            arGoodsCategory = esbFeignClient.getArGoodsCategory(arGoodsCategoryQuery);
            arGoodsCategoryQuery.setPageNum(arGoodsCategoryQuery.getPageNum() + 1);
            Object data = arGoodsCategory.get("data");
            if (data != null && !((JSONArray) arGoodsCategory.get("data")).isEmpty()) {
                JSONArray jsonArray = (JSONArray) arGoodsCategory.get("data");
                for (Object o : jsonArray) {
                    JSONObject each = (JSONObject) o;
                    String countryType = each.getString("countryType");
                    String oeRegion = each.getString("oeRegion");
                    String orderedQuantity = each.getString("orderedQuantity");
                    String omChannel = each.getString("omChannel");
                    String orderedItem = each.getString("orderedItem");
                    String power = each.getString("power");
                    if (countryType != null && oeRegion != null && orderedQuantity != null && power != null && orderedItem != null && codeNameSet.contains(orderedItem)) {
                        OpActualBohTemp opActualBohTemp = new OpActualBohTemp();
                        boolean flag = false;
                        for (String key : keySet) {
                            if (key.contains(oeRegion)) {
                                String aopArea = lovMap.get(key);
                                if (StringUtils.isNotBlank(aopArea)) {
                                    opActualBohTemp.setArea(aopArea);
                                    flag = true;
                                    break;
                                }
                            }
                        }
                        if (!flag) {
                            opActualBohTemp.setArea(null);
                        }
                        opActualBohTemp.setCountryFlag(countryType);
                        opActualBohTemp.setItem(orderedItem);
                        if ("Utility–SKA".equals(omChannel) || "Utility–EU SKA".equals(omChannel)) {
                            omChannel = "Utility";
                        } else if ("Residential".equals(omChannel) || "C&I".equals(omChannel) || "Non-Utility".equals(omChannel)) {
                            omChannel = "Distribution";
                        }
                        opActualBohTemp.setSalesChannel(omChannel);
                        opActualBohTemp.setItemType(TJConstant.ProductType.MODULE.getTjCode());
                        opActualBohTemp.setQuantity(new BigDecimal(orderedQuantity).multiply(new BigDecimal(power)).divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                        opActualBohTemp.setYear(period.getYear());
                        opActualBohTemp.setMonth(period.getMonthValue());
                        opActualBohTemp.setProductGroup(orderedItem);
                        productInfoList.stream().filter(bean -> orderedItem.equals(bean.getProductGroup())).findFirst().ifPresent(productInfo -> opActualBohTemp.setProductSeries(productInfo.getProductSeries()));
                        preBillingList.add(opActualBohTemp);
                    }
                }
            }
        } while (arGoodsCategory.get("data") != null && !((JSONArray) arGoodsCategory.get("data")).isEmpty() && ((JSONArray) arGoodsCategory.get("data")).size() == pageSize);

        preBillingList.removeIf(item -> ObjectUtils.isEmpty(item.getProductSeries()) || ObjectUtils.isEmpty(item.getArea()));

        return preBillingList;
    }

    private String countryFlagReplace(String countryFlag) {
        return CountryMapping.matchByAll(countryFlag).getAopEnum().getCode();
    }

    private String productTypeReplace(String productType) {
        return TJConstant.ProductType.match(productType).getCode();
    }

    /**
     * 查询临时表实际期初库存数据，查询范围为创建时间三个月内数据
     *
     * @return
     */
    private List<OpActualBohTemp> loadActualBohTemp(String year, String month, Boolean syscFlag, List<ProductInfo> productInfoList,
                                                    Map<String, String> lovMap) {
        QOpActualBohTemp qOpActualBohTemp = QOpActualBohTemp.opActualBohTemp;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        List<LocalDateTime> localDateTimes = factory.select(qOpActualBohTemp.createdTime.max()).from(qOpActualBohTemp).fetch();
        List<OpActualBohTemp> module = new ArrayList<>();
        if (!syscFlag) {
            //构建查询范围
            Integer QMonth = 0;
            Integer QYear = 0;
            if (CollectionUtils.isNotEmpty(localDateTimes)) {
                if (month.equals("01")) {
                    QMonth = 12;
                    QYear = Integer.parseInt(year) - 1;
                } else {
                    QMonth = Integer.parseInt(month) - 1;
                    QYear = Integer.parseInt(year);
                }
            }
            //查询组件期初库存
            module = factory
                    .select(Projections.fields(
                            OpActualBohTemp.class,
                            qOpActualBohTemp.countryFlag.as("countryFlag"),
                            qOpActualBohTemp.area.as("area"),
                            qOpActualBohTemp.salesChannel.as("salesChannel"),
                            qOpActualBohTemp.item.as("item"),
                            qOpActualBohTemp.productGroup.as("productGroup"),
                            qOpActualBohTemp.year.as("year"),
                            qOpActualBohTemp.month.as("month"),
                            qOpActualBohTemp.quantity.as("quantity"),
                            qOpActualBohTemp.itemType.as("itemType")
                    ))
                    .from(qOpActualBohTemp)
                    .where(qOpActualBohTemp.countryFlag.isNotNull())
                    .where(qOpActualBohTemp.productGroup.isNotNull())
                    .where(qOpActualBohTemp.area.isNotNull())
                    .where(qOpActualBohTemp.year.eq(QYear))
                    .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                    .where(qOpActualBohTemp.salesChannel.isNotNull())
                    .where(qOpActualBohTemp.month.eq(QMonth))
                    .where(qOpActualBohTemp.quantity.isNotNull())
                    .fetch();
            //  List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
            //  Map<String, String> lovMap = LovService.findLovName(LovHeaderCodeConstant.AOP_BOH_AREA_MAPPING_TJ);
            Set<String> keySet = lovMap.keySet();
            //规整数据，匹配产品系列，并且将W转换成MW，区域转换
            module.forEach(item -> {
                String replace = item.getProductGroup().replace("TSM-", "");
                if (StringUtils.isNotBlank(replace)) {
                    item.setProductGroup(replace);
                    ProductInfo productInfo = productInfoList.stream().filter(bean -> replace.equals(bean.getProductGroup())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(productInfo)) {
                        item.setProductSeries(productInfo.getProductSeries());
                    }
                }
                if (ObjectUtils.isNotEmpty(item.getQuantity())) {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                }
                if (ObjectUtils.isNotEmpty(item.getArea())) {
                    boolean flag = false;
                    for (String key : keySet) {
                        if (key.contains(item.getArea())) {
                            String aopArea = lovMap.get(key);
                            if (StringUtils.isNotBlank(aopArea)) {
                                item.setArea(aopArea);
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (!flag) {
                        item.setArea(null);
                    }
                }
            });
            //脏数据擦除
            module.removeIf(item -> ObjectUtils.isEmpty(item.getProductSeries()) || ObjectUtils.isEmpty(item.getArea()));
            //查询电池
            List<OpActualBohTemp> cellList = factory
                    .select(Projections.fields(
                            OpActualBohTemp.class,
                            qOpActualBohTemp.countryFlag.as("countryFlag"),
                            qOpActualBohTemp.item.as("item"),
                            qOpActualBohTemp.productSeries.as("productSeries"),
                            qOpActualBohTemp.year.as("year"),
                            qOpActualBohTemp.month.as("month"),
                            qOpActualBohTemp.quantity.as("quantity"),
                            qOpActualBohTemp.itemType.as("itemType")
                    ))
                    .from(qOpActualBohTemp)
                    .where(qOpActualBohTemp.countryFlag.isNotNull())
                    .where(qOpActualBohTemp.year.eq(QYear))
                    .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                    .where(qOpActualBohTemp.month.eq(QMonth))
                    .where(qOpActualBohTemp.quantity.isNotNull())
                    .fetch();
            cellList.forEach(item -> {
                item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
            });
            module.addAll(cellList);

            List<OpActualBohTemp> opActualBohTempList = loadRemoteDataFromERP(QYear, QMonth, productInfoList, lovMap);

            module.addAll(opActualBohTempList);
        } else {
            //构建查询范围
            Integer StarMonth = 0;
            Integer StarYear = 0;

            int endYear = LocalDate.now().minusMonths(1).getYear();
            int endMonth = LocalDate.now().minusMonths(1).getMonth().getValue();

            if (month.equals("01")) {
                StarMonth = 12;
                StarYear = Integer.parseInt(year) - 1;
            } else {
                StarMonth = Integer.parseInt(month) - 1;
                StarYear = Integer.parseInt(year);
            }

            //查询组件期初库存
            if (StarYear < endYear) {
                module = factory
                        .select(Projections.fields(
                                OpActualBohTemp.class,
                                qOpActualBohTemp.countryFlag.as("countryFlag"),
                                qOpActualBohTemp.area.as("area"),
                                qOpActualBohTemp.salesChannel.as("salesChannel"),
                                qOpActualBohTemp.item.as("item"),
                                qOpActualBohTemp.productGroup.as("productGroup"),
                                qOpActualBohTemp.year.as("year"),
                                qOpActualBohTemp.month.as("month"),
                                qOpActualBohTemp.quantity.as("quantity"),
                                qOpActualBohTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualBohTemp)
                        .where(qOpActualBohTemp.countryFlag.isNotNull())
                        .where(qOpActualBohTemp.productGroup.isNotNull())
                        .where(qOpActualBohTemp.area.isNotNull())
                        .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                        .where(qOpActualBohTemp.salesChannel.isNotNull())
                        .where(qOpActualBohTemp.year.eq(StarYear).and(qOpActualBohTemp.month.goe(StarMonth))
                                .or(qOpActualBohTemp.year.gt(StarYear).and(qOpActualBohTemp.year.lt(endYear)))
                                .or(qOpActualBohTemp.year.eq(endYear).and(qOpActualBohTemp.month.loe(endMonth))))
                        .where(qOpActualBohTemp.quantity.isNotNull())
                        .fetch();
            } else {
                module = factory
                        .select(Projections.fields(
                                OpActualBohTemp.class,
                                qOpActualBohTemp.countryFlag.as("countryFlag"),
                                qOpActualBohTemp.area.as("area"),
                                qOpActualBohTemp.salesChannel.as("salesChannel"),
                                qOpActualBohTemp.item.as("item"),
                                qOpActualBohTemp.productGroup.as("productGroup"),
                                qOpActualBohTemp.year.as("year"),
                                qOpActualBohTemp.month.as("month"),
                                qOpActualBohTemp.quantity.as("quantity"),
                                qOpActualBohTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualBohTemp)
                        .where(qOpActualBohTemp.countryFlag.isNotNull())
                        .where(qOpActualBohTemp.productGroup.isNotNull())
                        .where(qOpActualBohTemp.area.isNotNull())
                        .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                        .where(qOpActualBohTemp.salesChannel.isNotNull())
                        .where(qOpActualBohTemp.year.eq(StarYear).and(qOpActualBohTemp.month.goe(StarMonth))
                                .and(qOpActualBohTemp.year.eq(endYear).and(qOpActualBohTemp.month.loe(endMonth))))
                        .where(qOpActualBohTemp.quantity.isNotNull())
                        .fetch();
            }
            //  List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
            // Map<String, String> lovMap = LovService.findLovName(LovHeaderCodeConstant.AOP_BOH_AREA_MAPPING_TJ);
            Set<String> keySet = lovMap.keySet();
            //规整数据，匹配产品系列，并且将W转换成MW，区域转换
            module.forEach(item -> {
                String replace = item.getProductGroup().replace("TSM-", "");
                if (StringUtils.isNotBlank(replace)) {
                    item.setProductGroup(replace);
                    ProductInfo productInfo = productInfoList.stream().filter(bean -> replace.equals(bean.getProductGroup())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(productInfo)) {
                        item.setProductSeries(productInfo.getProductSeries());
                    }
                }
                if (ObjectUtils.isNotEmpty(item.getQuantity())) {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                }
                if (ObjectUtils.isNotEmpty(item.getArea())) {
                    boolean flag = false;
                    for (String key : keySet) {
                        if (key.contains(item.getArea())) {
                            String aopArea = lovMap.get(key);
                            if (StringUtils.isNotBlank(aopArea)) {
                                item.setArea(aopArea);
                                flag = true;
                                break;
                            }
                        }
                    }
                    if (!flag) {
                        item.setArea(null);
                    }
                }
            });
            //脏数据擦除
            module.removeIf(item -> ObjectUtils.isEmpty(item.getProductSeries()) || ObjectUtils.isEmpty(item.getArea()));
            //查询电池
            List<OpActualBohTemp> cellList = new ArrayList<>();
            if (StarYear < endYear) {
                cellList = factory
                        .select(Projections.fields(
                                OpActualBohTemp.class,
                                qOpActualBohTemp.countryFlag.as("countryFlag"),
                                qOpActualBohTemp.item.as("item"),
                                qOpActualBohTemp.productSeries.as("productSeries"),
                                qOpActualBohTemp.year.as("year"),
                                qOpActualBohTemp.month.as("month"),
                                qOpActualBohTemp.quantity.as("quantity"),
                                qOpActualBohTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualBohTemp)
                        .where(qOpActualBohTemp.countryFlag.isNotNull())
                        .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                        .where(qOpActualBohTemp.year.eq(StarYear).and(qOpActualBohTemp.month.goe(StarMonth))
                                .or(qOpActualBohTemp.year.gt(StarYear).and(qOpActualBohTemp.year.lt(endYear)))
                                .or(qOpActualBohTemp.year.eq(endYear).and(qOpActualBohTemp.month.loe(endMonth))))
                        .where(qOpActualBohTemp.quantity.isNotNull())
                        .fetch();
                cellList.forEach(item -> {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                });
            } else {
                cellList = factory
                        .select(Projections.fields(
                                OpActualBohTemp.class,
                                qOpActualBohTemp.countryFlag.as("countryFlag"),
                                qOpActualBohTemp.item.as("item"),
                                qOpActualBohTemp.productSeries.as("productSeries"),
                                qOpActualBohTemp.year.as("year"),
                                qOpActualBohTemp.month.as("month"),
                                qOpActualBohTemp.quantity.as("quantity"),
                                qOpActualBohTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualBohTemp)
                        .where(qOpActualBohTemp.countryFlag.isNotNull())
                        .where(qOpActualBohTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                        .where(qOpActualBohTemp.year.eq(StarYear).and(qOpActualBohTemp.month.goe(StarMonth))
                                .and(qOpActualBohTemp.year.eq(endYear).and(qOpActualBohTemp.month.loe(endMonth))))
                        .where(qOpActualBohTemp.quantity.isNotNull())
                        .fetch();
                cellList.forEach(item -> {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                });

            }
            module.addAll(cellList);

        }
        return module;
    }

    /**
     * 电池万片换算成MW
     *
     * @param cellList
     */
    private void calculateCell(List<ActualBohDTO> cellList) {
        List<String> errorList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cellList)) {
            List<Integer> yearList = cellList.stream().map(ActualBohDTO::getYear).distinct().collect(Collectors.toList());
            Integer year = yearList.stream().min(Integer::compareTo).orElse(0);
            yearList.add(year);
            List<CellWaferWeightDTO> cellWaferWeightDTOS = cellWaferWeightService.queryByTypeYear(yearList, ProductTypeEnum.CELL);
            //计算电池期初库存需要偏移一个月，2月的期初库存需要使用一月的折算参数和权重
            for (ActualBohDTO actualBohDTO : cellList) {
                List<CellWaferWeightDTO> cellWeightList = cellWaferWeightDTOS.stream().filter(item -> {
                    boolean flag = actualBohDTO.getCountryFlag().equals(item.getCountryFlag()) && actualBohDTO.getProductSeries().equals(item.getCellModel()) && actualBohDTO.getYear().equals(item.getYear());
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getCellShard())) {
                        flag = flag && actualBohDTO.getCellShard().equals(item.getCellShard());
                    }
                    return flag;
                }).collect(Collectors.toList());
                List<CellWaferWeightDTO> lastCellWeightList = cellWaferWeightDTOS.stream().filter(item -> {
                    boolean flag = actualBohDTO.getCountryFlag().equals(item.getCountryFlag()) && actualBohDTO.getProductSeries().equals(item.getCellModel()) && actualBohDTO.getYear().equals(item.getYear() - 1);
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getCellShard())) {
                        flag = flag && actualBohDTO.getCellShard().equals(item.getCellShard());
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (ObjectUtils.isNotEmpty(actualBohDTO.getM1Quantity())) {
                    if (CollectionUtils.isNotEmpty(lastCellWeightList)) {
                        List<Integer> weightMonth = new ArrayList<>();
                        List<Integer> discountMonth = new ArrayList<>();
                        BigDecimal m1Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1Quantity())) {
                                weightMonth.add(12);
                            } else if (cellWaferWeightDTO.getM12Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM12DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM12DiscountQuantity()) == 0)) {
                                discountMonth.add(12);
                            } else {
                                if (cellWaferWeightDTO.getM12Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m1Quantity = m1Quantity.add(actualBohDTO.getM1Quantity().multiply(cellWaferWeightDTO.getM12Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM12DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM1Quantity(m1Quantity);
                        if (CollectionUtils.isNotEmpty(weightMonth)) {
                            errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无权重配比", actualBohDTO.getYear() - 1, actualBohDTO.getCountryFlag(), actualBohDTO.getProductSeries(), actualBohDTO.getCellShard(), weightMonth.stream().distinct().collect(Collectors.toList())));
                        }
                        if (CollectionUtils.isNotEmpty(discountMonth)) {
                            errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无折算参数", actualBohDTO.getYear() - 1, actualBohDTO.getCountryFlag(), actualBohDTO.getProductSeries(), actualBohDTO.getCellShard(), discountMonth.stream().distinct().collect(Collectors.toList())));
                        }
                    }
                } else {
                    StringBuilder sb = new StringBuilder("年份：").append(actualBohDTO.getYear() - 1).append("，国内/海外：").append(actualBohDTO.getCountryFlag()).append("，电池型号：").append(actualBohDTO.getProductSeries());
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getCellShard())) {
                        sb.append("，分片方式：").append(actualBohDTO.getCellShard());
                    }
                    sb.append("无对应的权重配比");
                    errorList.add(sb.toString());
                }
                if (CollectionUtils.isNotEmpty(cellWeightList)) {
                    List<Integer> weightMonth = new ArrayList<>();
                    List<Integer> discountMonth = new ArrayList<>();
//                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM1Quantity())) {
//                        BigDecimal m1Quantity = BigDecimal.ZERO;
//                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
//                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1Quantity())) {
//                                weightMonth.add(1);
//                            } else if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM1DiscountQuantity()) == 0)) {
//                                discountMonth.add(1);
//                            } else {
//                                if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
//                                    m1Quantity = m1Quantity.add(actualBohDTO.getM1Quantity().multiply(cellWaferWeightDTO.getM1Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM1DiscountQuantity()), 6, RoundingMode.HALF_UP));
//                                }
//                            }
//                        }
//                        actualBohDTO.setM1Quantity(m1Quantity);
//                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM2Quantity())) {
                        BigDecimal m2Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1Quantity())) {
                                weightMonth.add(2);
                            } else if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM1DiscountQuantity()) == 0)) {
                                discountMonth.add(2);
                            } else {
                                if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m2Quantity = m2Quantity.add(actualBohDTO.getM2Quantity().multiply(cellWaferWeightDTO.getM1Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM1DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM2Quantity(m2Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM3Quantity())) {
                        BigDecimal m3Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM2Quantity())) {
                                weightMonth.add(3);
                            } else if (cellWaferWeightDTO.getM2Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM2DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM2DiscountQuantity()) == 0)) {
                                discountMonth.add(3);
                            } else {
                                if (cellWaferWeightDTO.getM2Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m3Quantity = m3Quantity.add(actualBohDTO.getM3Quantity().multiply(cellWaferWeightDTO.getM2Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM2DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM3Quantity(m3Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM4Quantity())) {
                        BigDecimal m4Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM3Quantity())) {
                                weightMonth.add(4);
                            } else if (cellWaferWeightDTO.getM3Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM3DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM3DiscountQuantity()) == 0)) {
                                discountMonth.add(4);
                            } else {
                                if (cellWaferWeightDTO.getM3Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m4Quantity = m4Quantity.add(actualBohDTO.getM4Quantity().multiply(cellWaferWeightDTO.getM3Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM3DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM4Quantity(m4Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM5Quantity())) {
                        BigDecimal m5Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM4Quantity())) {
                                weightMonth.add(5);
                            } else if (cellWaferWeightDTO.getM4Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM4DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM4DiscountQuantity()) == 0)) {
                                discountMonth.add(5);
                            } else {
                                if (cellWaferWeightDTO.getM4Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m5Quantity = m5Quantity.add(actualBohDTO.getM5Quantity().multiply(cellWaferWeightDTO.getM4Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM4DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM5Quantity(m5Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM6Quantity())) {
                        BigDecimal m6Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM5Quantity())) {
                                weightMonth.add(6);
                            } else if (cellWaferWeightDTO.getM5Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM5DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM5DiscountQuantity()) == 0)) {
                                discountMonth.add(6);
                            } else {
                                if (cellWaferWeightDTO.getM5Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m6Quantity = m6Quantity.add(actualBohDTO.getM6Quantity().multiply(cellWaferWeightDTO.getM5Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM5DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM6Quantity(m6Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM7Quantity())) {
                        BigDecimal m7Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM6Quantity())) {
                                weightMonth.add(7);
                            } else if (cellWaferWeightDTO.getM6Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM6DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM6DiscountQuantity()) == 0)) {
                                discountMonth.add(7);
                            } else {
                                if (cellWaferWeightDTO.getM6Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m7Quantity = m7Quantity.add(actualBohDTO.getM7Quantity().multiply(cellWaferWeightDTO.getM6Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM6DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM7Quantity(m7Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM8Quantity())) {
                        BigDecimal m8Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM7Quantity())) {
                                weightMonth.add(8);
                            } else if (cellWaferWeightDTO.getM7Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM7DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM7DiscountQuantity()) == 0)) {
                                discountMonth.add(8);
                            } else {
                                if (cellWaferWeightDTO.getM7Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m8Quantity = m8Quantity.add(actualBohDTO.getM8Quantity().multiply(cellWaferWeightDTO.getM7Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM7DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM8Quantity(m8Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM9Quantity())) {
                        BigDecimal m9Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM8Quantity())) {
                                weightMonth.add(9);
                            } else if (cellWaferWeightDTO.getM8Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM8DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM8DiscountQuantity()) == 0)) {
                                discountMonth.add(9);
                            } else {
                                if (cellWaferWeightDTO.getM8Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m9Quantity = m9Quantity.add(actualBohDTO.getM9Quantity().multiply(cellWaferWeightDTO.getM8Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM8DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM9Quantity(m9Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM10Quantity())) {
                        BigDecimal m10Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM9Quantity())) {
                                weightMonth.add(10);
                            } else if (cellWaferWeightDTO.getM9Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM9DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM9DiscountQuantity()) == 0)) {
                                discountMonth.add(10);
                            } else {
                                if (cellWaferWeightDTO.getM9Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m10Quantity = m10Quantity.add(actualBohDTO.getM10Quantity().multiply(cellWaferWeightDTO.getM9Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM9DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM10Quantity(m10Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM11Quantity())) {
                        BigDecimal m11Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM10Quantity())) {
                                weightMonth.add(11);
                            } else if (cellWaferWeightDTO.getM10Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM10DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM10DiscountQuantity()) == 0)) {
                                discountMonth.add(11);
                            } else {
                                if (cellWaferWeightDTO.getM10Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m11Quantity = m11Quantity.add(actualBohDTO.getM11Quantity().multiply(cellWaferWeightDTO.getM10Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM10DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM11Quantity(m11Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getM12Quantity())) {
                        BigDecimal m12Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM11Quantity())) {
                                weightMonth.add(12);
                            } else if (cellWaferWeightDTO.getM11Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM11DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM11DiscountQuantity()) == 0)) {
                                discountMonth.add(12);
                            } else {
                                if (cellWaferWeightDTO.getM11Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m12Quantity = m12Quantity.add(actualBohDTO.getM12Quantity().multiply(cellWaferWeightDTO.getM11Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM11DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualBohDTO.setM12Quantity(m12Quantity);
                    }
                    if (CollectionUtils.isNotEmpty(weightMonth)) {
                        errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无权重配比", actualBohDTO.getYear(), actualBohDTO.getCountryFlag(), actualBohDTO.getProductSeries(), actualBohDTO.getCellShard(), weightMonth.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollectionUtils.isNotEmpty(discountMonth)) {
                        errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无折算参数", actualBohDTO.getYear(), actualBohDTO.getCountryFlag(), actualBohDTO.getProductSeries(), actualBohDTO.getCellShard(), discountMonth.stream().distinct().collect(Collectors.toList())));
                    }
                } else {
                    StringBuilder sb = new StringBuilder("年份：").append(actualBohDTO.getYear()).append("，国内/海外：").append(actualBohDTO.getCountryFlag()).append("，电池型号：").append(actualBohDTO.getProductSeries());
                    if (ObjectUtils.isNotEmpty(actualBohDTO.getCellShard())) {
                        sb.append("，分片方式：").append(actualBohDTO.getCellShard());
                    }
                    sb.append("无对应的权重配比");
                    errorList.add(sb.toString());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            StringBuffer sb = new StringBuffer();
            for (String s : errorList) {
                sb.append(s).append("<br/>");
            }
            throw new BizException(sb.toString());
        }
    }

    @Override
    public Page<GeneralSummaryDTO> queryModuleSummaryPage(ActualBohQuery query) {
        QActualBoh qActualBoh = QActualBoh.actualBoh;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<GeneralSummaryDTO> fromSql = jpaQueryFactory
                .select(Projections.fields(
                        GeneralSummaryDTO.class, qActualBoh.year.as("year"),
                        qActualBoh.productSeries.as("productSeries"),
                        qActualBoh.area.as("area"),
                        qActualBoh.m1Quantity.sum().as("m1Quantity"),
                        qActualBoh.m2Quantity.sum().as("m2Quantity"),
                        qActualBoh.m3Quantity.sum().as("m3Quantity"),
                        qActualBoh.m4Quantity.sum().as("m4Quantity"),
                        qActualBoh.m5Quantity.sum().as("m5Quantity"),
                        qActualBoh.m6Quantity.sum().as("m6Quantity"),
                        qActualBoh.m7Quantity.sum().as("m7Quantity"),
                        qActualBoh.m8Quantity.sum().as("m8Quantity"),
                        qActualBoh.m9Quantity.sum().as("m9Quantity"),
                        qActualBoh.m10Quantity.sum().as("m10Quantity"),
                        qActualBoh.m11Quantity.sum().as("m11Quantity"),
                        qActualBoh.m12Quantity.sum().as("m12Quantity")
                ))
                .from(qActualBoh);
        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            builder.and(qActualBoh.productSeries.eq(query.getProductSeries()));
        }
        if (query.getYear() != null) {
            builder.and(qActualBoh.year.eq(query.getYear()));
        }
        if (StringUtils.isNotBlank(query.getArea())) {
            builder.and(qActualBoh.area.eq(query.getArea()));
        }

        if (ObjectUtils.isNotEmpty(query.getProductType())) {
            builder.and(qActualBoh.productType.eq(query.getProductType().getCode()));
        }

        JPAQuery<GeneralSummaryDTO> generalSummaryDTOJPAQuery = fromSql
                .where(builder)
                .groupBy(qActualBoh.year, qActualBoh.productSeries, qActualBoh.area);

        List<GeneralSummaryDTO> fetch = generalSummaryDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();

        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize());
        //值集转换 组件期初库存
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        List<Tuple> list = jpaQueryFactory.select(qActualBoh.year, qActualBoh.productSeries, qActualBoh.area)
                .from(qActualBoh)
                .where(builder)
                .groupBy(qActualBoh.year, qActualBoh.productSeries, qActualBoh.area).fetch();
        fetch.forEach(generalSummaryDTO -> {
            //区域
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_REGION, generalSummaryDTO.getArea()));
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(generalSummaryDTO -> {

            if (StringUtils.isNotBlank(generalSummaryDTO.getArea())) {
                LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_REGION, generalSummaryDTO.getArea()));
                if (Objects.nonNull(lovLineDTO01)) {
                    generalSummaryDTO.setArea(lovLineDTO01.getLovName());
                }
            }
        });

        for (GeneralSummaryDTO dto : fetch) {
            dto.calculateQuarter();
        }

        return new PageImpl<>(fetch, pageable, list.size());
    }

    @Override
    public List<ActualBoh> findAllByYear(Integer year) {
        return repository.findAllByYear(year);
    }

    @Override
    public void syncActualBohDataNew(LocalDate inventoryDate) {
        Integer maxPageSize = 1000;
        Integer maxPageNumber = 10000;
        List<ActualBoh> actualBohList = repository.findAllByYearAndProductType(inventoryDate.getYear(), ProductTypeEnum.CELL.getCode());

        Integer curPageNumber = 1;
        Boolean clearFlag = false;
        Long miId = null;
        Map<String, List<CellInventoryDTO>> cellInventoryDTOMap = Maps.newHashMap();
        do {
            CellInventoryQuery query = new CellInventoryQuery();
            query.setPageSize(maxPageSize);
            // 按ID去查 ID比较大小 分页截取的方式
            query.setPageNumber(1);
            query.setInventoryDate(inventoryDate);
            query.setInventoryTime("8.20");
            query.setSortIdFlag(true);
            query.setMinId(miId);
            ResponseEntity<Results<JSONObject>> responseEntity = powerFeignClient.getObtainInitialInventory(query);
            if (!responseEntity.getBody().isSuccess()) {
                throw new RuntimeException("获取电池期初库存数据出现异常");
            }
            JSONObject json = responseEntity.getBody().getData();
            List<CellInventoryDTO> cellInventoryDTOS = JSON.parseArray(json.getJSONArray("content").toString(), CellInventoryDTO.class);
            if (CollectionUtils.isEmpty(cellInventoryDTOS)) {
                if (curPageNumber == 1) clearFlag = true;
                break;
            }
            curPageNumber ++;
            miId = cellInventoryDTOS.get(cellInventoryDTOS.size() - 1).getId();
            this.buildCellInventoryDTOMap(cellInventoryDTOS, cellInventoryDTOMap);
        } while (curPageNumber <= maxPageNumber);

        // 一个月都未获取到有效数据 初始化为0
        if (clearFlag) {
            actualBohList.forEach(ele -> {
                IntStream.rangeClosed(1, 12).forEach(month -> {
                    String fileName = String.format("m%sQuantity", month);
                    BigDecimal monthQuantity = null;
                    if (month <= inventoryDate.getMonthValue()) {
                        monthQuantity = BigDecimal.ZERO;
                    }
                    ReflectUtil.setFieldValue(ele, fileName, monthQuantity);
                });
            });
        } else {
            this.buildActualBoh(inventoryDate, actualBohList, cellInventoryDTOMap);
        }
        repository.saveAll(actualBohList);
    }

    @Override
    @SneakyThrows
    public void syncWaferBohJobHandler(LocalDate inventoryDate, Boolean mvIngoreFlag) {
        log.info("syncWaferBohJobHandler start... {}", LocalDateTime.now());

        // 分页大小从0开始 0默认取第一行
        List<BigdataWaferBohDTO> apiDataList = Lists.newLinkedList();
        pullWaferBohJobHandler(0, inventoryDate, apiDataList);

        // MW折算系数
        Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceMap = this.getCellWaferWeightMaintenanceMap(inventoryDate.getYear());
        // 涉及210R+-N 需要去掉+号转为210R-N型号 所以这里要分组汇总一次
        Set<String> errorMVSets = Sets.newHashSet();

        List<String> effectOrgCodeList = bigdataApiTypeFactory.getAllEffectOrgCodeList();
        Iterator<BigdataWaferBohDTO> iterator = apiDataList.iterator();
        while (iterator.hasNext()) {
            BigdataWaferBohDTO ele = iterator.next();
            ele.setIsOversea(Objects.equals("国内", ele.getIsOversea()) ? CountryFlagEnum.INLAND.getCode() : CountryFlagEnum.OVERSEA.getCode());
            ele.setCellType(ele.getCellType().contains("+") ? ele.getCellType().replace("+", "") : ele.getCellType());
            // 当月的硅片-MW折算系数 可支持手工传入跳过MV折算系数 mvIngoreFlag=true即跳过不参与后续计算
            String fieldName = String.format(AdjustConstant.MQUANTITY, inventoryDate.getMonthValue());
            String maintenanceGroupKey = StringUtils.join(ele.getIsOversea(), ele.getCellType());
            CellWaferWeightMaintenanceDTO maintenanceDTO = cellWaferWeightMaintenanceMap.get(maintenanceGroupKey);
            if (Objects.isNull(maintenanceDTO)) {
                if (!mvIngoreFlag) {
                    errorMVSets.add(maintenanceGroupKey);
                }
                iterator.remove();
                continue;
            } else {
                BigDecimal mvQuantity = Objects.isNull(maintenanceDTO) ?
                        BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(maintenanceDTO, fieldName);
                if (Objects.isNull(mvQuantity) || mvQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    if (!mvIngoreFlag) {
                        errorMVSets.add(maintenanceGroupKey);
                    }
                    iterator.remove();
                    continue;
                }
            }
            // p2_2771 AOP实际数接口-硅片期初库存-增加账套过滤
            if (StringUtils.isEmpty(ele.getOrg()) || !effectOrgCodeList.contains(ele.getOrg())) {
                iterator.remove();
                continue;
            }
        }

        Assert.isTrue(CollectionUtils.isEmpty(errorMVSets), "MV折算系数未命中" + StringUtils.join(errorMVSets, "、"));

        //保存
        ActualBohServiceImpl beanHandle = SpringContextUtils.getBean(ActualBohServiceImpl.class);
        beanHandle.batchSaveWaferBohJob(inventoryDate, apiDataList, cellWaferWeightMaintenanceMap);
        log.info("syncWaferBohJobHandler end... {}", LocalDateTime.now());
    }

    @Override
    @SneakyThrows
    public void syncModuleBohJobHandler(LocalDate inventoryDate) {
        log.info("syncModuleBohJobHandler start... {}", LocalDateTime.now());

        // 分页大小从0开始 0默认取第一行
        List<BigdataModuleBohDTO> apiDataList = Lists.newLinkedList();
        pullModuleBohJobHandler(0, inventoryDate, apiDataList);

        List<BigdataModuleBohDTO> emptyApiDataList = apiDataList.stream().filter(ele -> StringUtils.isEmpty(ele.getProductFamily())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(emptyApiDataList)) {
            log.info("syncModuleBohJobHandler productFamily empty {}", JSON.toJSONString(emptyApiDataList));
        }
        // 过滤产品族为空的数据
        apiDataList = apiDataList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getProductFamily())).collect(Collectors.toList());

        List<BigdataModuleBohDTO> filterBUApiDataList = apiDataList.stream().filter(ele -> StringUtils.isEmpty(ele.getBu()) || !MODULE_BOH_BU_LIST.contains(ele.getBu())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterBUApiDataList)) {
            log.info("syncModuleBohJobHandler filterBUApiDataList {}", JSON.toJSONString(filterBUApiDataList));
        }
        // 过滤BU的数据
        apiDataList = apiDataList.stream().filter(ele -> !StringUtils.isEmpty(ele.getBu()) && MODULE_BOH_BU_LIST.contains(ele.getBu())).collect(Collectors.toList());

        // 产品信息对照表 路径：AOP管理->配置管理->产品信息对照表
        List<ProductInfo> productInfos = productInfoService.queryAllProductInfo();
        Map<String, String> productInfoMap = productInfos.stream().collect(Collectors.toMap(ProductInfo::getProductGroup, ProductInfo::getProductSeries, (v1, v2) -> v1));
        Map<String, LovLineDTO> bohAreaMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_BOH_AREA_MAPPING_TJ);
        Map<String, String> allBohAreaMap = Maps.newHashMap();
        bohAreaMap.values().forEach(ele -> {
            List<String> lovValueList = Arrays.asList(ele.getLovValue().split(","));
            lovValueList.forEach(lovValue -> {
                allBohAreaMap.put(lovValue, ele.getLovName());
            });
        });
        Map<String, LovLineDTO> channelMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CHANNEL_MAPPING_TJ);
        Map<String, String> allChannelMap = Maps.newHashMap();
        channelMap.values().forEach(ele -> {
            List<String> lovValueList = Arrays.asList(ele.getLovValue().split(","));
            lovValueList.forEach(lovValue -> {
                allChannelMap.put(lovValue, ele.getLovName());
            });
        });

        // 产地 校验
        Set<String> errorRegionList = Sets.newHashSet();
        Set<String> errorSaleChannelList = Sets.newHashSet();
        Iterator<BigdataModuleBohDTO> iterator = apiDataList.iterator();
        while (iterator.hasNext()) {
            BigdataModuleBohDTO ele = iterator.next();
            ele.setIsOversea(Objects.equals("国内", ele.getIsOversea()) ? CountryFlagEnum.INLAND.getCode() : CountryFlagEnum.OVERSEA.getCode());
            String productFamily = ele.getProductFamily().replaceFirst("TSM-", "");
            productFamily = productFamily.lastIndexOf("-Q") != -1 ? productFamily.substring(0, productFamily.lastIndexOf("-Q")) : productFamily;
            ele.setProductFamily(productFamily);
            String productSeries = productInfoMap.get(productFamily);
            if (StringUtils.isEmpty(productSeries)) {
                log.info("syncModuleBohJobHandler productSeries empty {}", JSON.toJSONString(iterator.hasNext()));
                iterator.remove();
                continue;
            } else {
                ele.setProductSeries(productSeries);
            }
            String region = allBohAreaMap.get(ele.getRegion());
            if (StringUtils.isEmpty(region)) {
                errorRegionList.add(ele.getRegion());
            } else {
                ele.setRegion(region);
            }
            String saleChannel = allChannelMap.get(ele.getChannel());
            if (StringUtils.isEmpty(saleChannel)) {
                errorSaleChannelList.add(ele.getChannel());
            } else {
                ele.setChannel(saleChannel);
            }
        }
        Assert.isTrue(CollectionUtils.isEmpty(errorRegionList), String.format("LOV:AOP_BOH_AREA_MAPPING_TJ 区域无以下映射：%s", StringUtils.join(errorRegionList, "、")));
        Assert.isTrue(CollectionUtils.isEmpty(errorSaleChannelList), String.format("LOV:AOP_CHANNEL_MAPPING_TJ 销售渠道无以下映射：%s", StringUtils.join(errorSaleChannelList, "、")));

        //保存
        ActualBohServiceImpl beanHandle = SpringContextUtils.getBean(ActualBohServiceImpl.class);
        beanHandle.batchSaveModuleBohJob(inventoryDate, apiDataList);
        log.info("syncModuleBohJobHandler end... {}", LocalDateTime.now());
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchSaveModuleBohJob(LocalDate inventoryDate,
                                      List<BigdataModuleBohDTO> apiDataList) {
        if (CollectionUtils.isEmpty(apiDataList)) {
            return;
        }
        List<ActualBoh> allActualBohList = repository.findAllByYearAndProductType(inventoryDate.getYear(), ProductTypeEnum.MODULE.getCode());
        Map<String, ActualBoh> allActualBohMap = allActualBohList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductGroup(), ele.getProductSeries(), ele.getSalesChannel(), ele.getArea()), Function.identity(), (v1, v2) -> v1));

        Map<String, List<BigdataModuleBohDTO>> apiDataMap = apiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getProductFamily(), ele.getProductSeries(), ele.getChannel(), ele.getRegion())));

        Integer month = inventoryDate.getMonthValue();
        List<ActualBoh> batchActualBohList = Lists.newLinkedList();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        apiDataMap.forEach((groupKey, v) -> {
            BigdataModuleBohDTO item = v.get(0);
            if (allActualBohMap.containsKey(groupKey)) {
                ActualBoh actualBoh = allActualBohMap.get(groupKey);
                BigDecimal quantity = v.stream().map(ele -> new BigDecimal(ele.getWQuantity()))
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualBoh, fieldName, quantity.setScale(6, RoundingMode.HALF_UP));
                batchActualBohList.add(actualBoh);
            } else {
                ActualBoh actualBoh = new ActualBoh();
                actualBoh.setCountryFlag(item.getIsOversea());
                actualBoh.setProductType(ProductTypeEnum.MODULE.getCode());
                actualBoh.setProductFrom(null);
                actualBoh.setProductSeries(item.getProductSeries());
                actualBoh.setProductGroup(item.getProductFamily());
                actualBoh.setSalesChannel(item.getChannel());
                actualBoh.setCellShard(null);
                actualBoh.setSalesChannel(item.getChannel());
                actualBoh.setYear(Integer.valueOf(item.getYear()));
                actualBoh.setArea(item.getRegion());
                actualBoh.setCreatedBy("-1");
                actualBoh.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal quantity = null;
                    if (curMonth < month) {
                        quantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        quantity = v.stream().map(ele -> new BigDecimal(ele.getWQuantity()))
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_UP);
                    }
                    ReflectUtil.setFieldValue(actualBoh, curFieldName, quantity);
                });
                batchActualBohList.add(actualBoh);
            }
        });
        repository.saveAll(batchActualBohList);
    }

    private void pullModuleBohJobHandler(int page,
                                         LocalDate inventoryDate,
                                         List<BigdataModuleBohDTO> apiDataList) throws Exception {
        try {
            HashMap<String, Object> condition = Maps.newHashMap();
            condition.put("month", inventoryDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String response = bigdataApiTypeFactory.executeByStrategy(BigdataTypeEnums.MODULE_BOH, condition, page);
            TjResponse<BigdataModuleBohDTO> tjResponse = JSON.parseObject(response, new TypeReference<TjResponse<BigdataModuleBohDTO>>(BigdataModuleBohDTO.class) {});
            if (tjResponse.isSuccess()) {
                List<BigdataModuleBohDTO> spliteApiDataList = tjResponse.getResults();
                if (CollectionUtils.isNotEmpty(spliteApiDataList)) {
                    log.info("pullModuleBohJobHandler 第{}批次 apiDataList {}", page + 1, JSON.toJSON(spliteApiDataList));
                    apiDataList.addAll(spliteApiDataList);
                    //继续调用
                    page++;
                    pullModuleBohJobHandler(page, inventoryDate, apiDataList);
                }
            }
        } catch (Exception ex) {
            log.error("pullWaferBohJobHandler exception", ex);
            throw ex;
        }
    }

    private void pullWaferBohJobHandler(int page,
                                        LocalDate inventoryDate,
                                        List<BigdataWaferBohDTO> apiDataList) throws Exception {
        try {
            HashMap<String, Object> condition = Maps.newHashMap();
            condition.put("month", inventoryDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String response = bigdataApiTypeFactory.executeByStrategy(BigdataTypeEnums.WAFER_BOH, condition, page);
            TjResponse<BigdataWaferBohDTO> tjResponse = JSON.parseObject(response, new TypeReference<TjResponse<BigdataWaferBohDTO>>(BigdataWaferBohDTO.class) {});
            if (tjResponse.isSuccess()) {
                List<BigdataWaferBohDTO> spliteApiDataList = tjResponse.getResults();
                if (CollectionUtils.isNotEmpty(spliteApiDataList)) {
                    log.info("pullWaferBohJobHandler 第{}批次 apiDataList {}", page + 1, JSON.toJSON(spliteApiDataList));
                    apiDataList.addAll(spliteApiDataList);
                    //继续调用
                    page++;
                    pullWaferBohJobHandler(page, inventoryDate, apiDataList);
                }
            }
        } catch (Exception ex) {
            log.error("pullWaferBohJobHandler exception", ex);
            throw ex;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveWaferBohJob(LocalDate inventoryDate,
                                     List<BigdataWaferBohDTO> apiDataList,
                                     Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceMap) {
        if (CollectionUtils.isEmpty(apiDataList)) {
            return;
        }
        List<ActualBoh> allActualBohList = repository.findAllByYearAndProductType(inventoryDate.getYear(), ProductTypeEnum.WAFER.getCode());
        Map<String, ActualBoh> allActualBohMap = allActualBohList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductSeries(), ele.getYear()), Function.identity(), (v1, v2) -> v1));

        Map<String, List<BigdataWaferBohDTO>> apiDataMap = apiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getCellType(), ele.getYear())));

        Integer month = inventoryDate.getMonthValue();
        List<ActualBoh> batchActualBohList = Lists.newLinkedList();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        apiDataMap.forEach((groupKey, v) -> {
            // 当月的硅片-MW折算系数
            String maintenanceGroupKey = StringUtils.join(v.get(0).getIsOversea(), v.get(0).getCellType());
            CellWaferWeightMaintenanceDTO maintenanceDTO = cellWaferWeightMaintenanceMap.get(maintenanceGroupKey);
            BigDecimal mvQuantity = Objects.isNull(maintenanceDTO) ?
                    BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(maintenanceDTO, fieldName);

            BigdataWaferBohDTO item = v.get(0);
            if (allActualBohMap.containsKey(groupKey)) {
                ActualBoh actualBoh = allActualBohMap.get(groupKey);
                BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualBoh, fieldName, Objects.equals(BigDecimal.ZERO, mvQuantity) ?
                        BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP));
                batchActualBohList.add(actualBoh);
            } else {
                ActualBoh actualBoh = new ActualBoh();
                actualBoh.setCountryFlag(item.getIsOversea());
                actualBoh.setProductType(ProductTypeEnum.WAFER.getCode());
                actualBoh.setProductSeries(item.getCellType());
                actualBoh.setYear(Integer.valueOf(item.getYear()));
                actualBoh.setCreatedBy("-1");
                actualBoh.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal quantity = null;
                    if (curMonth < month) {
                        quantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantity = Objects.equals(BigDecimal.ZERO, mvQuantity) ? BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP);
                    }
                    ReflectUtil.setFieldValue(actualBoh, curFieldName, quantity);
                });
                batchActualBohList.add(actualBoh);
            }
        });
        repository.saveAll(batchActualBohList);
    }

    public Map<String, CellWaferWeightMaintenanceDTO> getCellWaferWeightMaintenanceMap(Integer year) {
        CellWaferWeightMaintenanceQuery query = new CellWaferWeightMaintenanceQuery();
        query.setYear(year);
        query.setProductType(ProductTypeEnum.WAFER);
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        Page<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMaintenanceService.queryByPage(query);
        return maintenanceDTOS.getContent().stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getCellModel()), Function.identity(), (v1, v2) -> v1));
    }

    private void buildActualBoh(LocalDate inventoryDate,
                                List<ActualBoh> actualBohList,
                                Map<String, List<CellInventoryDTO>> cellInventoryDTOMap) {
        Map<String, BigDecimal> mwCoefficientMap = this.buildMwCoefficientMap();
        actualBohList.forEach(ele -> {
            String fileName = String.format("m%sQuantity", inventoryDate.getMonthValue());
            String groupKey = StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getProductSeries(), ele.getCellShard()), "_");
            List<CellInventoryDTO> itemCellInventoryDTOS = cellInventoryDTOMap.get(groupKey);
            if (!CollectionUtils.isEmpty(itemCellInventoryDTOS)) {
                BigDecimal calculateQuantity = this.actualBohCalculate(itemCellInventoryDTOS, mwCoefficientMap);
                ReflectUtil.setFieldValue(ele, fileName, calculateQuantity);
            }
        });
        List<String> allGroupKeyList = actualBohList.stream().map(ele ->
                StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getProductSeries(), ele.getCellShard()), "_")).collect(Collectors.toList());
        cellInventoryDTOMap.forEach((k, v) -> {
            if (allGroupKeyList.contains(k)) {
                return;
            }
            String[] split = k.split("_");
            ActualBoh actualBoh = new ActualBoh();
            actualBoh.setCountryFlag(split.length >= 1 ? split[0] : null);
            actualBoh.setProductType(ProductTypeEnum.CELL.getCode());
            actualBoh.setProductSeries(split.length >= 2 ? split[1] : null);
            actualBoh.setCellShard(split.length >= 3 ? split[2] : null);
            actualBoh.setYear(inventoryDate.getYear());
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fileName = String.format("m%sQuantity", month);
                BigDecimal monthQuantity = null;
                if (month < inventoryDate.getMonthValue()) {
                    monthQuantity = BigDecimal.ZERO;
                } else if (inventoryDate.getMonthValue() == month) {
                    monthQuantity = this.actualBohCalculate(v, mwCoefficientMap);
                }
                ReflectUtil.setFieldValue(actualBoh, fileName, monthQuantity);
            });
            actualBohList.add(actualBoh);
        });
    }

    private void buildCellInventoryDTOMap(List<CellInventoryDTO> cellInventoryDTOS,
                                          Map<String, List<CellInventoryDTO>> cellInventoryDTOMap) {
        List<String> itemCodes = cellInventoryDTOS.stream().map(CellInventoryDTO::getCellNo).distinct().collect(Collectors.toList());
        ItemsQuery query = new ItemsQuery();
        query.setItemCodes(itemCodes);
        ResponseEntity<Results<List<ItemsDTO>>> responseEntity = bomFeign.findByItemCodes(query);
        if (!responseEntity.getBody().isSuccess()) {
            throw new RuntimeException("获取料号数据出现异常");
        }
        Map<String, ItemsDTO> itemsDTOMap = responseEntity.getBody().getData().stream().collect(Collectors.toMap(ItemsDTO::getItemCode, Function.identity(), (v1, v2) -> v1));
        Iterator<CellInventoryDTO> iterator = cellInventoryDTOS.iterator();
        while (iterator.hasNext()) {
            CellInventoryDTO ele = iterator.next();
            ItemsDTO itemsDTO = itemsDTOMap.get(ele.getCellNo());
            if (Objects.isNull(itemsDTO)) {
                iterator.remove();
                continue;
            }
            String segment9 = ActualBohSegment9Enums.getDescByCode(itemsDTO.getSegment9());
            if (StringUtils.isEmpty(segment9)) {
                iterator.remove();
                continue;
            }
            if (StringUtils.isEmpty(itemsDTO.getSegment3())) {
                iterator.remove();
                continue;
            }
            String segment3 = itemsDTO.getSegment3();
            if (itemsDTO.getSegment3().startsWith("158")) {
                segment3 = "158";
            } else if (itemsDTO.getSegment3().startsWith("166")) {
                segment3 = "166";
            } else if (itemsDTO.getSegment3().startsWith("156")) {
                segment3 = "156";
            }
            //分片方式
            String shardMethod = !Arrays.asList("二分片", "三分片").contains(itemsDTO.getSegment11()) ? "" :
                    (Objects.equals("二分片", itemsDTO.getSegment11()) ? Priority.CellShard.TWC.getCode() : Priority.CellShard.THC.getCode());
            ele.setCellModel(segment3 + "-" + segment9);
            ele.setCellShard(shardMethod);
        }
        Map<String, List<CellInventoryDTO>> itemCellInventoryDTOMap = cellInventoryDTOS.stream().filter(ele -> StringUtils.isNotEmpty(ele.getCellModel())
                && StringUtils.isNotEmpty(ele.getCellShard())).collect(Collectors.groupingBy(ele ->
                StringUtils.join(Arrays.asList(ele.getIsOversea(), ele.getCellModel(), ele.getCellShard()), "_")));
        itemCellInventoryDTOMap.forEach((k, v) -> {
            List<CellInventoryDTO> inventoryDTOS = cellInventoryDTOMap.getOrDefault(k, Lists.newLinkedList());
            inventoryDTOS.addAll(v);
            cellInventoryDTOMap.put(k, inventoryDTOS);
        });
    }

    private BigDecimal actualBohCalculate(List<CellInventoryDTO> itemCellInventoryDTOS, Map<String, BigDecimal> mwCoefficientMap) {
        BigDecimal toalQuantity = itemCellInventoryDTOS.stream().map(itemCell -> {
            BigDecimal mwCoefficient = mwCoefficientMap.get(StringUtils.join(Arrays.asList(itemCell.getIsOversea(), itemCell.getCellType()), "_"));
            BigDecimal monthQuantity = itemCell.getQuantity();
            BigDecimal calculateQuantity = (Objects.isNull(monthQuantity) || monthQuantity.compareTo(BigDecimal.ZERO) == 0 || Objects.isNull(mwCoefficient) || mwCoefficient.compareTo(BigDecimal.ZERO) == 0)
                    ? BigDecimal.ZERO : monthQuantity.divide(mwCoefficient.multiply(new BigDecimal(10000)), 6, RoundingMode.HALF_UP);
            return Objects.isNull(mwCoefficient) ? BigDecimal.ZERO : calculateQuantity;
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        return toalQuantity.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO : toalQuantity;
    }

    private Map<String, BigDecimal> buildMwCoefficientMap() {
        ResponseEntity<Results<List<MwCoefficientDTO>>> resultsResponseEntity = powerFeignClient.allList();
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            log.error("MW折算系数接口获取失败：{}", JSON.toJSONString(resultsResponseEntity));
            throw new BizException("MW折算系数接口获取失败");
        }
        List<MwCoefficientDTO> mwCoefficientDTOS = resultsResponseEntity.getBody().getData();
        if (CollectionUtils.isEmpty(mwCoefficientDTOS)) {
            return Maps.newHashMap();
        }
        return mwCoefficientDTOS.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(Arrays.asList(ele.getIsOversea(), ele.getCellType()), "_"), MwCoefficientDTO::getCoefficient, (v1, v2) -> v1));
    }
}
