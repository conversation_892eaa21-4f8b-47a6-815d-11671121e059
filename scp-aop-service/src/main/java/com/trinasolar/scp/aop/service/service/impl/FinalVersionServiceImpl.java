package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.google.common.base.Joiner;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.aop.domain.base.*;
import com.trinasolar.scp.aop.domain.convert.AopReleaseVersionRecordConvert;
import com.trinasolar.scp.aop.domain.convert.CalculateSchedulingDEConvert;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.*;
import com.trinasolar.scp.aop.domain.priority.Priority;
import com.trinasolar.scp.aop.domain.query.ActualOtherQuery;
import com.trinasolar.scp.aop.domain.utils.MathUtils;
import com.trinasolar.scp.aop.service.feign.client.BatteryDmFeign;
import com.trinasolar.scp.aop.service.feign.client.PowerFeignClient;
import com.trinasolar.scp.aop.service.repository.AopReleaseVersionRecordRepository;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.aop.service.utils.CalculateConstant;
import com.trinasolar.scp.aop.service.utils.CalculateDataHandlerUtils;
import com.trinasolar.scp.aop.service.utils.ContrastUtils;
import com.trinasolar.scp.aop.service.utils.DataHandleUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.common.api.util.UserUtil;
import jdk.nashorn.internal.ir.ContinueNode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @version v1.0
 * @className FinalVersionServiceImpl
 * @description 终版service实现类
 * @date 2022-10-28
 */
@Slf4j
@Service("finalVersionService")
public class FinalVersionServiceImpl implements FinalVersionService {

    @Autowired
    CalculateService calculateService;
    @Autowired
    OriginSaleService originSaleService;
    @Autowired
    AdjustService adjustService;
    @Autowired
    CalculateSaleService calculateSaleService;
    @Autowired
    CalculateAllocationEohService calculateAllocationEohService;
    @Autowired
    CalculateCapacityIeService calculateCapacityIeService;
    @Autowired
    CalculateSchedulingService calculateSchedulingService;
    @Autowired
    CalculatePurchaseDemandService calculatePurchaseDemandService;
    @Autowired
    CalculateEohService calculateEohService;
    @Autowired
    CalculateActualEohService calculateActualEohService;
    @Autowired
    ActualOtherService actualOtherService;

    @PersistenceContext
    EntityManager entityManager;

    @Autowired
    CellDistributionService cellDistributionService;

    @Autowired
    private ConfigCellAMinusPercentService configCellAMinusPercentService;

    @Autowired
    private PowerFeignClient powerFeignClient;

    @Autowired
    ImportActualCapacityService importActualCapacityService;

    @Autowired
    CalculateSchedulingDEConvert schedulingDEConvert;

    @Autowired
    OriginCapacityIeService originCapacityIeService;

    @Autowired
    BatteryDmFeign batteryDmFeign;

    @Autowired
    AopReleaseVersionRecordRepository aopReleaseVersionRecordRepository;

    @Autowired
    AopReleaseVersionRecordConvert aopReleaseVersionRecordConvert;

    private static final Joiner joiner = Joiner.on("_").useForNull("null");

    @Autowired
    OverallBudgetService overallBudgetService;

    @Autowired
    ExecutorService threadPoolExecutor;

    @Autowired
    BudgetSyncLogService budgetSyncLogService;

    /**
     * 查询终版销售数据
     *
     * @param dataVersion
     * @return
     */
    @Override
    public List<CalculateSaleDTO> queryMtoCalculateSale(String dataVersion) {
        return calculateSaleService.queryMtoCalculateSale(dataVersion);
    }

    /**
     * 分厂区产能利用率
     *
     * @param dataVersion
     * @return
     */
    @Override
    public UsageInventoryNew usageByInventory(String dataVersion) {
        return adjustService.usageByInventoryNew(dataVersion, true);
    }

    /**
     * 车间打折后
     *
     * @param dataVersion
     * @return
     */
    @Override
    public UsageInventory afterDiscount(String dataVersion) {
        return adjustService.afterDiscount(dataVersion);
    }

    /**
     * 供需汇总
     *
     * @param dataVersion
     * @return
     */
    @Override
    public List<QuarterYearDTO> supplyDemandSummary(String dataVersion) {
        Calculate calculate = calculateService.queryByVersion(dataVersion);
        //销售数据
        String salesVersion = calculate.getSalesVersion();
        String[] split = salesVersion.split(",");
        List<OriginSale> originSaleList = new ArrayList<>();
        if (split.length == 1) {
            originSaleList = originSaleService.queryByDataVersion(split[0]);
        } else {
            List<OriginSale> inlandSale = originSaleService.queryByDataVersion(split[0]).stream().filter(item -> CountryFlagEnum.INLAND.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            List<OriginSale> overseaSale = originSaleService.queryByDataVersion(split[1]).stream().filter(item -> CountryFlagEnum.OVERSEA.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            originSaleList.addAll(inlandSale);
            originSaleList.addAll(overseaSale);
        }
        if (calculate.getCalculateRange().equals(CalculateRangeEnum.INLAND.getCode())) {
            originSaleList = originSaleList.stream().filter(item -> CountryFlagEnum.INLAND.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
        }
        if (calculate.getCalculateRange().equals(CalculateRangeEnum.OVERSEA.getCode())) {
            originSaleList = originSaleList.stream().filter(item -> CountryFlagEnum.OVERSEA.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
        }
        if (calculate.getCalculateRange().equals(CalculateRangeEnum.INDONESIA.getCode())) {
            originSaleList = originSaleList.stream().filter(item -> CountryFlagEnum.INDONESIA.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
        }
        //组件EOH
        List<CalculateAllocationEoh> calculateEohList = calculateAllocationEohService.queryByVersionSource(dataVersion, AllocationTypeEnum.ADJUST);
        //电池外购
        List<CalculatePurchaseDemand> cellPurchaseList = calculatePurchaseDemandService.queryPurchaseThDemandByVersionType(dataVersion, ProductTypeEnum.CELL);
        List<Long> purchaseOldIdList = cellPurchaseList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculatePurchaseDemand::getOldId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(purchaseOldIdList)) {
            cellPurchaseList.removeIf(item -> purchaseOldIdList.contains(item.getId()));
        }
        //查询组件电池产能
        List<CalculateCapacityIe> moduleCellCapacity = calculateCapacityIeService.queryModuleCellCapacityByVersion(dataVersion);

        //查询组件电池产出
        List<CalculateScheduling> moduleCellScheduling = null;
        if (Objects.nonNull(calculate.getChannel()) && Objects.equals(CalculateChannelEnum.EXCEL.getCode(), calculate.getChannel())) {
            List<ImportActualCapacity> moduleCapacityList = importActualCapacityService.queryByVersionType(dataVersion, ProductTypeEnum.MODULE, ProductFromEnum.SELF_PRODUCTION);
            List<ImportActualCapacity> cellCapacityList = importActualCapacityService.queryByVersionType(dataVersion, ProductTypeEnum.CELL, ProductFromEnum.SELF_PRODUCTION);
            moduleCapacityList.addAll(cellCapacityList);
            moduleCellScheduling = schedulingDEConvert.importToList(moduleCapacityList);
        } else {
            moduleCellScheduling = calculateSchedulingService.queryModuleCellSchedulingByVersion(dataVersion);
            List<Long> schedulingOldIdList = moduleCellScheduling.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateScheduling::getOldId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(schedulingOldIdList)) {
                moduleCellScheduling.removeIf(item -> schedulingOldIdList.contains(item.getId()));
            }
        }

        //OEM
        List<CalculatePurchaseDemand> moduleOemList = calculatePurchaseDemandService.queryOemByVersionAndType(dataVersion, ProductTypeEnum.MODULE);
        List<Long> oemOldIdList = moduleOemList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculatePurchaseDemand::getOldId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(oemOldIdList)) {
            moduleOemList.removeIf(item -> oemOldIdList.contains(item.getId()));
        }
        List<QuarterYearDTO> quarterYearDTOS = Lists.newLinkedList();
        if (Objects.nonNull(calculate.getChannel()) && Objects.equals(CalculateChannelEnum.EXCEL.getCode(), calculate.getChannel())) {
            CapacitySummaryResultDTO moduleSummaryResultDTO = new CapacitySummaryResultDTO();
            CapacitySummaryResultDTO cellSummaryResultDTO = new CapacitySummaryResultDTO();
            if (StringUtils.isNotEmpty(calculate.getModuleVersion())) {
                moduleSummaryResultDTO = originCapacityIeService.productSeriesCapacitySummaryTotal(calculate.getModuleVersion(), ProductTypeEnum.MODULE.getCode(), calculate.getYear());
            }
            if (StringUtils.isNotEmpty(calculate.getCellVersion())) {
                cellSummaryResultDTO = originCapacityIeService.productSeriesCapacitySummaryTotal(calculate.getCellVersion(), ProductTypeEnum.CELL.getCode(), calculate.getYear());
            }
            quarterYearDTOS = CalculateDataHandlerUtils.calculateSupplyDemandSummaryImport(calculate.getYear(), originSaleList, calculateEohList, cellPurchaseList, moduleOemList, moduleCellScheduling, moduleSummaryResultDTO, cellSummaryResultDTO);
        } else {
            quarterYearDTOS = CalculateDataHandlerUtils.calculateSupplyDemandSummary(calculate.getYear(), originSaleList, calculateEohList, cellPurchaseList, moduleOemList, moduleCellScheduling, moduleCellCapacity);
        }
        if (CollectionUtils.isEmpty(quarterYearDTOS)) {
            return Collections.emptyList();
        }
        this.supplyDemandSummaryReplaceTotalEohHandle(quarterYearDTOS, originSaleList, calculate);
        return quarterYearDTOS;
    }

    private void supplyDemandSummaryReplaceTotalEohHandle(List<QuarterYearDTO> quarterYearDTOS, List<OriginSale> originSaleList, Calculate calculate) {
        SummaryDTO summaryDTO = new SummaryDTO();
        summaryDTO.setSales(CalculateDataHandlerUtils.generateTotalSales(originSaleList));
        //Summary 组件部分拆分
        adjustService.moduleSummary(calculate.getDataVersion(), summaryDTO, calculate.getCalculateRange(), calculate.getYear());
        if (Objects.nonNull(summaryDTO.getModuleGlobalEoh())) {
            quarterYearDTOS.forEach(ele -> {
                if (!Objects.equals("Total EOH", ele.getMw())) {
                    return;
                }
                ele.setQ1Quantity(summaryDTO.getModuleGlobalEoh().getQ1Quantity());
                ele.setQ2Quantity(summaryDTO.getModuleGlobalEoh().getQ2Quantity());
                ele.setQ3Quantity(summaryDTO.getModuleGlobalEoh().getQ3Quantity());
                ele.setQ4Quantity(summaryDTO.getModuleGlobalEoh().getQ4Quantity());
                ele.setYearQuantity(summaryDTO.getModuleGlobalEoh().getQ4Quantity());
            });
        }
    }

    /**
     * 对比
     *
     * @param dataVersion
     * @return
     */
    @Override
    public ContrastResultDTO contrast(String dataVersion) {
        ContrastResultDTO contrastResultDTO = new ContrastResultDTO();
        Calculate calculate = calculateService.queryByVersion(dataVersion);
        String range = calculate.getCalculateRange();
        List<CountryFlagEnum> mto = new ArrayList<>();
        List<CountryFlagEnum> mts = new ArrayList<>();
        List<CountryFlagEnum> mtos = new ArrayList<>();
        if (CalculateRangeEnum.BOTH.getCode().equals(range) || CalculateRangeEnum.INLAND.getCode().equals(range)) {
            if (CalculateModelEnum.MTO.getCode().equals(calculate.getInlandType())) {
                mto.add(CountryFlagEnum.INLAND);
            } else if (CalculateModelEnum.MTS.getCode().equals(calculate.getInlandType())) {
                mts.add(CountryFlagEnum.INLAND);
            } else {
                mtos.add(CountryFlagEnum.INLAND);
            }
        }
        if (CalculateRangeEnum.BOTH.getCode().equals(range) || CalculateRangeEnum.OVERSEA.getCode().equals(range)) {
            if (CalculateModelEnum.MTO.getCode().equals(calculate.getOverseaType())) {
                mto.add(CountryFlagEnum.OVERSEA);
            } else if (CalculateModelEnum.MTS.getCode().equals(calculate.getOverseaType())) {
                mts.add(CountryFlagEnum.OVERSEA);
            } else {
                mtos.add(CountryFlagEnum.OVERSEA);
            }
        }
        if (CollectionUtils.isNotEmpty(mto)) {
            contrastResultDTO.setModuleContrast(mtoModuleContrast(dataVersion, mto));
            contrastResultDTO.setCellContrast(cellContrast(dataVersion, mto));
        }
        if (CollectionUtils.isNotEmpty(mts)) {
            contrastResultDTO.setSaleContrast(saleContrast(dataVersion, calculate.getSalesVersion(), mts));
            contrastResultDTO.setModuleContrast(otherModuleContrast(dataVersion, mts));
            contrastResultDTO.setCellContrast(cellContrast(dataVersion, mts));
        }
        if (CollectionUtils.isNotEmpty(mtos)) {
            contrastResultDTO.setSaleContrast(saleContrast(dataVersion, calculate.getSalesVersion(), mtos));
            contrastResultDTO.setModuleContrast(otherModuleContrast(dataVersion, mtos));
            contrastResultDTO.setCellContrast(cellContrast(dataVersion, mtos));
        }
        return contrastResultDTO;
    }


    /**
     * 产销平衡组件EOH
     *
     * @param calculateVersion
     * @param countryFlagList
     * @return
     */
    private List<ContrastResultInnerDTO> otherModuleContrast(String calculateVersion, List<CountryFlagEnum> countryFlagList) {
        List<CalculateAllocationEoh> calculateActualEohList = calculateAllocationEohService.queryByVersionSource(calculateVersion, AllocationTypeEnum.ADJUST)
                .stream().filter(item -> !AreaEnums.RMA.getCode().equals(item.getArea()) && !AreaEnums.MFG.getCode().equals(item.getArea()) && !AreaEnums.Q2Q3.getCode().equals(item.getArea()) && !AreaEnums.OTHS.getCode().equals(item.getArea()) && !AreaEnums.OTHS.getDesc().equals(item.getArea())).collect(Collectors.toList());
        List<CalculateEoh> calculateEohList = calculateEohService.queryByVersionAndType(calculateVersion, ProductTypeEnum.MODULE).stream().filter(item -> ObjectUtils.isEmpty(item.getOldId())).collect(Collectors.toList());
        List<Long> idList2 = calculateEohList.stream().filter(item -> !AreaEnums.RMA.getCode().equals(item.getArea()) && ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateEoh::getOldId).collect(Collectors.toList());
        calculateEohList.removeIf(item -> idList2.contains(item.getId()));
        List<ContrastResultInnerDTO> innerList = new ArrayList<>();
        countryFlagList.forEach(countryFlag -> {
            Map<String, List<CalculateAllocationEoh>> calculateActualEohs = calculateActualEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateAllocationEoh::getProductSeries));
            Map<String, List<CalculateEoh>> calculateEohs = calculateEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateEoh::getProductSeries));
            Set<String> keySet = new HashSet<>();
            keySet.addAll(calculateActualEohs.keySet());
            keySet.addAll(calculateEohs.keySet());
            keySet.forEach(productSeries -> {
                List<CalculateAllocationEoh> allocationEohList = calculateActualEohs.get(productSeries);
                List<CalculateEoh> eohList = calculateEohs.get(productSeries);
                ContrastResultInnerDTO item1 = new ContrastResultInnerDTO();
                item1.setCountryFlag(countryFlag.getCode());
                item1.setProductSeries(productSeries);
                item1.setLevel(ContrastUtils.LEVEL2);
                item1.setMw(ContrastUtils.MODULEEOH2);
                Integer country = Priority.Country.getByCode(countryFlag.getCode());
                Integer series = Priority.Module.getByCode(productSeries);
                item1.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(allocationEohList)) {
                    item1.initQuarterZero();
                } else {
                    item1.setQ1Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM3Quantity).collect(Collectors.toList())));
                    item1.setQ2Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM6Quantity).collect(Collectors.toList())));
                    item1.setQ3Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM9Quantity).collect(Collectors.toList())));
                    item1.setQ4Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM12Quantity).collect(Collectors.toList())));
                    item1.initYearEoh();
                }
                innerList.add(item1);
                ContrastResultInnerDTO item2 = new ContrastResultInnerDTO();
                item2.setCountryFlag(countryFlag.getCode());
                item2.setProductSeries(productSeries);
                item2.setMw(ContrastUtils.MODULEEOH1);
                item2.setLevel(ContrastUtils.LEVEL1);
                item2.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(eohList)) {
                    item2.initQuarterZero();
                } else {
                    item2.setQ1Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM3Quantity).collect(Collectors.toList())));
                    item2.setQ2Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM6Quantity).collect(Collectors.toList())));
                    item2.setQ3Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM9Quantity).collect(Collectors.toList())));
                    item2.setQ4Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM12Quantity).collect(Collectors.toList())));
                    item2.initYearEoh();
                }
                innerList.add(item2);
                innerList.add(ContrastUtils.contrast(item1, item2, ContrastUtils.GAP, ContrastUtils.LEVEL3, ProductTypeEnum.CELL, country * 100 + series));
            });
        });
        return innerList.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 以销定产组件EOH对比
     *
     * @param calculateVersion
     * @param countryFlagList
     * @return
     */
    private List<ContrastResultInnerDTO> mtoModuleContrast(String calculateVersion, List<CountryFlagEnum> countryFlagList) {
        List<CalculateAllocationEoh> calculateActualEohList = calculateAllocationEohService.queryByVersionSource(calculateVersion, AllocationTypeEnum.ADJUST)
                .stream().filter(item -> !AreaEnums.RMA.getCode().equals(item.getArea()) && !AreaEnums.MFG.getCode().equals(item.getArea()) && !AreaEnums.Q2Q3.getCode().equals(item.getArea()) && !AreaEnums.OTHS.getCode().equals(item.getArea()) && !AreaEnums.OTHS.getDesc().equals(item.getArea())).collect(Collectors.toList());
        List<CalculateEoh> calculateEohList = calculateEohService.queryByVersionAndType(calculateVersion, ProductTypeEnum.MODULE).stream().filter(item -> !AreaEnums.RMA.getCode().equals(item.getArea()) && ObjectUtils.isEmpty(item.getOldId())).collect(Collectors.toList());
        List<Long> idList2 = calculateEohList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateEoh::getOldId).collect(Collectors.toList());
        calculateEohList.removeIf(item -> idList2.contains(item.getId()));
        List<ContrastResultInnerDTO> innerList = new ArrayList<>();
        countryFlagList.forEach(countryFlag -> {
            Map<String, List<CalculateAllocationEoh>> calculateActualEohs = calculateActualEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateAllocationEoh::getProductSeries));
            Map<String, List<CalculateEoh>> calculateEohs = calculateEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateEoh::getProductSeries));
            Set<String> keySet = new HashSet<>();
            keySet.addAll(calculateActualEohs.keySet());
            keySet.addAll(calculateEohs.keySet());
            keySet.forEach(productSeries -> {
                List<CalculateAllocationEoh> allocationEohList = calculateActualEohs.get(productSeries);
                List<CalculateEoh> eohList = calculateEohs.get(productSeries);

                ContrastResultInnerDTO item1 = new ContrastResultInnerDTO();
                item1.setCountryFlag(countryFlag.getCode());
                item1.setProductSeries(productSeries);
                item1.setLevel(ContrastUtils.LEVEL2);
                item1.setMw(ContrastUtils.MODULEEOH2);
                Integer country = Priority.Country.getByCode(countryFlag.getCode());
                Integer series = Priority.Module.getByCode(productSeries);
                item1.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(allocationEohList)) {
                    item1.initQuarterZero();
                } else {
                    item1.setQ1Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM3Quantity).collect(Collectors.toList())));
                    item1.setQ2Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM6Quantity).collect(Collectors.toList())));
                    item1.setQ3Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM9Quantity).collect(Collectors.toList())));
                    item1.setQ4Quantity(DataHandleUtils.calSumZero(allocationEohList.stream().map(CalculateAllocationEoh::getM12Quantity).collect(Collectors.toList())));
                    item1.initYearEoh();
                }
                innerList.add(item1);
                ContrastResultInnerDTO item2 = new ContrastResultInnerDTO();
                item2.setCountryFlag(countryFlag.getCode());
                item2.setProductSeries(productSeries);
                item2.setMw(ContrastUtils.MODULEEOH1);
                item2.setLevel(ContrastUtils.LEVEL1);
                item2.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(eohList)) {
                    item2.initQuarterZero();
                } else {
                    item2.setQ1Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM3Quantity).collect(Collectors.toList())));
                    item2.setQ2Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM6Quantity).collect(Collectors.toList())));
                    item2.setQ3Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM9Quantity).collect(Collectors.toList())));
                    item2.setQ4Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM12Quantity).collect(Collectors.toList())));
                    item2.initYearEoh();
                }
                innerList.add(item2);
                innerList.add(ContrastUtils.contrast(item1, item2, ContrastUtils.GAP, ContrastUtils.LEVEL3, ProductTypeEnum.MODULE, country * 100 + series));
            });
        });
        return innerList.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 电池对比
     *
     * @param calculateVersion
     * @param countryFlagList
     * @return
     */
    private List<ContrastResultInnerDTO> cellContrast(String calculateVersion, List<CountryFlagEnum> countryFlagList) {
        List<CalculateActualEoh> calculateActualEohList = calculateActualEohService.queryByVersionAndType(calculateVersion, ProductTypeEnum.CELL);
        List<Long> idList1 = calculateActualEohList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateActualEoh::getOldId).collect(Collectors.toList());
        calculateActualEohList.removeIf(item -> idList1.contains(item.getId()));
        List<CalculateEoh> calculateEohList = calculateEohService.queryByVersionAndType(calculateVersion, ProductTypeEnum.CELL);
        List<Long> idList2 = calculateEohList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateEoh::getOldId).collect(Collectors.toList());
        calculateEohList.removeIf(item -> idList2.contains(item.getId()));
        List<ContrastResultInnerDTO> innerList = new ArrayList<>();
        countryFlagList.forEach(countryFlag -> {
            Map<String, List<CalculateActualEoh>> calculateActualEohs = calculateActualEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateActualEoh::getProductSeries));
            Map<String, List<CalculateEoh>> calculateEohs = calculateEohList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.groupingBy(CalculateEoh::getProductSeries));
            Set<String> keySet = new HashSet<>();
            keySet.addAll(calculateActualEohs.keySet());
            keySet.addAll(calculateEohs.keySet());
            keySet.forEach(productSeries -> {
                List<CalculateActualEoh> actualEohList = calculateActualEohs.get(productSeries);
                List<CalculateEoh> eohList = calculateEohs.get(productSeries);
                ContrastResultInnerDTO item1 = new ContrastResultInnerDTO();
                item1.setCountryFlag(countryFlag.getCode());
                item1.setProductSeries(productSeries);
                item1.setLevel(ContrastUtils.LEVEL2);
                item1.setMw(ContrastUtils.CELLEOH2);
                Integer country = Priority.Country.getByCode(countryFlag.getCode());
                Integer series = Priority.Cell.getByCode(productSeries);
                item1.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(actualEohList)) {
                    item1.initMonthZero();
                } else {
                    item1.setM1Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM1Quantity).collect(Collectors.toList())));
                    item1.setM2Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM2Quantity).collect(Collectors.toList())));
                    item1.setM3Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM3Quantity).collect(Collectors.toList())));
                    item1.setM4Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM4Quantity).collect(Collectors.toList())));
                    item1.setM5Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM5Quantity).collect(Collectors.toList())));
                    item1.setM6Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM6Quantity).collect(Collectors.toList())));
                    item1.setM7Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM7Quantity).collect(Collectors.toList())));
                    item1.setM8Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM8Quantity).collect(Collectors.toList())));
                    item1.setM9Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM9Quantity).collect(Collectors.toList())));
                    item1.setM10Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM10Quantity).collect(Collectors.toList())));
                    item1.setM11Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM11Quantity).collect(Collectors.toList())));
                    item1.setM12Quantity(DataHandleUtils.calSumZero(actualEohList.stream().map(CalculateActualEoh::getM12Quantity).collect(Collectors.toList())));
                    item1.initEoh();
                }
                innerList.add(item1);
                ContrastResultInnerDTO item2 = new ContrastResultInnerDTO();
                item2.setCountryFlag(countryFlag.getCode());
                item2.setLevel(ContrastUtils.LEVEL1);
                item2.setProductSeries(productSeries);
                item2.setMw(ContrastUtils.CELLEOH1);
                item2.setPriority(country * 100 + series);
                if (CollectionUtils.isEmpty(eohList)) {
                    item2.initMonthZero();
                } else {
                    item2.setM1Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM1Quantity).collect(Collectors.toList())));
                    item2.setM2Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM2Quantity).collect(Collectors.toList())));
                    item2.setM3Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM3Quantity).collect(Collectors.toList())));
                    item2.setM4Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM4Quantity).collect(Collectors.toList())));
                    item2.setM5Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM5Quantity).collect(Collectors.toList())));
                    item2.setM6Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM6Quantity).collect(Collectors.toList())));
                    item2.setM7Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM7Quantity).collect(Collectors.toList())));
                    item2.setM8Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM8Quantity).collect(Collectors.toList())));
                    item2.setM9Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM9Quantity).collect(Collectors.toList())));
                    item2.setM10Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM10Quantity).collect(Collectors.toList())));
                    item2.setM11Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM11Quantity).collect(Collectors.toList())));
                    item2.setM12Quantity(DataHandleUtils.calSumZero(eohList.stream().map(CalculateEoh::getM12Quantity).collect(Collectors.toList())));
                    item2.initEoh();
                }
                innerList.add(item2);
                innerList.add(ContrastUtils.contrast(item1, item2, ContrastUtils.GAP, ContrastUtils.LEVEL3, ProductTypeEnum.CELL, country * 100 + series));
            });
        });
        return innerList.stream().sorted().collect(Collectors.toList());
    }

    /**
     * 销售数据对比
     *
     * @param calculateVersion 计算版本号
     * @param salesVersion     GSM原始销售版本号
     * @param countryFlagList
     * @return
     */
    private List<ContrastResultInnerDTO> saleContrast(String calculateVersion, String salesVersion, List<CountryFlagEnum> countryFlagList) {
        List<CalculateSale> calculateSaleList = calculateSaleService.queryByCalculateVersion(calculateVersion, AllocationTypeEnum.ADJUST);
        String[] split = salesVersion.split(",");
        List<OriginSale> originSaleList = new ArrayList<>();
        if (split.length == 1) {
            originSaleList = originSaleService.queryByDataVersion(split[0]);
        } else {
            List<OriginSale> inlandSale = originSaleService.queryByDataVersion(split[0]).stream().filter(item -> CountryFlagEnum.INLAND.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            List<OriginSale> overseaSale = originSaleService.queryByDataVersion(split[1]).stream().filter(item -> CountryFlagEnum.OVERSEA.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            originSaleList.addAll(inlandSale);
            originSaleList.addAll(overseaSale);
        }
        List<ContrastResultInnerDTO> innerList = new ArrayList<>();
        List<OriginSale> finalOriginSaleList = originSaleList;
        countryFlagList.forEach(countryFlag -> {
            List<CalculateSale> calculateSales = calculateSaleList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            List<OriginSale> originSales = finalOriginSaleList.stream().filter(item -> countryFlag.getCode().equals(item.getCountryFlag())).collect(Collectors.toList());
            ContrastResultInnerDTO item1 = new ContrastResultInnerDTO();
            item1.setCountryFlag(countryFlag.getCode());
            item1.setLevel(ContrastUtils.LEVEL1);
            item1.setMw(ContrastUtils.SALE1);
            item1.setPriority(Priority.Module.getByCode("productSeries"));
            if (CollectionUtils.isEmpty(calculateSales)) {
                item1.initMonthZero();
            } else {
                item1.setQ1Quantity(DataHandleUtils.calSumZero(calculateSales.stream().map(CalculateSale::getQ1Quantity).collect(Collectors.toList())));
                item1.setQ2Quantity(DataHandleUtils.calSumZero(calculateSales.stream().map(CalculateSale::getQ2Quantity).collect(Collectors.toList())));
                item1.setQ3Quantity(DataHandleUtils.calSumZero(calculateSales.stream().map(CalculateSale::getQ3Quantity).collect(Collectors.toList())));
                item1.setQ4Quantity(DataHandleUtils.calSumZero(calculateSales.stream().map(CalculateSale::getQ4Quantity).collect(Collectors.toList())));
                item1.initYearSum();
            }
            innerList.add(item1);
            ContrastResultInnerDTO item2 = new ContrastResultInnerDTO();
            item2.setCountryFlag(countryFlag.getCode());
            item2.setLevel(ContrastUtils.LEVEL2);
            item2.setMw(ContrastUtils.SALE2);
            item2.setPriority(Priority.Module.getByCode("productSeries"));
            if (CollectionUtils.isEmpty(originSales)) {
                item2.initMonthZero();
            } else {
                item2.setQ1Quantity(DataHandleUtils.calSumZero(originSales.stream().map(OriginSale::getQ1Quantity).collect(Collectors.toList())));
                item2.setQ2Quantity(DataHandleUtils.calSumZero(originSales.stream().map(OriginSale::getQ2Quantity).collect(Collectors.toList())));
                item2.setQ3Quantity(DataHandleUtils.calSumZero(originSales.stream().map(OriginSale::getQ3Quantity).collect(Collectors.toList())));
                item2.setQ4Quantity(DataHandleUtils.calSumZero(originSales.stream().map(OriginSale::getQ4Quantity).collect(Collectors.toList())));
                item2.initYearSum();
            }
            innerList.add(item2);
            innerList.add(ContrastUtils.contrast(item1, item2, ContrastUtils.SALEGAP, ContrastUtils.LEVEL3, ProductTypeEnum.CELL, 0));
        });
        return innerList.stream().sorted().collect(Collectors.toList());
    }

    @Override
    public Map<String, String> queryCreateBy() {
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        QCalculate calculate = QCalculate.calculate;
        List<String> fetch = factory.selectDistinct(calculate.createdBy).from(calculate).where(calculate.versionType.ne(VersionTypeEnum.CALCULATIONVERSION.getCode())).fetch();
        Map<String, String> userNamesByIds = UserUtil.getUserNamesByIds(fetch);
        return userNamesByIds;
    }

    @Override
    public void releaseVersion(DataVersionReleaseReqDTO reqDTO) {
        Assert.isTrue(Objects.nonNull(reqDTO) && StringUtils.isNotEmpty(reqDTO.getDataVersion()), "请先选择要发布的版本");
        Calculate calculate = calculateService.queryByVersion(reqDTO.getDataVersion());
        Assert.notNull(calculate, "数据不存在");
        Assert.isTrue(CollectionUtils.isNotEmpty(reqDTO.getReleasePortSet()), "请选择发布接口");
        long budgetCount = reqDTO.getReleasePortSet().stream().filter(ele -> Objects.equals(ReleaseVersionPortEnum.BUDGET.getCode(), ele)).count();
        if (budgetCount > 0) {
            Assert.isTrue(Objects.nonNull(reqDTO.getBudgetVersion()) && Objects.nonNull(reqDTO.getBudgetScene()), "请选择预算场景和预算版本");
        }
        /* LocalDate now = LocalDate.now();
        int year = now.getYear();
        Assert.isTrue(calculate.getYear() != null && calculate.getYear() >= year, String.format("发布的版本年份【%s】不能早于当前年份【%s】", calculate.getYear(), year));
 */
        reqDTO.getReleasePortSet().forEach(port -> {
            if (Objects.equals(ReleaseVersionPortEnum.BUDGET.getCode(), port)) {
                this.releaseVersionBudgetHandle(calculate, reqDTO);
            } else if (Objects.equals(ReleaseVersionPortEnum.POWER.getCode(), port)) {
                this.releaseVersionPowerHandle(calculate, reqDTO.getBusinessType());
            } else if (Objects.equals(ReleaseVersionPortEnum.PLAN_INSIDE.getCode(), port)) {
                this.releaseVersionPlanInsideHandle(calculate);
            }
        });
    }

    /**
     * 发布版本 - 全面预算系统对接
     *
     * @param calculate
     * @param reqDTO
     */
    private void releaseVersionBudgetHandle(Calculate calculate, DataVersionReleaseReqDTO reqDTO) {
        OverallBudgetBaseDTO baseDTO = OverallBudgetBaseDTO
                .builder()
                .dataVersion(calculate.getDataVersion())
                .years(String.valueOf(calculate.getYear()))
                .budgetScene(reqDTO.getBudgetScene())
                .budgetVersion(reqDTO.getBudgetVersion())
                .pushDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .build();

        List<String> errorMsgList = Lists.newArrayList();
        try {
            //  组件产品系列排产量提前执行，执行完成后再触发其他接口
            this.overallBudgetServiceHandle(OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT, baseDTO);
        } catch (Exception ex) {
            log.error("全面预算系统对接 releaseVersionBudgetHandle exception 当前调用接口：{} 入参：{}",
                    OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT.name(), JSONUtil.toJsonStr(baseDTO), ex);
            errorMsgList.add("全面预算系统对接-" + OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT.getDesc() + "接口调用出错，原因：" + ex.getMessage() + "，如无法定位请联系管理员排查！");
        } finally {
            budgetSyncLogService.saveAll(OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT, baseDTO);
        }

        List<OverallBudgetInterfaceEnum> interfaceEnums = Arrays.asList(OverallBudgetInterfaceEnum.values());
        CountDownLatch countDownLatch = new CountDownLatch(interfaceEnums.size() - 1);
        interfaceEnums.forEach(ele -> {
            //  组件产品系列排产量已执行 跳过
            if (OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT == ele) return;
            threadPoolExecutor.execute(() -> {
                try {
                    this.overallBudgetServiceHandle(ele, baseDTO);
                } catch (Exception ex) {
                    log.error("全面预算系统对接 releaseVersionBudgetHandle exception 当前调用接口：{} 入参：{}",
                            ele.name(), JSONUtil.toJsonStr(baseDTO), ex);
                    errorMsgList.add("全面预算系统对接-" + ele.getDesc() + "接口调用出错，原因：" + ex.getMessage() + "，如无法定位请联系管理员排查！");
                } finally {
                    countDownLatch.countDown();
                    budgetSyncLogService.saveAll(ele, baseDTO);
                }
            });
        });

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        Assert.isTrue(CollectionUtils.isEmpty(errorMsgList), "全面预算以下接口对接出错：\n" + String.join("\n", errorMsgList));
    }

    public void overallBudgetServiceHandle(OverallBudgetInterfaceEnum overallBudgetInterfaceEnum,
                                           OverallBudgetBaseDTO baseDTO) throws Exception {
        if (OverallBudgetInterfaceEnum.SYNC_MODULE_OUTPUT == overallBudgetInterfaceEnum) {
            //  组件产品系列排产量
            overallBudgetService.syncModuleOutput(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_HORIZONTAL_OR_VERTICAL == overallBudgetInterfaceEnum) {
            //  组件属性-横竖装比例
            overallBudgetService.syncHorizontalOrVertical(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_PRODUCTFAMILY_RAITO == overallBudgetInterfaceEnum) {
            //  组件属性-产品族比例
            overallBudgetService.syncProductFamilyRaito(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_CELL_SCHEDULE == overallBudgetInterfaceEnum) {
            //  电池排产量
            overallBudgetService.syncCellSchedule(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_CELL_SALE == overallBudgetInterfaceEnum) {
            //  电池销售
            overallBudgetService.syncCellSale(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_CELL_PURCHASE == overallBudgetInterfaceEnum) {
            //  电池片采购量
            overallBudgetService.syncCellPurchase(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_WAFER_PURCHASE == overallBudgetInterfaceEnum) {
            //  硅片采购
            overallBudgetService.syncWaferPurchase(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_CELL_INVENTORY_DAYS == overallBudgetInterfaceEnum) {
            //  主材库存天数-电池片
            overallBudgetService.syncCellInventoryDays(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_WAFER_INVENTORY_DAYS == overallBudgetInterfaceEnum) {
            //  主材库存天数-硅片
            overallBudgetService.syncWaferInventoryDays(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_MODULE_DISCHARGE == overallBudgetInterfaceEnum) {
            //  组件车间A-使用量
            overallBudgetService.syncModuleDischarge(baseDTO);
        } else if (OverallBudgetInterfaceEnum.SYNC_CELL_PURCHASEDATA == overallBudgetInterfaceEnum) {
            //  全面预算-电池片采购量
            overallBudgetService.syncCellPurchaseData(baseDTO);
        }
    }

    /**
     * 发布版本 - 长期功率预测接口
     *
     * @param calculate
     */
    private void releaseVersionPowerHandle(Calculate calculate, String type) {
        List<PowerSupplyAopSaveDTO> saveDTOs = Lists.newLinkedList();
        if(StringUtils.isBlank(type)) {
            // 自产数据
            this.powerSelfProductionHandle(saveDTOs, calculate);
        }
        // 外购数据
        this.powerPurchaseHandle(saveDTOs, calculate);
        powerFeignClient.saveAll(saveDTOs);
    }




    /**
     * 发布版本 - 计划内部接口
     *
     * @param calculate
     */
    private void releaseVersionPlanInsideHandle(Calculate calculate) {
        // BDM
        List<CellDistributionInfo> result = new ArrayList<>();
        List<ActualOtherDTO> otherResult = new ArrayList<>();
        Map<String, LovLineDTO> aopCellShardMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        //获取版本下已有电池分配数据
        getCellDistributionInfo(result, calculate, aopCellShardMap);
        boolean success = batteryDmFeign.toSendSyncData(result).getBody().isSuccess();
        if (!success) {
            throw new BizException("发送版本已有电池分配数据出错");
        }
        //获取电池外卖数据
        getActualOtherInfo(otherResult, calculate, aopCellShardMap);
        boolean otherSuccess = batteryDmFeign.toSendSyncDataOther(otherResult).getBody().isSuccess();
        if (!otherSuccess) {
            throw new BizException("发送版本对应年份电池硅片外卖数据出错");
        }

        // MAPS && DP
        AopReleaseVersionRecord record = AopReleaseVersionRecord
                .builder()
                .year(calculate.getYear())
                .aopVersion(calculate.getDataVersion())
                .aopVersionId(calculate.getId())
                .versionType(calculate.getVersionType())
                .preVersion(calculate.getPreVersion())
                .releaseDate(LocalDateTime.now())
                .mapsFlag(YesOrNoEnums.Y.getCode())
                .dpFlag(YesOrNoEnums.Y.getCode())
                .build();
        aopReleaseVersionRecordRepository.save(record);
    }

    private void getActualOtherInfo(List<ActualOtherDTO> otherResult, Calculate calculate, Map<String, LovLineDTO> aopCellShardMap) {
        ActualOtherQuery query = new ActualOtherQuery();
        query.setPageSize(10000);
        query.setYear(calculate.getYear());
        query.setProductType(Arrays.asList("CELL", "WAFER"));
        query.setProductFromList(Arrays.asList("OUTGOING"));
        List<ActualOtherDTO> actualOtherList = actualOtherService.queryByPage(query).getContent();
        for (ActualOtherDTO otherDTO : actualOtherList) {
            ActualOtherDTO dto = new ActualOtherDTO();
            BeanUtils.copyProperties(otherDTO, dto);
            //此处要求传递过去使用lovName
            dto.setCellShard(aopCellShardMap.get(dto.getCellShard()).getLovName());
            otherResult.add(dto);
        }
    }

    private void getCellDistributionInfo(List<CellDistributionInfo> result, Calculate calculate, Map<String, LovLineDTO> aopCellShardMap) {

        List<CellDistribution> cellDistributions = cellDistributionService.queryCellDistributionByVersion(calculate.getDataVersion());
        for (CellDistribution cellDistribution : cellDistributions) {
            CellDistributionInfo dto = new CellDistributionInfo();
            BeanUtils.copyProperties(cellDistribution, dto);
            //此处要求传递过去使用lovName
            dto.setCellShard(aopCellShardMap.get(dto.getCellShard()).getLovName());
            result.add(dto);
        }
    }

    private void powerSelfProductionHandle(List<PowerSupplyAopSaveDTO> saveDTOs, Calculate calculate) {
        //自产所有数据
        List<PowerSupplyAopSaveDTO> iePowerSupplyAopSaveDTOList = Lists.newArrayList();
        //自产A-所有数据
        List<PowerSupplyAopSaveDTO> ieAMinusPowerSupplyAopSaveDTOList = Lists.newArrayList();
        //自产12月数据
        List<PowerSupplyAopSaveDTO> decemberIePowerSupplyAopSaveDTOList = Lists.newArrayList();
        //自产A-12月数据
        List<PowerSupplyAopSaveDTO> decemberIeAMinusPowerSupplyAopSaveDTOList = Lists.newArrayList();

        Map<String, LovLineDTO> aopCellShardMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        Map<String, LovLineDTO> apsPowerCellSeriesTypeMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.APS_POWER_CELL_SERIES_TYPE);
        Map<String, LovLineDTO> effectPowerCellMap = apsPowerCellSeriesTypeMap.values().stream().map(ele -> {
            if (StringUtils.isNotEmpty(ele.getAttribute1()) && StringUtils.isNumeric(ele.getAttribute1())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(ele.getAttribute1()));
                ele.setAttribute1(Objects.nonNull(lovLineDTO) ? lovLineDTO.getLovValue() : null);
            }
            if (StringUtils.isNotEmpty(ele.getAttribute4()) && StringUtils.isNumeric(ele.getAttribute4())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(ele.getAttribute4()));
                ele.setAttribute4(Objects.nonNull(lovLineDTO) ? lovLineDTO.getLovName() : null);
            }
            return ele;
        }).collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getAttribute1(), ele.getAttribute4()), Function.identity(), (v1, v2) -> v1));
        Map<String, LovLineDTO> basePlaceMap = LovService.getAllByHeaderCode(LovHeaderCodeConstant.BASE_PLACE);
        //自产A-电池比例分组：车间+电池型号+基地+国内/海外
        Map<String, List<ConfigCellAMinusPercent>> ieCellAMinusPercentConfigMap = configCellAMinusPercentService.getIECellMinusPercentConfig(String.valueOf(calculate.getYear())).stream().collect(Collectors.groupingBy(config -> joiner.join(config.getWorkshop(), config.getCellModel(), config.getBasePlace(), config.getCountryFlag())));
        // 自产数据组装：1、"电池分配页"获取数据来源 2、LOV转换校验 3、数据组装，数据源与传参数据关系为1对12（即一条数据对应12个月）
        List<CellDistribution> cellDistributions = cellDistributionService.queryCellDistributionByVersion(calculate.getDataVersion());
        cellDistributions.forEach(ele -> {
            LovLineDTO cellShardLovLineDTO = aopCellShardMap.get(ele.getCellShard());
            String cellShard = Objects.nonNull(cellShardLovLineDTO) ? cellShardLovLineDTO.getLovName() : null;
            Assert.notNull(cellShard, String.format("自产-电池分配数据未匹配到LOV-AOP电池分片方式【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
                    ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));

            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getProductSeries(), cellShard));
            Assert.notNull(cellSeriesLovLineDTO, String.format("自产-电池分配数据未匹配到LOV-电池型号映射关系【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
                    ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));

            LovLineDTO basePlaceLovLineDTO = basePlaceMap.get(ele.getBasePlace());
            Assert.isTrue(Objects.nonNull(basePlaceLovLineDTO) && StringUtils.isNotEmpty(basePlaceLovLineDTO.getAttribute2()),
                    String.format("自产-电池分配数据未匹配到LOV-生产基地对应的国内/国外【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
                            ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));

            LovLineDTO isOverseaLovLineDTO = LovUtils.get(Long.valueOf(basePlaceLovLineDTO.getAttribute2()));
            Assert.notNull(isOverseaLovLineDTO, String.format("自产-电池分配数据匹配的LOV-生产基地对应的国内/国外数据未匹配数据【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
                    ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));

            //自产A-(从自产中用自产A-比例配置筛选出)
            List<ConfigCellAMinusPercent> configCellAMinusPercentList = ieCellAMinusPercentConfigMap.get(joiner.join(ele.getWorkshop(), ele.getProductSeries(), ele.getBasePlace(), ele.getCountryFlag()));
            if (CollectionUtils.isNotEmpty(configCellAMinusPercentList)) {
                //维度唯一只有一个
                ConfigCellAMinusPercent configCellAMinusPercent = configCellAMinusPercentList.get(0);
                IntStream.rangeClosed(1, 12).forEach(month -> {
                    BigDecimal monthQuantity = (BigDecimal) ReflectUtil.getFieldValue(ele, "m" + month + "Quantity");
                    String monthPercent = (String) ReflectUtil.getFieldValue(configCellAMinusPercent, "m" + month + "Percent");
                    BigDecimal monthPercentBigDecimal = parsingDataToBigDecimal(monthPercent);
                    if (monthQuantity != null && monthPercentBigDecimal != null) {
                        BigDecimal supplyQuantity = monthQuantity.multiply(monthPercentBigDecimal).setScale(3, RoundingMode.HALF_UP);
                        PowerSupplyAopSaveDTO build = PowerSupplyAopSaveDTO
                                .builder()
                                .isOversea(isOverseaLovLineDTO.getLovValue())
                                .cellType(joiner.join(cellSeriesLovLineDTO.getLovValue(), "A-"))
                                .year(String.valueOf(calculate.getYear()))
                                .month(String.valueOf(month))
                                .supplier(ele.getWorkshop())
                                .supplyType("自产")
                                .supplyQuantity(supplyQuantity)
                                .dataVersion(calculate.getFinalVersionType())
                                .publicVersion(calculate.getDataVersion())
                                .build();
                        ieAMinusPowerSupplyAopSaveDTOList.add(build);
                        if (month == Month.DECEMBER.getValue()) {
                            decemberIeAMinusPowerSupplyAopSaveDTOList.add(build);
                        }
                        //数据量减去A-数量
                        ReflectUtil.setFieldValue(ele, "m" + month + "Quantity", monthQuantity.subtract(supplyQuantity));
                    }
                });
            }
            //自产
            IntStream.rangeClosed(1, 12).forEach(month -> {
                BigDecimal monthQuantity = (BigDecimal) ReflectUtil.getFieldValue(ele, "m" + month + "Quantity");
                if (monthQuantity != null) {
                    PowerSupplyAopSaveDTO build = PowerSupplyAopSaveDTO
                            .builder()
                            .isOversea(isOverseaLovLineDTO.getLovValue())
                            .cellType(cellSeriesLovLineDTO.getLovValue())
                            .year(String.valueOf(calculate.getYear()))
                            .month(String.valueOf(month))
                            .supplier(ele.getWorkshop())
                            .supplyType("自产")
                            .supplyQuantity(monthQuantity.setScale(3, RoundingMode.HALF_UP))
                            .dataVersion(calculate.getFinalVersionType())
                            .publicVersion(calculate.getDataVersion())
                            .build();
                    iePowerSupplyAopSaveDTOList.add(build);
                    if (month == Month.DECEMBER.getValue()) {
                        decemberIePowerSupplyAopSaveDTOList.add(build);
                    }
                }
            });
        });
        //新增自产当前年1~12月数据
        saveDTOs.addAll(iePowerSupplyAopSaveDTOList);
        //新增自产次年1~(now.getMonthValue()-1)月数据：版本年是当前年且当前月份>1,将12月数据作为次年1~(now.getMonthValue()-1)月的数据
        saveDTOs.addAll(buildNextYearPowerSupplyAopSaveDTO(calculate.getYear(), decemberIePowerSupplyAopSaveDTOList));
        //新增自产A-当前年1~12月数据
        saveDTOs.addAll(ieAMinusPowerSupplyAopSaveDTOList);
        //新增自产A-次年1~(now.getMonthValue()-1)月数据：版本年是当前年且当前月份>1,将12月数据作为次年1~(now.getMonthValue()-1)月的数据
        saveDTOs.addAll(buildNextYearPowerSupplyAopSaveDTO(calculate.getYear(), decemberIeAMinusPowerSupplyAopSaveDTOList));
    }

    private void powerPurchaseHandle(List<PowerSupplyAopSaveDTO> saveDTOs, Calculate calculate) {
        SummaryDTO summaryDTO = adjustService.querySummary(calculate.getDataVersion(), YesOrNoEnums.N);

        List<ConfigCellAMinusPercent> percents = configCellAMinusPercentService.getPurchaseCellSupplyPercentConfig(String.valueOf(calculate.getYear()));
        Map<String, List<ConfigCellAMinusPercent>> percentMap = percents.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getCellModel())));

        this.powerPurchaseHandleByCountry(summaryDTO.getCellInland(), CountryFlagEnum.INLAND, calculate, percentMap, saveDTOs);
        this.powerPurchaseHandleByCountry(summaryDTO.getCellIndonesia(), CountryFlagEnum.INDONESIA, calculate, percentMap, saveDTOs);
        this.powerPurchaseHandleByCountry(summaryDTO.getCellOverSea(), CountryFlagEnum.OVERSEA, calculate, percentMap, saveDTOs);
    }

    private void powerPurchaseHandleByCountry(SummaryInnerDTO<List<SummaryInnerDTO<List<SummaryInnerDTO<?>>>>> cellDTO,
                                              CountryFlagEnum countryFlagEnum,
                                              Calculate calculate,
                                              Map<String, List<ConfigCellAMinusPercent>> percentMap,
                                              List<PowerSupplyAopSaveDTO> saveDTOs) {
        if (Objects.isNull(cellDTO) || CollectionUtils.isEmpty(cellDTO.getNextFloor())) {
            return;
        }
        SummaryInnerDTO<List<SummaryInnerDTO<?>>> nextFloor = cellDTO.getNextFloor().stream()
                .filter(ele -> Objects.equals(CalculateConstant.PURCHASE, ele.getMw())).findFirst().orElse(null);
        if (Objects.isNull(nextFloor) || CollectionUtils.isEmpty(nextFloor.getNextFloor())) {
            return;
        }
        nextFloor.getNextFloor().forEach(ele -> {
            List<ConfigCellAMinusPercent> percents = percentMap.get(StringUtils.join(countryFlagEnum.getCode(), ele.getMw()));
            if(CollectionUtils.isEmpty(percents)){
                log.error("外购电池供应比例未维护数据 【产地：{}】【电池型号：{}】", countryFlagEnum.getDesc(), ele.getMw());
            }else {
                percents.forEach(percentItem -> {
                    List<PowerSupplyAopSaveDTO> decemberPowerSupplyAopSaveDTOList = Lists.newArrayList();
                    IntStream.rangeClosed(1, 12).forEach(month -> {
                        BigDecimal monthQuantity = (BigDecimal) ReflectUtil.getFieldValue(ele, "m" + month + "Quantity");
                        String monthPercent = (String) ReflectUtil.getFieldValue(percentItem, "m" + month + "Percent");
                        BigDecimal monthPercentBigDecimal = parsingDataToBigDecimal(monthPercent);
                        PowerSupplyAopSaveDTO build = PowerSupplyAopSaveDTO
                                .builder()
                                .isOversea(countryFlagEnum.getCode())
                                .cellType(percentItem.getCellType())
                                .year(String.valueOf(calculate.getYear()))
                                .month(String.valueOf(month))
                                .supplier(percentItem.getSupplier())
                                .supplyType("外购")
                                .supplyQuantity(MathUtils.multiplication(monthQuantity, monthPercentBigDecimal).setScale(3, RoundingMode.HALF_UP))
                                .dataVersion(calculate.getFinalVersionType())
                                .publicVersion(calculate.getDataVersion())
                                .build();
                        saveDTOs.add(build);
                        if (month == Month.DECEMBER.getValue()) {
                            decemberPowerSupplyAopSaveDTOList.add(build);
                        }
                    });
                    //版本年是当前年且当前月份>1,将12月数据作为次年1~(now.getMonthValue()-1)月的数据
                    saveDTOs.addAll(buildNextYearPowerSupplyAopSaveDTO(calculate.getYear(), decemberPowerSupplyAopSaveDTOList));
                });
            }
        });
    }

    private List<PowerSupplyAopSaveDTO> buildNextYearPowerSupplyAopSaveDTO(Integer versionYear, List<PowerSupplyAopSaveDTO> decemberPowerSupplyAopSaveDTOList) {
        List<PowerSupplyAopSaveDTO> nextYearPowerSupplyAopSaveDTOList = Lists.newArrayList();
        LocalDate now = LocalDate.now();
        if (!(versionYear == now.getYear() && now.getMonthValue() > 1 && CollectionUtils.isNotEmpty(decemberPowerSupplyAopSaveDTOList))) {
            return Collections.emptyList();
        }
        String nextYear = String.valueOf(versionYear + 1);
        decemberPowerSupplyAopSaveDTOList.forEach(decemberPowerSupplyAopSaveDTO -> {
            List<PowerSupplyAopSaveDTO> monthPowerSupplyAopSaveDTOList = IntStream.rangeClosed(1, now.getMonthValue() - 1).mapToObj(month -> {
                //补充的次年月的数据
                PowerSupplyAopSaveDTO powerSupplyAopSaveDTO = new PowerSupplyAopSaveDTO();
                BeanUtils.copyProperties(decemberPowerSupplyAopSaveDTO, powerSupplyAopSaveDTO, "year", "month");
                //修改年月
                powerSupplyAopSaveDTO.setYear(nextYear);
                powerSupplyAopSaveDTO.setMonth(String.valueOf(month));
                return powerSupplyAopSaveDTO;
            }).collect(Collectors.toList());
            nextYearPowerSupplyAopSaveDTOList.addAll(monthPowerSupplyAopSaveDTOList);
        });
        return nextYearPowerSupplyAopSaveDTOList;
    }


    /**
     * 解析带有%的数值，去除%，并转换为BigDecimal
     *
     * @param cellValue
     * @return
     */
    private BigDecimal parsingDataToBigDecimal(Object cellValue) {
        if (Objects.nonNull(cellValue)) {
            String value = cellValue.toString();
            if (value.contains("%")) {
                value = value.replace("%", "");
                BigDecimal decimal = new BigDecimal(value);
                return MathUtils.checkIsZero(decimal) ? BigDecimal.ZERO : decimal.divide(MathUtils.ONE_HUNDRED, 4, BigDecimal.ROUND_HALF_UP);
            }
            return new BigDecimal(value);
        }
        return null;
    }

    @Override
    public Map<Integer, AopReleaseVersionRecordResDTO> getReleaseVersionRecord(AopReleaseVersionRecordReqDTO reqDTO) {
        Map<Integer, AopReleaseVersionRecordResDTO> resultMap = Maps.newHashMap();
        QAopReleaseVersionRecord record = QAopReleaseVersionRecord.aopReleaseVersionRecord;
        reqDTO.getYears().forEach(year -> {
            JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
            JPAQuery<AopReleaseVersionRecord> jpaQuery = jpaQueryFactory.selectFrom(record);
            jpaQuery.where(record.year.eq(year));
            if (reqDTO.getReleaseVersionType().contains(ReleaseVersionTypeEnum.MAPS.name())) {
                jpaQuery.where(record.mapsFlag.eq(YesOrNoEnums.Y.getCode()));
            }
            if (reqDTO.getReleaseVersionType().contains(ReleaseVersionTypeEnum.DP.name())) {
                jpaQuery.where(record.dpFlag.eq(YesOrNoEnums.Y.getCode()));
            }
            jpaQuery.orderBy(record.releaseDate.desc());
            jpaQuery.limit(1);
            AopReleaseVersionRecord releaseVersionRecord = jpaQuery.fetchFirst();
            resultMap.put(year, aopReleaseVersionRecordConvert.toDto(releaseVersionRecord));
        });
        return resultMap;
    }
}
