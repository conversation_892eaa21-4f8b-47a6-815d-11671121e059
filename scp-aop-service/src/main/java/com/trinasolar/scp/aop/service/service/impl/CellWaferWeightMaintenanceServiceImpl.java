package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.converters.longconverter.LongStringConverter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.aop.domain.constant.QPConstant;
import com.trinasolar.scp.aop.domain.convert.CellWaferWeightMaintenanceDEConvert;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.IndexTypeEnum;
import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.aop.domain.excel.CellWaferWeightMaintenanceImportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.CellWeightMaintenanceExportExcelDTO;
import com.trinasolar.scp.aop.domain.excel.WafterWeightMaintenanceExportExcelDTO;
import com.trinasolar.scp.aop.domain.query.CalculateQuery;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.save.CellWaferWeightMaintenanceSaveDTO;
import com.trinasolar.scp.aop.domain.utils.MathUtils;
import com.trinasolar.scp.aop.service.repository.*;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.common.api.base.GlobalConstant;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.IterableUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.trinasolar.scp.common.api.util.LovUtils.instance;

/**
 * 电池硅片权重
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
@Slf4j
@Service("cellWaferWeightMaintenanceService")
public class CellWaferWeightMaintenanceServiceImpl implements CellWaferWeightMaintenanceService {
    @Autowired
    private CellWaferWeightMaintenanceRepository repository;

    @PersistenceContext
    private EntityManager entityManager;

    private static final String DEFAULT_REGEX = "-";
    private static final String DEFAULT_REGEX1 = "#";

    @Autowired
    ProductInfoService productInfoService;

    @Autowired
    @Lazy
    HorizontalOrVerticalService  horizontalOrVerticalService;

    @Autowired
    @Lazy
    AdjustService adjustService;

    @Autowired
    ConfigModuleGoodSnapshotRepository configModuleGoodSnapshotRepository;

    @Autowired
    AveragePowerSnapshotRepository averagerPowerSnapshotRepository;

    @Autowired
    ConfigCellGoodSnapshotRepository configCellGoodSnapshotRepository;

    @Autowired
    ProductFamilyCalculationPowerMaintenanceSnapshotRepository productFamilyCalculationPowerMaintenanceSnapshotRepository;

    @Override
    public Page<CellWaferWeightMaintenanceDTO> queryByPage(CellWaferWeightMaintenanceQuery query) {
        ProductTypeEnum productType = query.getProductType();
        if (ObjectUtils.isEmpty(productType)) {
            return new PageImpl<>(new ArrayList<>());
        }
        QCellWaferWeightMaintenance qCellWaferWeightMaintenance = QCellWaferWeightMaintenance.cellWaferWeightMaintenance;
        BooleanBuilder booleanBuilder = this.buildBooleanBuilder(query, qCellWaferWeightMaintenance);

        Sort sort = Sort.by(Sort.Direction.ASC, "year");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);

        Page<CellWaferWeightMaintenance> cellWaferWeightMaintenancePage = repository.findAll(booleanBuilder, pageable);
        List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS = CellWaferWeightMaintenanceDEConvert.INSTANCE.toDto(cellWaferWeightMaintenancePage.getContent());

        //值集转换
        Set<LovLineQuery> lovLineQuerys = this.collectLovValueLine(cellWaferWeightMaintenanceDTOS, productType);
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);

        this.queryLovNameTrans(cellWaferWeightMaintenanceDTOS, lovMap, productType);
        if (ProductTypeEnum.CELL.equals(query.getProductType())) {
            cellWaferWeightMaintenanceDTOS = cellWaferWeightMaintenanceDTOS.stream().sorted(Comparator.comparing(CellWaferWeightMaintenanceDTO::getYear).thenComparing(CellWaferWeightMaintenanceDTO::getCountryFlag).thenComparing(CellWaferWeightMaintenanceDTO::getProductType).thenComparing(CellWaferWeightMaintenanceDTO::getCellShard)).collect(Collectors.toList());
        } else {
            cellWaferWeightMaintenanceDTOS = cellWaferWeightMaintenanceDTOS.stream().sorted(Comparator.comparing(CellWaferWeightMaintenanceDTO::getYear).thenComparing(CellWaferWeightMaintenanceDTO::getCountryFlag).thenComparing(CellWaferWeightMaintenanceDTO::getProductType)).collect(Collectors.toList());
        }
        return new PageImpl<>(cellWaferWeightMaintenanceDTOS, pageable, cellWaferWeightMaintenancePage.getTotalElements());
    }

    private void queryLovNameTrans(List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS, Map<String, LovLineDTO> lovMap, ProductTypeEnum productType) {
        cellWaferWeightMaintenanceDTOS.forEach(item -> {
            //国内/海外
            LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            if (Objects.nonNull(lovLineDTO02) && ObjectUtils.isNotEmpty(item.getCountryFlag())) {
                item.setCountryFlagName(lovLineDTO02.getLovName());
            } else {
                throw new BizException(item.getCountryFlag() + "值集列表不存在");
            }
            //电池型号
            LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SERIES, item.getCellModel()));
            if (Objects.nonNull(lovLineDTO03) && ObjectUtils.isNotEmpty(item.getCellModel())) {
                item.setCellModelName(lovLineDTO03.getLovName());
            } else {
                throw new BizException(item.getCellModel() + "值集列表不存在");
            }
            if (Objects.nonNull(productType) && ObjectUtils.isNotEmpty(item.getCellShard()) && ProductTypeEnum.CELL.equals(productType)) {
                //分片方式
                LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, item.getCellShard()));
                if (Objects.nonNull(lovLineDTO01)) {
                    item.setCellShardName(lovLineDTO01.getLovName());
                } else {
                    throw new BizException(item.getCellShard() + "值集列表不存在");
                }
            }
        });
    }

    private Set<LovLineQuery> collectLovValueLine(List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS, ProductTypeEnum productType) {
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        cellWaferWeightMaintenanceDTOS.forEach(item -> {
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            //电池型号
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SERIES, item.getCellModel()));
            //电池 分片方式
            if (ProductTypeEnum.CELL.equals(productType)) {
                lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SHARD, item.getCellShard()));
            }
        });
        return lovLineQuerys;
    }

    private BooleanBuilder buildBooleanBuilder(CellWaferWeightMaintenanceQuery query, QCellWaferWeightMaintenance qCellWaferWeightMaintenance) {
        ProductTypeEnum productType = query.getProductType();
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (ObjectUtils.isNotEmpty(query.getYear())) {
            booleanBuilder.and(qCellWaferWeightMaintenance.year.eq(query.getYear()));
        }
        if (ObjectUtils.isNotEmpty(query.getYears())) {
            booleanBuilder.and(qCellWaferWeightMaintenance.year.in(query.getYears()));
        }
        if (StringUtils.isNotBlank(query.getCellModel())) {
            booleanBuilder.and(qCellWaferWeightMaintenance.cellModel.eq(query.getCellModel()));
        }
        if (StringUtils.isNotBlank(query.getCountryFlag())) {
            booleanBuilder.and(qCellWaferWeightMaintenance.countryFlag.eq(query.getCountryFlag()));
        }
        //电池 且查询条件分片方式
        if (StringUtils.isNotBlank(query.getCellShard()) && ProductTypeEnum.CELL.equals(productType)) {
            booleanBuilder.and(qCellWaferWeightMaintenance.cellShard.eq(query.getCellShard()));
        }
        booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(query.getProductType().getCode()));
        return booleanBuilder;
    }

    @Override
    public CellWaferWeightMaintenanceDTO queryById(Long id) {
        CellWaferWeightMaintenance queryObj = repository.findById(id).orElse(null);
        if (Objects.isNull(queryObj)) {
            return null;
        }
        CellWaferWeightMaintenanceDTO result = new CellWaferWeightMaintenanceDTO();
        BeanUtils.copyProperties(queryObj, result);
        this.queryLovDataTrans(result, queryObj);
        return result;
    }

    private void queryLovDataTrans(CellWaferWeightMaintenanceDTO result, CellWaferWeightMaintenance queryObj) {
        if (StringUtils.isBlank(queryObj.getProductType())) {
            throw new BizException("产品类型不能为空，请检查数据。");
        }
        //值集转换
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        //国内/海外
        lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, result.getCountryFlag()));
        //电池型号
        lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SERIES, result.getCellModel()));
        //电池 分片方式
        if (ProductTypeEnum.CELL.getCode().equals(queryObj.getProductType())) {
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SHARD, result.getCellShard()));
        }
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        //国内/海外
        LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, result.getCountryFlag()));
        if (Objects.nonNull(lovLineDTO02) && StringUtils.isNotBlank(result.getCountryFlag())) {
            result.setCountryFlagName(lovLineDTO02.getLovName());
        } else {
            throw new BizException(result.getCountryFlag() + "值集列表不存在");
        }
        //电池型号
        LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SERIES, result.getCellModel()));
        if (Objects.nonNull(lovLineDTO03) && StringUtils.isNotBlank(result.getCellModel())) {
            result.setCellModelName(lovLineDTO03.getLovName());
        } else {
            throw new BizException(result.getCellModel() + "值集列表不存在");
        }
        //分片方式
        LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, result.getCellShard()));
        if (StringUtils.isNotBlank(result.getCellShard())) {
            if (Objects.nonNull(lovLineDTO01)) {
                result.setCellShardName(lovLineDTO01.getLovName());
            } else {
                throw new BizException(result.getCellShard() + "值集列表不存在");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public CellWaferWeightMaintenanceDTO save(CellWaferWeightMaintenanceSaveDTO saveDTO) {
        String productType = saveDTO.getProductType();
        ProductTypeEnum productTypeEnum = ProductTypeEnum.valueOf(productType);
        if (Objects.isNull(productTypeEnum)) {
            throw new BizException("产品类型不能为空，请检查数据。");
        }

        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        //国内/海外
        lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, saveDTO.getCountryFlag()));
        //电池型号
        lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SERIES, saveDTO.getCellModel()));
        //电池 分片方式
        if (ProductTypeEnum.CELL.equals(productTypeEnum)) {
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SHARD, saveDTO.getCellShard()));
        }
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);

        //国内/海外
        LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, saveDTO.getCountryFlag()));
        if (Objects.isNull(lovLineDTO02) && StringUtils.isNotBlank(saveDTO.getCountryFlag())) {
            throw new BizException(saveDTO.getCountryFlag() + "值集列表不存在");
        }
        //电池型号
        LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SERIES, saveDTO.getCellModel()));
        if (Objects.isNull(lovLineDTO03) && StringUtils.isNotBlank(saveDTO.getCellModel())) {
            throw new BizException(saveDTO.getCellModel() + "值集列表不存在");
        }
        //电池 分片方式
        if (ProductTypeEnum.CELL.equals(productTypeEnum)) {
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, saveDTO.getCellShard()));
            if (StringUtils.isNotBlank(saveDTO.getCellShard()) && Objects.isNull(lovLineDTO01)) {
                throw new BizException(saveDTO.getCellShard() + "值集列表不存在");
            }
        }

        CellWaferWeightMaintenance newObj = Optional.ofNullable(saveDTO.getId()).map(id -> repository.getOne(saveDTO.getId())).orElse(new CellWaferWeightMaintenance());
        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.forEach(id -> repository.deleteById(id));
    }

    @SneakyThrows
    @Override
    public void export(CellWaferWeightMaintenanceQuery query, HttpServletResponse response) {
        List<CellWaferWeightMaintenanceDTO> dtos = queryByPage(query).getContent();

        //MW折算系数-电池 MW折算系数-硅片
        String waferSheelName = "MW折算系数-硅片";
        String cellSheelName = "MW折算系数-电池";
        String waferFileName = waferSheelName + "_" + com.ibm.dpf.base.core.util.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        String cellFileName = cellSheelName + "_" + com.ibm.dpf.base.core.util.DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        boolean isCell = ProductTypeEnum.CELL.equals(query.getProductType());

        ExcelUtils.setExportResponseHeader(response, isCell ? cellFileName : waferFileName);
        // 这里 指定文件
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).registerConverter(new LocalDateConverter()).registerConverter(new LocalDateTimeConverter()).registerConverter(new LongStringConverter()).build()) {
            WriteSheet sheet = EasyExcel.writerSheet(0, isCell ? cellSheelName : waferSheelName).head(isCell ? CellWeightMaintenanceExportExcelDTO.class : WafterWeightMaintenanceExportExcelDTO.class).build();
            //写入数据
            excelWriter.write(isCell ? CellWaferWeightMaintenanceDEConvert.INSTANCE.toCellExportExcelDTO(dtos) : CellWaferWeightMaintenanceDEConvert.INSTANCE.toWafterExportExcelDTO(dtos), sheet);
        }
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String importData(MultipartFile multipartFile, ExcelPara excelPara, ProductTypeEnum productType) {
        String batchNo = UUID.randomUUID().toString();

        //校验
        CellWaferWeightMaintenanceCheckDTO checkDTO = this.checkImport(multipartFile, excelPara, productType);
        if (checkDTO.getIsRepeat().equals(false) && StringUtils.isNotBlank(checkDTO.getErrMsg())) {
            throw new BizException(checkDTO.getErrMsg());
        }

        List<CellWaferWeightMaintenanceDTO> excelData = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellWaferWeightMaintenanceDTO.class, excelPara);
        log.info(String.format("{}条数据，开始存储数据库！", excelData.size()));

        //相同年份数据覆盖
        this.coverSameData(productType, excelData);

        Set<LovLineQuery> lovLineQuerys = this.collectLovNameLine(productType, excelData);
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);

        //数值转换
        this.lovDataTrans(productType, excelData, lovMap);

        repository.saveAll(CellWaferWeightMaintenanceDEConvert.INSTANCE.toEntity(excelData));
        log.info("存储数据库成功！");

        return batchNo;
    }

    private void lovDataTrans(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS, Map<String, LovLineDTO> lovMap) {
        cellWaferWeightMaintenanceDTOS.stream().forEach(dto -> {
            dto.setProductType(productType.getCode());

            //国内/海外
            LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, dto.getCountryFlagName()));
            if (Objects.nonNull(lovLineDTO02)) {
                dto.setCountryFlag(lovLineDTO02.getLovValue());
            }
            //电池型号
            LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SERIES, dto.getCellModelName()));
            if (Objects.nonNull(lovLineDTO03)) {
                dto.setCellModel(lovLineDTO03.getLovValue());
            }
            //电池 分片方式
            if (ProductTypeEnum.CELL.equals(productType)) {
                LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, dto.getCellShardName()));
                if (Objects.nonNull(lovLineDTO01)) {
                    dto.setCellShard(lovLineDTO01.getLovValue());
                }
            }
        });
    }

    private void coverSameData(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> excelData) {
        //收集年份
        List<Integer> years = excelData.stream().map(CellWaferWeightMaintenanceDTO::getYear).distinct().collect(Collectors.toList());
        //清除相同年份数据
        QCellWaferWeightMaintenance qCellWaferWeightMaintenance = QCellWaferWeightMaintenance.cellWaferWeightMaintenance;
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(qCellWaferWeightMaintenance.year.in(years));
        //电池类型
        if (ProductTypeEnum.CELL.equals(productType)) {
            booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(ProductTypeEnum.CELL.getCode()));
        } else {//硅片类型
            booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(ProductTypeEnum.WAFER.getCode()));
        }
        booleanBuilder.and(qCellWaferWeightMaintenance.isDeleted.eq(DeleteEnum.NO.getCode()));
        List<CellWaferWeightMaintenance> allYears = IterableUtils.toList(repository.findAll(booleanBuilder));
        if (CollectionUtils.isNotEmpty(excelData) && CollectionUtils.isNotEmpty(allYears)) {
            //逻辑删除
            List<Long> ids = allYears.stream().map(CellWaferWeightMaintenance::getId).collect(Collectors.toList());
            this.logicDeleteByIds(ids);
            //repository.deleteInBatch(allYears);
        }
    }

    private Set<LovLineQuery> collectLovNameLine(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> excelData) {
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        excelData.stream().forEach(dto -> {
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, dto.getCountryFlagName()));
            //电池型号
            lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_CELL_SERIES, dto.getCellModelName()));
            //电池 分片方式
            if (ProductTypeEnum.CELL.equals(productType)) {
                lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_CELL_SHARD, dto.getCellShardName()));
            }
        });
        return lovLineQuerys;
    }

    @SneakyThrows
    @Override
    public CellWaferWeightMaintenanceCheckDTO checkImport(MultipartFile multipartFile, ExcelPara excelPara, ProductTypeEnum productType) {
        //构建返回消息
        CellWaferWeightMaintenanceCheckDTO checkDTO = new CellWaferWeightMaintenanceCheckDTO();
        checkDTO.setIsRepeat(false);

        //产品大类（电池/硅片）校验
        if (Objects.isNull(productType)) {
            throw new BizException("产品类型不能为空，请检查数据。");
        }

        //读取数据
        List<CellWaferWeightMaintenanceImportExcelDTO> importExcelDTOS = ExcelUtils.readExcel(multipartFile.getInputStream(), null, CellWaferWeightMaintenanceImportExcelDTO.class, excelPara);

        //读取数据为空
        if (CollectionUtils.isEmpty(importExcelDTOS)) {
            return checkDTO;
        }

        CalculateQuery query = new CalculateQuery();
        query.setPageNumber(1);
        query.setPageSize(GlobalConstant.max_page_size);
        Page<CalculateDTO> calculateDTOS = adjustService.versionRecord(query);
        List<CalculateDTO> sopVersions = calculateDTOS.getContent();
        //合法异常信息收集
        Map<Integer, StringBuffer> checkErrMsgMap = Maps.newTreeMap();
        //必填项校验失败：第X行字段XXX列未填写
        boolean isCheckError = this.isIsCheckError(importExcelDTOS, checkErrMsgMap);
        //如果不合法则报错
        if (isCheckError) {
            checkDTO.setErrMsg(checkErrMsgMap.values().toString());
            return checkDTO;
        }

        List<CellWaferWeightMaintenanceDTO> excelData = this.getCellWaferWeightMaintenanceDTOS(importExcelDTOS);

        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();

        //必填异常信息收集
        Map<Integer, StringBuffer> requiredErrMsgMap = Maps.newTreeMap();
        //必填项校验
        boolean isRequiredErr = this.checkIsRequired(productType, excelData, requiredErrMsgMap, lovLineQuerys,sopVersions);

        //值列表校验异常信息收集
        Map<Integer, StringBuffer> formatErrMsgMap = Maps.newTreeMap();
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        //值列表校验
        boolean isFormatErr = this.checkIsFormatErr(productType, excelData, formatErrMsgMap, lovMap);

        //异常信息处理
        if (isRequiredErr && isFormatErr) {
            checkDTO.setErrMsg(String.format("%s{%s}", requiredErrMsgMap.values().toString(), formatErrMsgMap.values().toString()));
            return checkDTO;
        } else if (isRequiredErr) {
            checkDTO.setErrMsg(requiredErrMsgMap.values().toString());
            return checkDTO;
        } else if (isFormatErr) {
            checkDTO.setErrMsg(formatErrMsgMap.values().toString());
            return checkDTO;
        }

        // 当前年份数据已存在，请确认是否覆盖
        boolean isRepeatErr = false;
        Map<Integer, StringBuffer> repeatErrMsgMap = Maps.newTreeMap();
        isRepeatErr = this.checkIsRepeat(productType, excelData, repeatErrMsgMap, isRepeatErr);
        //重复校验
        if (isRepeatErr) {
            checkDTO.setIsRepeat(true);
            checkDTO.setErrMsg(repeatErrMsgMap.values().toString());
            return checkDTO;
        }

        return checkDTO;
    }

    private boolean isIsCheckError(List<CellWaferWeightMaintenanceImportExcelDTO> importExcelDTOS, Map<Integer, StringBuffer> requiredErrMsgMap) {
        boolean isCheckError = false;
        //  Integer数值类型（年份），BigDecimal数值类型校验（）
        for (int i = 0; i < importExcelDTOS.size(); i++) {
            boolean isCheckLineError = false;
            //默认excel列表展示对应行数（第一行为表头）
            int lineNo = i + 2;
            StringBuffer errMsg = requiredErrMsgMap.getOrDefault(lineNo, new StringBuffer("第[" + lineNo + "]行，"));
            CellWaferWeightMaintenanceImportExcelDTO importExcelDTO = importExcelDTOS.get(i);
            //数据类型校验
            String year = importExcelDTO.getYear();
            String m1Quantity = importExcelDTO.getM1Quantity();
            String m2Quantity = importExcelDTO.getM2Quantity();
            String m3Quantity = importExcelDTO.getM3Quantity();
            String m4Quantity = importExcelDTO.getM4Quantity();
            String m5Quantity = importExcelDTO.getM5Quantity();
            String m6Quantity = importExcelDTO.getM6Quantity();
            String m7Quantity = importExcelDTO.getM7Quantity();
            String m8Quantity = importExcelDTO.getM8Quantity();
            String m9Quantity = importExcelDTO.getM9Quantity();
            String m10Quantity = importExcelDTO.getM10Quantity();
            String m11Quantity = importExcelDTO.getM11Quantity();
            String m12Quantity = importExcelDTO.getM12Quantity();
            String dataVersion = importExcelDTO.getDataVersion();
            if(StringUtils.isBlank(dataVersion)){
                errMsg.append(String.format("字段%s列未填写。","S&OP版本号"));
                isCheckLineError = true;
                isCheckError = true;
            }
            if (StringUtils.isNotBlank(year)) {
                try {
                    Integer.valueOf(year);
                } catch (Exception e) {
                    errMsg.append(String.format("%s列，数据不合法。", "年份"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m1Quantity)) {
                try {
                    new BigDecimal(m1Quantity);
                } catch (Exception e) {
                    // 1月权重 : Jan
                    errMsg.append(String.format("%s列，数据不合法。", "Jan"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m2Quantity)) {
                try {
                    new BigDecimal(m2Quantity);
                } catch (Exception e) {
                    //2月权重: Feb
                    errMsg.append(String.format("%s列，数据不合法。", "Feb"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m3Quantity)) {
                try {
                    new BigDecimal(m3Quantity);
                } catch (Exception e) {
                    //3月权重 : Mar
                    errMsg.append(String.format("%s列，数据不合法。", "Mar"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m4Quantity)) {
                try {
                    new BigDecimal(m4Quantity);
                } catch (Exception e) {
                    //4月权重 : Apr
                    errMsg.append(String.format("%s列，数据不合法。", "Apr"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m5Quantity)) {
                try {
                    new BigDecimal(m5Quantity);
                } catch (Exception e) {
                    //5月权重 : May
                    errMsg.append(String.format("%s列，数据不合法。", "May"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m6Quantity)) {
                try {
                    new BigDecimal(m6Quantity);
                } catch (Exception e) {
                    //6月权重 : Jun
                    errMsg.append(String.format("%s列，数据不合法。", "Jun"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m7Quantity)) {
                try {
                    new BigDecimal(m7Quantity);
                } catch (Exception e) {
                    //7月权重 : Jul
                    errMsg.append(String.format("%s列，数据不合法。", "Jul"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m8Quantity)) {
                try {
                    new BigDecimal(m8Quantity);
                } catch (Exception e) {
                    //8月权重 : Aug
                    errMsg.append(String.format("%s列，数据不合法。", "Aug"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m9Quantity)) {
                try {
                    new BigDecimal(m9Quantity);
                } catch (Exception e) {
                    //9月权重 : Sep
                    errMsg.append(String.format("%s列，数据不合法。", "Sep"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m10Quantity)) {
                try {
                    new BigDecimal(m10Quantity);
                } catch (Exception e) {
                    //10月权重 : Oct
                    errMsg.append(String.format("%s列，数据不合法。", "Oct"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m11Quantity)) {
                try {
                    new BigDecimal(m11Quantity);
                } catch (Exception e) {
                    //11月权重 : Nov
                    errMsg.append(String.format("%s列，数据不合法。", "Nov"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            if (StringUtils.isNotBlank(m12Quantity)) {
                try {
                    new BigDecimal(m12Quantity);
                } catch (Exception e) {
                    //12月权重 : Dec
                    errMsg.append(String.format("%s列，数据不合法。", "Dec"));
                    isCheckLineError = true;
                    isCheckError = true;
                }
            }
            //如果每行存在错误则收集错误信息
            if (isCheckLineError) {
                requiredErrMsgMap.put(lineNo, errMsg);
            }
        }
        return isCheckError;
    }

    private List<CellWaferWeightMaintenanceDTO> getCellWaferWeightMaintenanceDTOS(List<CellWaferWeightMaintenanceImportExcelDTO> importExcelDTOS) {
        List<CellWaferWeightMaintenanceDTO> excelData = new ArrayList<>();
        //数据转换
        for (CellWaferWeightMaintenanceImportExcelDTO importExcelDTO : importExcelDTOS) {
            //年份以及相关月权重
            String year = importExcelDTO.getYear();
            String m1Quantity = importExcelDTO.getM1Quantity();
            String m2Quantity = importExcelDTO.getM2Quantity();
            String m3Quantity = importExcelDTO.getM3Quantity();
            String m4Quantity = importExcelDTO.getM4Quantity();
            String m5Quantity = importExcelDTO.getM5Quantity();
            String m6Quantity = importExcelDTO.getM6Quantity();
            String m7Quantity = importExcelDTO.getM7Quantity();
            String m8Quantity = importExcelDTO.getM8Quantity();
            String m9Quantity = importExcelDTO.getM9Quantity();
            String m10Quantity = importExcelDTO.getM10Quantity();
            String m11Quantity = importExcelDTO.getM11Quantity();
            String m12Quantity = importExcelDTO.getM12Quantity();

            //读取excel转换数据
            CellWaferWeightMaintenanceDTO dto = CellWaferWeightMaintenanceDEConvert.INSTANCE.importExcelToDto(importExcelDTO);

            //合法数据转换
            dto.setYear(StringUtils.isNotBlank(year) ? Integer.valueOf(year) : null);
            dto.setM1Quantity(StringUtils.isNotBlank(m1Quantity) ? new BigDecimal(m1Quantity) : null);
            dto.setM2Quantity(StringUtils.isNotBlank(m2Quantity) ? new BigDecimal(m2Quantity) : null);
            dto.setM3Quantity(StringUtils.isNotBlank(m3Quantity) ? new BigDecimal(m3Quantity) : null);
            dto.setM4Quantity(StringUtils.isNotBlank(m4Quantity) ? new BigDecimal(m4Quantity) : null);
            dto.setM5Quantity(StringUtils.isNotBlank(m5Quantity) ? new BigDecimal(m5Quantity) : null);
            dto.setM6Quantity(StringUtils.isNotBlank(m6Quantity) ? new BigDecimal(m6Quantity) : null);
            dto.setM7Quantity(StringUtils.isNotBlank(m7Quantity) ? new BigDecimal(m7Quantity) : null);
            dto.setM8Quantity(StringUtils.isNotBlank(m8Quantity) ? new BigDecimal(m8Quantity) : null);
            dto.setM9Quantity(StringUtils.isNotBlank(m9Quantity) ? new BigDecimal(m9Quantity) : null);
            dto.setM10Quantity(StringUtils.isNotBlank(m10Quantity) ? new BigDecimal(m10Quantity) : null);
            dto.setM11Quantity(StringUtils.isNotBlank(m11Quantity) ? new BigDecimal(m11Quantity) : null);
            dto.setM12Quantity(StringUtils.isNotBlank(m12Quantity) ? new BigDecimal(m12Quantity) : null);
            excelData.add(dto);
        }
        return excelData;
    }

    private boolean checkIsRepeat(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> excelData, Map<Integer, StringBuffer> repeatErrMsgMap, boolean isRepeatErr) {
        QCellWaferWeightMaintenance qCellWaferWeightMaintenance = QCellWaferWeightMaintenance.cellWaferWeightMaintenance;
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        //年份集合
        List<Integer> years = excelData.stream().map(CellWaferWeightMaintenanceDTO::getYear).distinct().collect(Collectors.toList());
        if (ProductTypeEnum.CELL.equals(productType)) {
            booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(ProductTypeEnum.CELL.getCode()));
        } else {
            booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(ProductTypeEnum.WAFER.getCode()));
        }
        booleanBuilder.and(qCellWaferWeightMaintenance.year.in(years));
        booleanBuilder.and(qCellWaferWeightMaintenance.isDeleted.eq(DeleteEnum.NO.getCode()));
        List<CellWaferWeightMaintenance> cellWaferWeightMaintenances = IterableUtils.toList(repository.findAll(booleanBuilder));
        if (CollectionUtils.isNotEmpty(cellWaferWeightMaintenances)) {
            //收集错误信息
            repeatErrMsgMap.put(1, new StringBuffer("当前年份数据已存在，请确认是否覆盖。"));
            isRepeatErr = true;
        }
        return isRepeatErr;
    }

    private boolean checkIsFormatErr(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> excelData, Map<Integer, StringBuffer> formatErrMsgMap, Map<String, LovLineDTO> lovMap) {
        //值列表校验失败：第X行字段XXX列填写值错误
        boolean isFormatErr = false;
        //分组 重复数据校验 (匹配字段：国内/海外 + 电池型号 + 分片方式 + 年份) 提示:第***行。存在重复数据。
        Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOMap = new HashMap<>();
        for (int i = 0; i < excelData.size(); i++) {
            //是否存在行信息错误
            boolean isLineError = false;
            CellWaferWeightMaintenanceDTO dto = excelData.get(i);
            int lineNo = i + 2;
            StringBuffer errMsg = formatErrMsgMap.getOrDefault(lineNo, new StringBuffer("第[" + lineNo + "]行，"));
            //国内/海外
            String countryFlag = dto.getCountryFlagName();
            //电池型号
            String cellModel = dto.getCellModelName();
            //电池分片方式
            String cellShard = dto.getCellShardName();
            //年份
            Integer year = dto.getYear();
            if (StringUtils.isNotBlank(countryFlag)) {
                LovLineDTO lovLineDTO = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, countryFlag));
                if (Objects.isNull(lovLineDTO)) {
                    errMsg.append(String.format("字段%s列填写值错误。", "国内/海外"));
                    isFormatErr = true;
                    isLineError = true;
                }
            }
            if (StringUtils.isNotBlank(cellModel)) {
                LovLineDTO lovLineDTO = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SERIES, cellModel));
                if (Objects.isNull(lovLineDTO)) {
                    errMsg.append(String.format("字段%s列填写值错误。", "电池型号"));
                    isFormatErr = true;
                    isLineError = true;
                }
            }
            if (ProductTypeEnum.CELL.equals(productType)) {
                if (StringUtils.isNotBlank(cellShard)) {
                    LovLineDTO lovLineDTO = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, cellShard));
                    if (Objects.isNull(lovLineDTO)) {
                        errMsg.append(String.format("字段%s列填写值错误。", "分片方式"));
                        isFormatErr = true;
                        isLineError = true;
                    }
                }
            }

            //匹配字段：国内/海外 + 电池型号 + 分片方式 + 年份
            String key = String.format("%s%s%s{%s}", countryFlag, cellModel, cellShard, year);
            CellWaferWeightMaintenanceDTO duplicateDataDTO = cellWaferWeightMaintenanceDTOMap.get(key);
            //存在重复数据
            if (Objects.nonNull(duplicateDataDTO)) {
                //文件中第***行存在存在重复数据。
                errMsg.append("存在重复数据。");
                isFormatErr = true;
                isLineError = true;
            } else {
                //不存在则添加进去
                cellWaferWeightMaintenanceDTOMap.put(key, dto);
            }

            //如果行信息存在错误则收集
            if (isLineError) {
                //收集错误信息
                formatErrMsgMap.put(lineNo, errMsg);
            }
        }
        return isFormatErr;
    }

    private boolean checkIsRequired(ProductTypeEnum productType, List<CellWaferWeightMaintenanceDTO> excelData, Map<Integer, StringBuffer> requiredErrMsgMap, Set<LovLineQuery> lovLineQuerys, List<CalculateDTO> sopVersions) {

        //必填项校验失败：第X行字段XXX列未填写
        boolean isRequiredErr = false;
        for (int i = 0; i < excelData.size(); i++) {
            //是否存在行信息错误
            boolean isLineError = false;
            CellWaferWeightMaintenanceDTO dto = excelData.get(i);
            int lineNo = i + 2;
            StringBuffer errMsg = requiredErrMsgMap.getOrDefault(lineNo, new StringBuffer("第[" + lineNo + "]行，"));
            //国内/海外
            String countryFlag = dto.getCountryFlagName();
            //电池型号
            String cellModel = dto.getCellModelName();
            //电池分片方式
            String cellShard = dto.getCellShardName();
            //年份
            Integer year = dto.getYear();
            String dataVersion = dto.getDataVersion();
            Optional<CalculateDTO> calculateDTOOptional = sopVersions.stream().filter(sopVersion -> sopVersion.getDataVersion().equals(dataVersion)).findFirst();
            if (!calculateDTOOptional.isPresent()) {
                errMsg.append(String.format("字段%s列填写值错误。", "S&OP版本号"));
                isRequiredErr = true;
                isLineError = true;;
            }

            if (StringUtils.isBlank(countryFlag)) {
                errMsg.append(String.format("字段%s列未填写。", "国内/海外"));
                isRequiredErr = true;
                isLineError = true;
            } else {
                //国内/海外
                lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, countryFlag));
            }
            if (StringUtils.isBlank(cellModel)) {
                errMsg.append(String.format("字段%s列未填写。", "电池型号"));
                isRequiredErr = true;
                isLineError = true;
            } else {
                //电池型号
                lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_CELL_SERIES, cellModel));
            }
            //电池分片方式
            if (ProductTypeEnum.CELL.equals(productType)) {
                if (StringUtils.isBlank(cellShard)) {
                    errMsg.append(String.format("字段%s列未填写。", "分片方式"));
                    isRequiredErr = true;
                    isLineError = true;
                } else {
                    lovLineQuerys.add(new LovLineQuery().buildWithName(LovHeaderCodeConstant.AOP_CELL_SHARD, cellShard));
                }
            }
            if (Objects.isNull(year)) {
                errMsg.append(String.format("字段%s列未填写。", "年份"));
                isRequiredErr = true;
                isLineError = true;
            }
            //如果每行存在错误则收集错误信息
            if (isLineError) {
                requiredErrMsgMap.put(lineNo, errMsg);
            }
        }
        return isRequiredErr;
    }

    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public Map<String, String> calculate(CellWaferWeightMaintenanceCalculateDTO calculateDTO) {
        Map<String, String> msgMap = new HashMap<>();
        //产品类型和版本号
        ProductTypeEnum productType = calculateDTO.getProductType();
        String dataVersion = calculateDTO.getDataVersion();
        if (Objects.isNull(productType) || StringUtils.isBlank(dataVersion)) {
            throw new BizException("产品类型或者数据版本号不能为空，请检查数据。");
        }
        int checkType = StringUtils.isNotBlank(calculateDTO.getCheckType()) ? Integer.parseInt(calculateDTO.getCheckType()) : 1;
        String checkFlag = StringUtils.isNotBlank(calculateDTO.getCheckFlag()) ? calculateDTO.getCheckFlag() : "Yes";

        //计算逻辑：
        //1：先获取组件排产量中所有的产品系列 （取表aop_import_actual_capacity product_type = MODULE或CELL；product_from =  SELF-PRODUCTION 获取产品系列、车间、年份、月份、排产量）
        //2：按国内海外、产品系列去获取产品族分解系数、横竖装比例系数 （按版本、国内海外、产品族、季度维度获取良率数据）（按国内海外、产品系列、年份、季度获取数据，1-竖装比例=非标比例）
        //3：然后根据产品族分解系数中产品族获取长期预测功率 （按国内海外、产品族、横竖装、年份、月份维度获取数据）
        //4：根据LOV获取产品族对应电池片数 （LOV CODE：aps_power_cell_prod_map 按产品族获取电池片数）
        //5：根据产品系列到产品信息对照表中获取产品系列对应的电池型号、分片方式 （获取产品族与电池型号、产品系列的对应关系）
        //6：根据组件排产量的车间+产品族分解系数分解的产品族+年份月份获取组件良率 （按国内海外、产品族、车间、年份、月份维度获取良率数据，仅获取指标等于工原损的数据。组件良率=1-工原损）
        //电池良率：按国内海外、品类、晶体类型、主栅、车间、年份、月份维度获取良率数据。品类+晶体类型= 电池型号  （LOV：APS_POWER_CELL_SERIES_TYPE 将电池型号+主栅转换为分片方式）

        //全局 年份 收集
        List<String> allYears = new ArrayList<>();
        //全局 产品系列 收集
        List<String> allProductSeries = new ArrayList<>();
        //全局 国内海外收集
        List<String> countryFlagList = new ArrayList<>();
        //全局 电池型号
        List<String> allCellModel = new ArrayList<>();
        //全局 分片方式
        List<String> allCellShard = new ArrayList<>();

        //组件排产量
        List<ImportActualCapacityDTO> importActualCapacityDTOS = getImportActualCapacityDto(dataVersion);
        Map<String, List<ImportActualCapacityDTO>> importActualCapacityMap;
        List<String> checkDataByProductSeriesList;
        if (CollectionUtils.isNotEmpty(importActualCapacityDTOS)) {
            importActualCapacityMap = importActualCapacityDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), dto.getWorkshop(), String.valueOf(dto.getYear()))));
            checkDataByProductSeriesList = importActualCapacityDTOS.stream().map(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), String.valueOf(dto.getYear()))).distinct().collect(Collectors.toList());
            List<String> importActualCapacityAllYears = importActualCapacityDTOS.stream().map(dto -> String.valueOf(dto.getYear())).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(importActualCapacityAllYears)) {
                allYears.addAll(importActualCapacityAllYears);
            }
            List<String> importActualCapacityAllProductSeries = importActualCapacityDTOS.stream().map(ImportActualCapacityDTO::getProductSeries).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(importActualCapacityAllProductSeries)) {
                allProductSeries.addAll(importActualCapacityAllProductSeries);
            }
            List<String> importActualCapacityAllCountryFlag = importActualCapacityDTOS.stream().map(ImportActualCapacityDTO::getCountryFlag).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(importActualCapacityAllCountryFlag)) {
                countryFlagList.addAll(importActualCapacityAllCountryFlag);
            }
            List<String> productSeriesTemp = importActualCapacityDTOS.stream().map(ImportActualCapacityDTO::getProductSeries).collect(Collectors.toList());
            List<ProductInfo> productInfos = productInfoService.queryListByProductSeries(productSeriesTemp);
            Map<String, ProductInfo> productSeries = productInfos.stream().collect(Collectors.toMap(ProductInfo::getProductSeries, Function.identity(), (v1, v2) -> v1));
            importActualCapacityDTOS.forEach(p -> {
                if (productSeries.get(p.getProductSeries()) != null) {
                    ProductInfo productInfo = productSeries.get(p.getProductSeries());
                    p.setCellModel(productInfo.getCellModel());
                    p.setCellShard(productInfo.getCellShard());
                    p.setWaferModel(productInfo.getWaferModel());
                }
            });
        } else {
            throw new BizException("组件排产量数据未维护!");
        }

        //电池排产量  维度：国内海外 + 电池型号（productSeries） + 电池分片方式（cellShard） + 车间 + 年份
        List<CellDistributionDTO> cellDistributionDTOS = getCellDistributionDto(dataVersion);
        Map<String, List<CellDistributionDTO>> cellDistributionDTOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(cellDistributionDTOS)) {
            cellDistributionDTOMap = cellDistributionDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), dto.getCellShard(), dto.getWorkshop(), String.valueOf(dto.getYear()))));
            List<String> cellDistributionAllYears = cellDistributionDTOS.stream().map(dto -> String.valueOf(dto.getYear())).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cellDistributionAllYears)) {
                allYears.addAll(cellDistributionAllYears);
            }
            List<String> cellDistributionAllCountryFlag = cellDistributionDTOS.stream().map(CellDistributionDTO::getCountryFlag).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cellDistributionAllCountryFlag)) {
                countryFlagList.addAll(cellDistributionAllCountryFlag);
            }
        } else if (ProductTypeEnum.WAFER.equals(calculateDTO.getProductType())) {
            throw new BizException("电池排产量数据未维护!");
        }
        log.info("版本号[{}]下电池分配分组数据共{}条", dataVersion, cellDistributionDTOMap.size());

        Map<String, List<ProductInfoDTO>> productSeriesMap;
        List<ProductInfoDTO> productInfoDTOS = getProductInfoDTO(allProductSeries);
        if (CollectionUtils.isNotEmpty(productInfoDTOS)) {
            List<String> msgList = new ArrayList<>();
            productSeriesMap = productInfoDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getProductSeries())));
            allProductSeries.forEach(p -> {
                if (CollectionUtils.isEmpty(productSeriesMap.get(p))) {
                    msgList.add(p);
                }
            });
            if (CollectionUtils.isNotEmpty(msgList) && (checkType == 1 && "Yes".equals(checkFlag))) {
                msgMap.put("1", String.format("产品系列【%s】对应的产品信息对照表未维护！", String.join(";", msgList)));
                return msgMap;
            }
            List<String> productInfoCellModels = productInfoDTOS.stream().map(ProductInfoDTO::getCellModel).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productInfoCellModels)) {
                allCellModel.addAll(productInfoCellModels);
            }
            List<String> productInfoCellShards = productInfoDTOS.stream().map(ProductInfoDTO::getCellShard).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(productInfoCellShards)) {
                allCellShard.addAll(productInfoCellShards);
            }
        } else {
            throw new BizException("产品系列对应的产品信息对照表未维护！");
        }
        //产品族比例（根据国内海外、产品系列获取比例系数）维度：国内海外+产品族+产品系列+年份 -- 按季度
        List<ProductGroupPercentDTO> productGroupPercentDTOS = new ArrayList<>();
        importActualCapacityDTOS.forEach(p -> {
            List<ProductGroupPercentDTO> productGroupPercentDTO = getProductGroupPercentDTO(dataVersion, Collections.singletonList(p.getCountryFlag()), Collections.singletonList(p.getProductSeries()));
            if (CollectionUtils.isNotEmpty(productGroupPercentDTO)) {
                productGroupPercentDTOS.addAll(productGroupPercentDTO);
            }
        });

        //产品族集合
        List<String> productGroupList = productGroupPercentDTOS.stream().map(ProductGroupPercentDTO::getProductGroup).distinct().collect(Collectors.toList());
        List<String> checkProductGroupList = productGroupPercentDTOS.stream().map(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductGroup(), String.valueOf(dto.getYear()))).distinct().collect(Collectors.toList());
        Map<String, List<ProductGroupPercentDTO>> productGroupPercentDTOMap = productGroupPercentDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductGroup(), dto.getProductSeries(), String.valueOf(dto.getYear()))));
        if (productGroupPercentDTOMap.isEmpty()) {
            throw new BizException(String.format("根据版本号[%s]、国内海外[%s]、产品系列[[%s]]获取产品族比例失败，请检查数据！", dataVersion, JSONUtil.toJsonStr(countryFlagList), JSONUtil.toJsonStr(allProductSeries)));
        }
        List<String> productGroupMsgList = new ArrayList<>();
        Map<String, List<ProductGroupPercentDTO>> productGroupPercentMap = productGroupPercentDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), String.valueOf(dto.getYear()))));
        checkDataByProductSeriesList.forEach(p -> {
            if (CollectionUtils.isEmpty(productGroupPercentMap.get(p))) {
                productGroupMsgList.add(p);
            }
        });
        if (checkType < 2) {
            checkType = 2;
            checkFlag = "Yes";
        }
        if (CollectionUtils.isNotEmpty(productGroupMsgList) && (checkType == 2 && "Yes".equals(checkFlag))) {
            msgMap.put("2", String.format("产品系列【%s】对应的产品族比例未维护！", String.join(";", productGroupMsgList)));
            return msgMap;
        }
        log.info("产品族分组共{}条", productGroupPercentDTOMap.size());

        //横竖装比例（根据国内海外、产品系列获取比例系数） 维度：国内海外+产品系列+年份   注意:横竖装系数（竖装+非标（根据竖装计算得到）） -- 按季度
        List<HorizontalOrVerticalDTO> horizontalOrVerticalDTOS = horizontalOrVerticalService.getHorizontalOrVerticalDTO(countryFlagList, allProductSeries,  Collections.singletonList(dataVersion));
        Map<String, List<HorizontalOrVerticalDTO>> horizontalOrVerticalDTOMap = horizontalOrVerticalDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), String.valueOf(dto.getYear()))));
        if (horizontalOrVerticalDTOMap.isEmpty()) {
            throw new BizException(String.format("根据国内海外[%s]、产品系列[%s]获取横竖装比例失败，请检查数据！", JSONUtil.toJsonStr(countryFlagList), JSONUtil.toJsonStr(allProductSeries)));
        }
        log.info("横竖装比例共{}条", horizontalOrVerticalDTOMap.size());
        List<String> horizontalOrVerticalDTOMsgList = new ArrayList<>();
        checkDataByProductSeriesList.forEach(p -> {
            if (CollectionUtils.isEmpty(horizontalOrVerticalDTOMap.get(p))) {
                horizontalOrVerticalDTOMsgList.add(p);
            }
        });
        if (checkType < 3) {
            checkType = 3;
            checkFlag = "Yes";
        }
        if (CollectionUtils.isNotEmpty(productGroupMsgList) && (checkType == 3 && "Yes".equals(checkFlag))) {
            msgMap.put("3", String.format("产品系列【%s】对应的横竖装比例未维护！", String.join(";", horizontalOrVerticalDTOMsgList)));
            return msgMap;
        }

        //长期功率维护（根据产品族获取长期预测功率） 维度：国内海外+产品族+横竖装+年份  -- 按月
        List<ProductFamilyCalculationPowerMaintenanceDTO> productFamilyCalculationPowerMaintenanceDTOS = getProductFamilyCalculationPowerMaintenanceDTO(productGroupList, allYears);
        Map<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> productFamilyCalculationPowerMaintenanceDTOMap = productFamilyCalculationPowerMaintenanceDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductFamily(), dto.getInstallType(), String.valueOf(dto.getYear()))));
        if (productFamilyCalculationPowerMaintenanceDTOMap.isEmpty()) {
            throw new BizException(String.format("产品族[%s],获取产品族测算功率失败，请检查数据！", JSONUtil.toJsonStr(productGroupList)));
        }
        Map<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> checkProductFamilyCalculationPowerMaintenanceMap = productFamilyCalculationPowerMaintenanceDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductFamily(), String.valueOf(dto.getYear()))));
        List<String> productFamilyCalculationPowerMaintenanceMsgList = new ArrayList<>();
        checkProductGroupList.forEach(p -> {
            List<ProductFamilyCalculationPowerMaintenanceDTO> productFamilyCalculationPowerMaintenanceList = checkProductFamilyCalculationPowerMaintenanceMap.get(p);
            if (CollectionUtils.isEmpty(productFamilyCalculationPowerMaintenanceList) || productFamilyCalculationPowerMaintenanceList.size() != 2) {
                productFamilyCalculationPowerMaintenanceMsgList.add(p);
            }

        });
        if (checkType < 4) {
            checkType = 4;
            checkFlag = "Yes";
        }
        if (CollectionUtils.isNotEmpty(productFamilyCalculationPowerMaintenanceMsgList) && (checkType == 4 && "Yes".equals(checkFlag))) {
            msgMap.put("4", String.format("产品族【%s】对应的产品族测算功率(横竖装)未维护！", String.join(";", productFamilyCalculationPowerMaintenanceMsgList)));
            return msgMap;
        }
        log.info("长期功率维护共{}条", productFamilyCalculationPowerMaintenanceDTOMap.size());

        //组件良率(仅获取指标等于工原损的数据。组件良率=1-工原损) 维度：国内海外+产品族+产品系列+车间+年份
        List<ConfigModuleGoodDTO> configModuleGoodDTOS = getConfigModuleGoodDTO(allYears);
        Map<String, List<ConfigModuleGoodDTO>> configModuleGoodDTOMap = configModuleGoodDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getIsOversea(), dto.getProductFamily(), dto.getProductSeries(), dto.getWorkshop(), dto.getYear())));
        if (configModuleGoodDTOMap.isEmpty()) {
            throw new BizException(String.format("【%s】获取组件良率失败，请检查数据！", JSONUtil.toJsonStr(productGroupList)));
        }
        List<String> configModuleGoodDTOMsgList = new ArrayList<>();
        importActualCapacityMap.forEach((k, v) -> {
            String isOversea = v.get(0).getCountryFlag();
            String productSeries = v.get(0).getProductSeries();
            String workshop = v.get(0).getWorkshop();
            Integer year = v.get(0).getYear();
            List<ProductGroupPercentDTO> groupPercentDTOList = productGroupPercentMap.get(String.join(DEFAULT_REGEX, isOversea, productSeries, String.valueOf(year)));
            if (CollectionUtils.isNotEmpty(groupPercentDTOList)) {
                groupPercentDTOList.stream().sorted(Comparator.comparing(ProductGroupPercentDTO::getProductGroup)).distinct().forEach(productInfoDTO -> {
                    String key = String.join(DEFAULT_REGEX, isOversea, productInfoDTO.getProductGroup(), productSeries, workshop, String.valueOf(year));
                    List<ConfigModuleGoodDTO> moduleGoodDTOList = configModuleGoodDTOMap.get(key);
                    if (CollectionUtils.isEmpty(moduleGoodDTOList)) {
                        configModuleGoodDTOMsgList.add(key);
                    }
                });
            }
        });
        if (checkType < 5) {
            checkType = 5;
            checkFlag = "Yes";
        }
        if (CollectionUtils.isNotEmpty(configModuleGoodDTOMsgList) && (checkType == 5 && "Yes".equals(checkFlag))) {
            msgMap.put("5", String.format("产品族【%s】对应的组件良率未维护！", String.join(";", configModuleGoodDTOMsgList)));
            return msgMap;
        }
        log.info("组件良率共{}条", configModuleGoodDTOMap.size());

        //电池片数 MAP ( 产品族 8 产品系列 6 --》 电池片数 2 )
        LovLineQuery apsPowerCellProdLovLineQuery = new LovLineQuery();
        apsPowerCellProdLovLineQuery.setCode(LovHeaderCodeConstant.APS_POWER_CELL_PROD_MAP);
        List<LovLineDTO> lovLineDTOS = instance().callService(Arrays.asList(apsPowerCellProdLovLineQuery));
        Map<String, List<LovLineDTO>> apsPowerCellProdlovMap = lovLineDTOS.stream().collect(Collectors.groupingBy(dto -> {
            String attribute8 = StringUtils.isBlank(dto.getAttribute8()) ? "" : dto.getAttribute8();
            String attribute6 = StringUtils.isBlank(dto.getAttribute6()) ? "" : dto.getAttribute6();
            return String.join(DEFAULT_REGEX, attribute8, attribute6);
        }));
        log.info("电池片数共{}条", apsPowerCellProdlovMap.size());

        //电池型号映射关系 map（电池型号+主栅 --》 分片方式）
        LovLineQuery lovLineQuery = new LovLineQuery();
        lovLineQuery.setCode(LovHeaderCodeConstant.APS_POWER_CELL_SERIES_TYPE);
        List<LovLineDTO> cellModelToShardlovLineDTOS = instance().callService(Arrays.asList(lovLineQuery));
        Map<String, List<LovLineDTO>> cellModelToShardMap = cellModelToShardlovLineDTOS.stream().collect(Collectors.groupingBy(dto -> {
            String a1lovValue = LovUtils.get(Long.valueOf(dto.getAttribute1())).getLovValue();
            String a4lovValue = LovUtils.get(Long.valueOf(dto.getAttribute4())).getLovValue();
            return String.join(DEFAULT_REGEX, a1lovValue, a4lovValue);
        }));

        //电池型号映射关系 map（电池型号+主栅 --》 分片方式）
        LovLineQuery lovLineQuery1 = new LovLineQuery();
        lovLineQuery1.setCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        List<LovLineDTO> shardlovLineDTOS = instance().callService(Arrays.asList(lovLineQuery1));
        Map<String, String> cellModelToMap = shardlovLineDTOS.stream().collect(Collectors.toMap(LovLineDTO::getLovName, LovLineDTO::getLovValue, (v1, v2) -> v1));
        log.info("电池型号映射关（电池型号+主栅--》分片方式）共{}条", cellModelToShardMap.size());
        //电池良率 维度：国内海外、（品类、晶体类型）电池型号、主栅（分片方式）、车间、年份
        List<ConfigCellGoodDTO> configCellGoodDTOS = getConfigCellGoodDTO(allYears);
        Map<String, List<ConfigCellGoodDTO>> configCellGoodDTOMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(configCellGoodDTOS)) {
            for (ConfigCellGoodDTO dto : configCellGoodDTOS) {
                String isOversea = dto.getIsOversea();
                String productCategory = dto.getProductCategory();
                String crystalType = dto.getCrystalType();
                //电池型号
                String cellModel = String.join(DEFAULT_REGEX, productCategory, crystalType.replace("型", ""));
                String mainGrid = dto.getMainGrid();
                if (CollectionUtils.isNotEmpty(allCellModel) && allCellModel.contains(cellModel)) {
                   /*  List<LovLineDTO> lovLineDTOList = cellModelToShardMap.get(String.join(DEFAULT_REGEX, cellModel, mainGrid));
                     if (CollectionUtils.isEmpty(lovLineDTOList)) {
                        throw new BizException(String.format("电池型号[%s],主栅[%s],兑换分片方式失败，请检查数据！", cellModel, mainGrid));
                    } */
                    //分片方式
                    //LovLineDTO lovLineDTO = lovLineDTOList.stream().findFirst().get();
                    //String cellShard = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute4())).getLovValue();
                    String cellShard = cellModelToMap.get(Objects.nonNull(dto.getFragmentType()) ? dto.getFragmentType().equals("二半片") ? "二分片" : dto.getFragmentType() : null);
                    String workshop = dto.getWorkshop();
                    String year = dto.getYear();
                    String key = String.join(DEFAULT_REGEX, isOversea, cellModel, cellShard, workshop, year, dto.getProductType());
                    List<ConfigCellGoodDTO> values = configCellGoodDTOMap.get(key);
                    if (CollectionUtils.isEmpty(values)) {
                        configCellGoodDTOMap.put(key, Collections.singletonList(dto));
                    } else {
                        List<ConfigCellGoodDTO> mutableValues = new ArrayList<>(values);
                        mutableValues.add(dto);
                        configCellGoodDTOMap.put(key, mutableValues);
                    }
                }
            }
        } else if (ProductTypeEnum.WAFER.equals(calculateDTO.getProductType())) {
            throw new BizException("电池排产量数据未维护!");
        }
        List<String> configCellGoodDTOMsgList = new ArrayList<>();
        cellDistributionDTOMap.forEach((k, v) -> {
            List<ConfigCellGoodDTO> cellGoodDTOS = configCellGoodDTOMap.get(k);
            if (CollectionUtils.isEmpty((cellGoodDTOS))) {
                configCellGoodDTOMsgList.add(k);
            }
        });
        if (checkType < 6) {
            checkType = 6;
            checkFlag = "Yes";
        }
        if (CollectionUtils.isNotEmpty(configCellGoodDTOMsgList) && (checkType == 6 && "Yes".equals(checkFlag)) && ProductTypeEnum.WAFER.equals(calculateDTO.getProductType())) {
            msgMap.put("6", String.format("【%s】对应的电池良率未维护！", String.join(";", configCellGoodDTOMsgList)));
            return msgMap;
        }

        log.info("***组装数据***begin***");
        // 按照 （国内海外、产品族、产品系列、电池型号、分片方式、车间、年份、横竖装） 维度 汇总
        log.info("***组装数据***end***");

        //收集整理后数据
        List<CellWaferWeightMaintenance> cellWaferWeightMaintenanceList = new ArrayList<>();
        List<CellWaferWeightMaintenance> cellWeightMaintenances = new ArrayList<>();
        List<CellWaferWeightMaintenance> waferWeightMaintenances = new ArrayList<>();

        log.info("***开始计算***begin***");

        //电池
        if (ProductTypeEnum.CELL.equals(productType)) {
            log.info("***电池类型计算***begin***");
            calculateCell(importActualCapacityDTOS, dataVersion, importActualCapacityMap, cellDistributionDTOMap, configCellGoodDTOMap, productSeriesMap, apsPowerCellProdlovMap, productGroupPercentDTOMap, configModuleGoodDTOMap, productFamilyCalculationPowerMaintenanceDTOMap, horizontalOrVerticalDTOMap, cellWeightMaintenances);
            log.info("***电池类型计算***end***");
        }
        //硅片
        if (ProductTypeEnum.WAFER.equals(productType)) {
            log.info("***电池类型计算***begin***");
            calculateCell(importActualCapacityDTOS, dataVersion, importActualCapacityMap, cellDistributionDTOMap, configCellGoodDTOMap, productSeriesMap, apsPowerCellProdlovMap, productGroupPercentDTOMap, configModuleGoodDTOMap, productFamilyCalculationPowerMaintenanceDTOMap, horizontalOrVerticalDTOMap, cellWeightMaintenances);
            log.info("***电池类型计算***end***");

            log.info("***硅片类型计算***begin***");
            // 实际落地表维度： 国内/海外 + 电池型号 + 年份 + 月份

            //硅片MW折算系数
            //（按电池型号、月份维度）
            //            各电池产品=
            //                    (1*100*电池片数/组件良率/组件功率/平均电池良率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量

            //（按电池型号、月份维度）
            //            平均电池良率=
            //（各车间各电池型号分片方式排产量*电池良率）/汇总电池排产量
            //(1*100*电池片数/组件良率(产品族+产品系列)/组件功率（产品族+横竖装）) * (产品族比例*横竖装比例*组件排产量）/汇总组件排产量
            List<CellWaferWeightMaintenanceCalculateResultDTO> maintenanceCalculateResultDTOS = fillData(importActualCapacityDTOS, dataVersion, importActualCapacityMap, cellDistributionDTOMap, configCellGoodDTOMap, productSeriesMap, apsPowerCellProdlovMap, productGroupPercentDTOMap, configModuleGoodDTOMap, productFamilyCalculationPowerMaintenanceDTOMap, horizontalOrVerticalDTOMap);

            // 分组计算结果DTO
            //String.join(DEFAULT_REGEX,dto.getCountryFlag(), dto.getCellModel(), String.valueOf(dto.getYear()))
            Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> resultDTOMAP = groupByCountryFlagCellModelYear(maintenanceCalculateResultDTOS);
            //String.join(DEFAULT_REGEX,dto.getCountryFlag(),dto.getWorkshop(), dto.getCellModel(), String.valueOf(dto.getYear()))));
            //Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> resultFilter = groupByCountryFlagWorkshopCellModelYear(maintenanceCalculateResultDTOS);
            //组件排产量汇总
            //Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> importActualCapacitySumMap = maintenanceCalculateResultDTOS.stream()
            //        .collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getCellModel(), String.valueOf(dto.getYear()))));


            // 组件排产量汇总
            //Map<String, List<ImportActualCapacityDTO>> importActualCapacitySumMap = getImportActualCapacitySumMap(importActualCapacityDTOS, resultFilter);

            // 电池排查量 汇总
            List<CellDistributionDTO> temp = new ArrayList<>();
            getBatteryAverage(cellDistributionDTOS, temp, configCellGoodDTOMap,cellModelToShardMap);
            Map<String, List<CellDistributionDTO>> cellGoodRageMap = groupByCountryFlagProductSeriesYear(temp);

            List<CellWaferWeightMaintenance> cellWeightMaintenancesListTemp = processCalculateResults(resultDTOMAP, cellGoodRageMap);

            if (CollectionUtils.isNotEmpty(cellWeightMaintenancesListTemp)) {
                Map<String, List<CellWaferWeightMaintenance>> tempMap = groupCellWaferWeightMaintenances(cellWeightMaintenancesListTemp);
                tempMap.forEach((k, value) -> {
                    CellWaferWeightMaintenance cellWaferWeightMaintenance = aggregateCellWaferWeightMaintenance(value);
                    waferWeightMaintenances.add(cellWaferWeightMaintenance);
                });
            }
            log.info("***硅片类型计算***end***");
        }
        log.info("***计算结束***end***");
        cellWaferWeightMaintenanceList.addAll(cellWeightMaintenances);
        cellWaferWeightMaintenanceList.addAll(waferWeightMaintenances);

        //覆盖相同年份数据
        deleteSameYearData(cellWaferWeightMaintenanceList, productType);

        cellWaferWeightMaintenanceList.forEach(i->{
            i.setDataVersion(dataVersion);
        });
        // 计算结果入库
        repository.saveAll(cellWaferWeightMaintenanceList);
        //保存产品族测算功率，组件良率，电池良率
        save(dataVersion,productFamilyCalculationPowerMaintenanceDTOS,configModuleGoodDTOS,configCellGoodDTOS,cellWeightMaintenances);
        return msgMap;
    }

    private void save(String dataVersion, List<ProductFamilyCalculationPowerMaintenanceDTO> productFamilyCalculationPowerMaintenanceDTOS, List<ConfigModuleGoodDTO> configModuleGoodDTOS, List<ConfigCellGoodDTO> configCellGoodDTOS, List<CellWaferWeightMaintenance> cellWeightMaintenances) {
        List<ProductFamilyCalculationPowerMaintenanceSnapshot> collect = productFamilyCalculationPowerMaintenanceDTOS.stream().map(i -> {
            ProductFamilyCalculationPowerMaintenanceSnapshot productFamilyCalculationPowerMaintenanceSnapshot = new ProductFamilyCalculationPowerMaintenanceSnapshot();
            BeanUtils.copyProperties(i, productFamilyCalculationPowerMaintenanceSnapshot);
            productFamilyCalculationPowerMaintenanceSnapshot.setDataVersion(dataVersion);
            return productFamilyCalculationPowerMaintenanceSnapshot;
        }).collect(Collectors.toList());
        productFamilyCalculationPowerMaintenanceSnapshotRepository.deleteAllByDataVersion(dataVersion);
        productFamilyCalculationPowerMaintenanceSnapshotRepository.saveAll(collect);
        List<ConfigModuleGoodSnapshot> collect1 = configModuleGoodDTOS.stream().map(i -> {
            ConfigModuleGoodSnapshot configModuleGoodSnapshot = new ConfigModuleGoodSnapshot();
            BeanUtils.copyProperties(i, configModuleGoodSnapshot);
            configModuleGoodSnapshot.setDataVersion(dataVersion);
            return configModuleGoodSnapshot;
        }).collect(Collectors.toList());
        configModuleGoodSnapshotRepository.deleteAllByDataVersion(dataVersion);
        configModuleGoodSnapshotRepository.saveAll(collect1);

        List<ConfigCellGoodSnapshot> collect2 = configCellGoodDTOS.stream().map(i -> {
            ConfigCellGoodSnapshot configCellGoodSnapshot = new ConfigCellGoodSnapshot();
            BeanUtils.copyProperties(i, configCellGoodSnapshot);
            configCellGoodSnapshot.setDataVersion(dataVersion);
            return configCellGoodSnapshot;
        }).collect(Collectors.toList());
        configCellGoodSnapshotRepository.deleteAllByDataVersion(dataVersion);
        configCellGoodSnapshotRepository.saveAll(collect2);

        List<AveragePowerSnapshot> collect3 = cellWeightMaintenances.stream().map(i -> {
            AveragePowerSnapshot averagePowerSnapshot = new AveragePowerSnapshot();
            BeanUtils.copyProperties(i, averagePowerSnapshot);
            averagePowerSnapshot.setDataVersion(dataVersion);
            return averagePowerSnapshot;
        }).collect(Collectors.toList());
        averagerPowerSnapshotRepository.deleteAllByDataVersion(dataVersion);
        averagerPowerSnapshotRepository.saveAll(collect3);

    }

    // 辅助方法定义
    private Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> groupByCountryFlagCellModelYear(List<CellWaferWeightMaintenanceCalculateResultDTO> list) {
        return list.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getWaferModel(), String.valueOf(dto.getYear()))));
    }

    private Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> groupByCountryFlagWorkshopCellModelYear(List<CellWaferWeightMaintenanceCalculateResultDTO> list) {
        return list.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getWorkshop(), dto.getCellModel(), String.valueOf(dto.getYear()))));
    }

    private Map<String, List<ImportActualCapacityDTO>> getImportActualCapacitySumMap(List<ImportActualCapacityDTO> dtos, Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> filter) {
        return dtos.stream().filter(dto -> CollectionUtils.isNotEmpty(filter.get(String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getWorkshop(), dto.getCellModel(), String.valueOf(dto.getYear()))))).collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getCellModel(), String.valueOf(dto.getYear()))));
    }

    private Map<String, List<CellDistributionDTO>> groupByCountryFlagProductSeriesYear(List<CellDistributionDTO> dtos) {
        return dtos.stream().collect(Collectors.groupingBy(item -> String.join(DEFAULT_REGEX, item.getCountryFlag(), item.getProductSeries(), String.valueOf(item.getYear()))));
    }

    private List<CellWaferWeightMaintenance> processCalculateResults(Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> resultDTOMAP, Map<String, List<CellDistributionDTO>> cellGoodRageMap) {
        List<CellWaferWeightMaintenance> resultList = new ArrayList<>();
        resultDTOMAP.forEach((key, value) -> {
            BigDecimal[] monthlySums = calculateMonthlySums(value);
            updateCalculateResultDTOs(value, cellGoodRageMap.get(key), monthlySums, resultList);
        });
        return resultList;
    }

    private BigDecimal[] calculateMonthlySums(List<CellWaferWeightMaintenanceCalculateResultDTO> capacityDTOS) {
        BigDecimal[] sums = new BigDecimal[12];
        // 总排产量 每个月汇总
        for (int i = 0; i < 12; i++) {
            BigDecimal acc = BigDecimal.ZERO;
            int indexMonth = i + 1;
            for (CellWaferWeightMaintenanceCalculateResultDTO item : capacityDTOS) {
                BigDecimal temp = BigDecimal.ONE;
                if (BigDecimal.ZERO.compareTo((BigDecimal) invokeGetter(item, "getM" + indexMonth + "QuantityPFCPM")) == 0 || BigDecimal.ZERO.compareTo((BigDecimal) invokeGetter(item, "getQuantityM" + indexMonth)) == 0) {

                    temp = BigDecimal.ZERO;
                }
                BigDecimal multiply = ((BigDecimal) Objects.requireNonNull(invokeGetter(item, "getM" + indexMonth + "Quantity"))).multiply((BigDecimal) invokeGetter(item, "getQ" + indexMonth + "PercentHV")).multiply((BigDecimal) invokeGetter(item, "getQ" + indexMonth + "Percent")).multiply(temp);
                acc = acc.add(multiply);
            }
            sums[i] = acc;
        }
        return sums;
    }

    private void updateCalculateResultDTOs(List<CellWaferWeightMaintenanceCalculateResultDTO> dtos, List<CellDistributionDTO> distributionDTOS, BigDecimal[] monthlySums, List<CellWaferWeightMaintenance> resultList) {
        if (CollectionUtils.isNotEmpty(distributionDTOS)) {
            for (CellWaferWeightMaintenanceCalculateResultDTO dto : dtos) {
                setQuantities(dto, distributionDTOS);
                resultList.add(getWaferWeightMaintenance(dto, monthlySums[0], monthlySums[1], monthlySums[2], monthlySums[3], monthlySums[4], monthlySums[5], monthlySums[6], monthlySums[7], monthlySums[8], monthlySums[9], monthlySums[10], monthlySums[11]));
            }
        } else {
            log.error("电池平均良率不存在{}", dtos.get(0).toString());
        }
    }

    private void setQuantities(CellWaferWeightMaintenanceCalculateResultDTO dto, List<CellDistributionDTO> distributionDTOS) {
        IntStream.rangeClosed(1, 12).forEach(month -> {
            BigDecimal quantity = distributionDTOS.stream().map(distributionDTO -> (BigDecimal) invokeGetter(distributionDTO, "getM" + month + "Quantity")).reduce(BigDecimal.ZERO, BigDecimal::add);
            try {
                dto.getClass().getMethod("setQuantityM" + month + "CG", BigDecimal.class).invoke(dto, quantity);
            } catch (Exception e) {
                log.error("设置数量时出错", e);
            }
        });
    }


    private Map<String, List<CellWaferWeightMaintenance>> groupCellWaferWeightMaintenances(List<CellWaferWeightMaintenance> maintenances) {
        return maintenances.stream().collect(Collectors.groupingBy(m -> String.join(DEFAULT_REGEX1, m.getCellModel(), m.getCountryFlag(), m.getYear().toString())));
    }

    private CellWaferWeightMaintenance aggregateCellWaferWeightMaintenance(List<CellWaferWeightMaintenance> maintenances) {
        CellWaferWeightMaintenance aggregated = new CellWaferWeightMaintenance();
        BeanUtils.copyProperties(maintenances.get(0), aggregated);
        IntStream.rangeClosed(1, 12).forEach(month -> {
            BigDecimal sumQuantity = maintenances.stream().map(maintenance -> (BigDecimal) invokeGetter(maintenance, "getM" + month + "Quantity")).reduce(BigDecimal.ZERO, BigDecimal::add);
            try {
                aggregated.getClass().getMethod("setM" + month + "Quantity", BigDecimal.class).invoke(aggregated, sumQuantity);
            } catch (Exception e) {
                log.error("聚合数量时出错", e);
            }
        });
        return aggregated;
    }

    private static void getBatteryAverage(List<CellDistributionDTO> cellDistributionDTOS, List<CellDistributionDTO> temp, Map<String, List<ConfigCellGoodDTO>> configCellGoodDTOMap, Map<String, List<LovLineDTO>> cellModelToShardMap) {

        // 集合初始化
        Map<String, List<CellDistributionDTO>> processMap = new HashMap<>();
        Map<String, List<CellDistributionDTO>> processAverageMap = new HashMap<>();

        // 数据处理与分组
        cellDistributionDTOS.forEach(p -> {
            String productSeries = p.getProductSeries();
            String cellShard = p.getCellShard();
            String productType = null;
            List<LovLineDTO> lovLineDTOS = cellModelToShardMap.get(String.join(DEFAULT_REGEX, productSeries, cellShard));
            if(CollectionUtils.isNotEmpty(lovLineDTOS)){
                LovLineDTO lovLineDTO = LovUtils.get(Long.valueOf(lovLineDTOS.get(0).getAttribute5()));
                productType = lovLineDTO.getLovValue();
            }
            if (productSeries != null && productSeries.contains("2.0")) {
                productSeries = productSeries.replace(" 2.0", "");
            }
            String key = String.join(DEFAULT_REGEX, p.getCountryFlag(), productSeries, cellShard, p.getWorkshop(), String.valueOf(p.getYear()), productType);
            List<ConfigCellGoodDTO> cellGoodDTOS = configCellGoodDTOMap.get(key);
            if (CollectionUtils.isNotEmpty(cellGoodDTOS)) {
                ConfigCellGoodDTO configCellGoodDTO = cellGoodDTOS.stream().findFirst().get();
                CellDistributionDTO processedDTO = copyAndProcessDTO(p, configCellGoodDTO); // 复制并处理单个DTO
                processedDTO.setProductSeries(productSeries);
                String groupKey = String.join(DEFAULT_REGEX, p.getCountryFlag(), productSeries, String.valueOf(p.getYear()));
                processAverageMap.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(p);
                processMap.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(processedDTO);
            } else {
                log.error("电池良率为空！{}", key);
            }
        });

        // 计算平均值
        processMap.forEach((k, v) -> {
            List<CellDistributionDTO> averageLit = processAverageMap.get(k);
            CellDistributionDTO avgObject = calculateAverage(v, averageLit);
            temp.add(avgObject);
        });
    }

    private static CellDistributionDTO copyAndProcessDTO(CellDistributionDTO original, ConfigCellGoodDTO config) {
        CellDistributionDTO dto = new CellDistributionDTO();
        BeanUtils.copyProperties(original, dto);
        for (int i = 1; i <= 12; i++) {
            BigDecimal quantity = Optional.ofNullable((BigDecimal) invokeGetter(dto, "getM" + i + "Quantity")).orElse(BigDecimal.ZERO);
            BigDecimal multiplier = Optional.ofNullable((BigDecimal) invokeGetter(config, "getQuantityM" + i)).orElse(BigDecimal.ZERO);
            invokeSetter(original, "setM" + i + "Quantity", quantity.multiply(multiplier));
        }
        return dto;
    }

    private static CellDistributionDTO calculateAverage(List<CellDistributionDTO> dtos, List<CellDistributionDTO> averageList) {
        CellDistributionDTO avgDTO = new CellDistributionDTO();
        // 设置基础属性
        avgDTO.setCountryFlag(dtos.get(0).getCountryFlag());
        avgDTO.setProductSeries(dtos.get(0).getProductSeries());
        avgDTO.setYear(dtos.get(0).getYear());

        for (int i = 1; i <= 12; i++) {
            BigDecimal sum = BigDecimal.ZERO;
            for (CellDistributionDTO cellDistributionDTO : dtos) {
                BigDecimal bigDecimal = (BigDecimal) invokeGetter(cellDistributionDTO, "getM" + i + "Quantity");
                sum = sum.add(bigDecimal);
            }
            BigDecimal avgSum = BigDecimal.ZERO;
            for (CellDistributionDTO dto : averageList) {
                if (((BigDecimal) invokeGetter(dto, "getM" + i + "Quantity")).compareTo(BigDecimal.ZERO) != 0) {
                    BigDecimal bigDecimal = (BigDecimal) invokeGetter(dto, "getM" + i + "Quantity");
                    avgSum = avgSum.add(bigDecimal);
                }
            }

            invokeSetter(avgDTO, "setM" + i + "Quantity", sum.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : avgSum.divide(sum, 8, RoundingMode.HALF_UP));
        }
        return avgDTO;
    }

    private static Object invokeGetter(Object obj, String methodName) {
        try {
            Method method = obj.getClass().getMethod(methodName);
            return method.invoke(obj);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static void invokeSetter(Object obj, String methodName, Object value) {
        try {
            Method method = obj.getClass().getMethod(methodName, value.getClass());
            method.invoke(obj, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void calculateCell(List<ImportActualCapacityDTO> importActualCapacityDTOS, String dataVersion, Map<String, List<ImportActualCapacityDTO>> importActualCapacityMap, Map<String, List<CellDistributionDTO>> cellDistributionDTOMap, Map<String, List<ConfigCellGoodDTO>> configCellGoodDTOMap, Map<String, List<ProductInfoDTO>> productSeriesMap, Map<String, List<LovLineDTO>> apsPowerCellProdlovMap, Map<String, List<ProductGroupPercentDTO>> productGroupPercentDTOMap, Map<String, List<ConfigModuleGoodDTO>> configModuleGoodDTOMap, Map<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> productFamilyCalculationPowerMaintenanceDTOMap, Map<String, List<HorizontalOrVerticalDTO>> horizontalOrVerticalDTOMap, List<CellWaferWeightMaintenance> cellWeightMaintenances) {
        // 实际落地表维度： 国内/海外 + 电池型号 + 分片方式 + 年份 + 月份

        //电池MW折算系数
        //（按电池型号+分片方式、月份维度）
        //各电池产品=
        //(1*100*电池片数/组件良率(产品族+产品系列)/组件功率（产品族+横竖装）) * (产品族比例*横竖装比例*组件排产量）/汇总组件排产量
        List<CellWaferWeightMaintenanceCalculateResultDTO> maintenanceCalculateResultDTOS = fillData(importActualCapacityDTOS, dataVersion, importActualCapacityMap, cellDistributionDTOMap, configCellGoodDTOMap, productSeriesMap, apsPowerCellProdlovMap, productGroupPercentDTOMap, configModuleGoodDTOMap, productFamilyCalculationPowerMaintenanceDTOMap, horizontalOrVerticalDTOMap);
        Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> resultDTOMAP = maintenanceCalculateResultDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getWorkshop(), dto.getProductSeries(), String.valueOf(dto.getYear()))));
        //组件排产量汇总
        Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> resultDTOMap = maintenanceCalculateResultDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getCellModel(), dto.getCellShard(), String.valueOf(dto.getYear()))));
        List<CellWaferWeightMaintenance> cellWeightMaintenancesListTemp = new ArrayList<>();
        for (Map.Entry<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> entry : resultDTOMAP.entrySet()) {
            //String key = entry.getKey();
            List<CellWaferWeightMaintenanceCalculateResultDTO> value = entry.getValue();
            log.info("计算key", entry.getKey());
            for (CellWaferWeightMaintenanceCalculateResultDTO resultDTO : value) {
                String key = String.join(DEFAULT_REGEX, resultDTO.getCountryFlag(), resultDTO.getCellModel(), resultDTO.getCellShard(), String.valueOf(resultDTO.getYear()));
                //总排产量 每个月汇总

                BigDecimal q1Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM1QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM1()) == 0 ? BigDecimal.ZERO : item.getM1Quantity().multiply(item.getQ1PercentHV()).multiply(item.getQ1Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q2Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM2QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM2()) == 0 ? BigDecimal.ZERO : item.getM2Quantity().multiply(item.getQ2PercentHV()).multiply(item.getQ2Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q3Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM3QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM3()) == 0 ? BigDecimal.ZERO : item.getM3Quantity().multiply(item.getQ3PercentHV()).multiply(item.getQ3Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q4Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM4QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM4()) == 0 ? BigDecimal.ZERO : item.getM4Quantity().multiply(item.getQ4PercentHV()).multiply(item.getQ4Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q5Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM5QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM5()) == 0 ? BigDecimal.ZERO : item.getM5Quantity().multiply(item.getQ5PercentHV()).multiply(item.getQ5Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q6Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM6QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM6()) == 0 ? BigDecimal.ZERO : item.getM6Quantity().multiply(item.getQ6PercentHV()).multiply(item.getQ6Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q7Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM7QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM7()) == 0 ? BigDecimal.ZERO : item.getM7Quantity().multiply(item.getQ7PercentHV()).multiply(item.getQ7Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q8Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM8QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM8()) == 0 ? BigDecimal.ZERO : item.getM8Quantity().multiply(item.getQ8PercentHV()).multiply(item.getQ8Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q9Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM9QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM9()) == 0 ? BigDecimal.ZERO : item.getM9Quantity().multiply(item.getQ9PercentHV()).multiply(item.getQ9Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q10Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM10QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM10()) == 0 ? BigDecimal.ZERO : item.getM10Quantity().multiply(item.getQ10PercentHV()).multiply(item.getQ10Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q11Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM11QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM11()) == 0 ? BigDecimal.ZERO : item.getM11Quantity().multiply(item.getQ11PercentHV()).multiply(item.getQ11Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal q12Sum = resultDTOMap.get(key).stream().map(item -> BigDecimal.ZERO.compareTo(item.getM12QuantityPFCPM()) == 0 || BigDecimal.ZERO.compareTo(item.getQuantityM12()) == 0 ? BigDecimal.ZERO : item.getM12Quantity().multiply(item.getQ12PercentHV()).multiply(item.getQ12Percent())).reduce(BigDecimal.ZERO, BigDecimal::add);
                CellWaferWeightMaintenance cellWaferWeightMaintenance = getCellWeightMaintenance(resultDTO, q1Sum, q2Sum, q3Sum, q4Sum, q5Sum, q6Sum, q7Sum, q8Sum, q9Sum, q10Sum, q11Sum, q12Sum);
                cellWeightMaintenancesListTemp.add(cellWaferWeightMaintenance);
            }
        }
        if (CollectionUtils.isNotEmpty(cellWeightMaintenancesListTemp)) {
            Map<String, List<CellWaferWeightMaintenance>> temp = cellWeightMaintenancesListTemp.stream().collect(Collectors.groupingBy(m -> String.join(DEFAULT_REGEX1, m.getCountryFlag(), m.getCellModel(), m.getCellShard(), m.getCountryFlag(), m.getYear().toString())));
            temp.forEach((k, value) -> {
                CellWaferWeightMaintenance cellWaferWeightMaintenance = new CellWaferWeightMaintenance();
                BeanUtils.copyProperties(value.get(0), cellWaferWeightMaintenance);
                cellWaferWeightMaintenance.setM1Quantity(value.stream().map(CellWaferWeightMaintenance::getM1Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM2Quantity(value.stream().map(CellWaferWeightMaintenance::getM2Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM3Quantity(value.stream().map(CellWaferWeightMaintenance::getM3Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM4Quantity(value.stream().map(CellWaferWeightMaintenance::getM4Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM5Quantity(value.stream().map(CellWaferWeightMaintenance::getM5Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM6Quantity(value.stream().map(CellWaferWeightMaintenance::getM6Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM7Quantity(value.stream().map(CellWaferWeightMaintenance::getM7Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM8Quantity(value.stream().map(CellWaferWeightMaintenance::getM8Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM9Quantity(value.stream().map(CellWaferWeightMaintenance::getM9Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM10Quantity(value.stream().map(CellWaferWeightMaintenance::getM10Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM11Quantity(value.stream().map(CellWaferWeightMaintenance::getM11Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setM12Quantity(value.stream().map(CellWaferWeightMaintenance::getM12Quantity).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower1(value.stream().map(CellWaferWeightMaintenance::getAveragePower1).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower2(value.stream().map(CellWaferWeightMaintenance::getAveragePower2).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower3(value.stream().map(CellWaferWeightMaintenance::getAveragePower3).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower4(value.stream().map(CellWaferWeightMaintenance::getAveragePower4).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower5(value.stream().map(CellWaferWeightMaintenance::getAveragePower5).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower6(value.stream().map(CellWaferWeightMaintenance::getAveragePower6).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower7(value.stream().map(CellWaferWeightMaintenance::getAveragePower7).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower8(value.stream().map(CellWaferWeightMaintenance::getAveragePower8).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower9(value.stream().map(CellWaferWeightMaintenance::getAveragePower9).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower10(value.stream().map(CellWaferWeightMaintenance::getAveragePower10).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower11(value.stream().map(CellWaferWeightMaintenance::getAveragePower11).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWaferWeightMaintenance.setAveragePower12(value.stream().map(CellWaferWeightMaintenance::getAveragePower12).reduce(BigDecimal.ZERO, BigDecimal::add));
                cellWeightMaintenances.add(cellWaferWeightMaintenance);
            });
        }
    }

    private Map<String, CellWaferWeightMaintenanceCalculateResultDTO> getCellGoodRateMap(List<CellWaferWeightMaintenanceCalculateResultDTO> valueList, Map<String, List<CellDistributionDTO>> cellGoodRateSumMap) {
        Map<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> cellGoodMap = valueList.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getWorkshop(), dto.getCellModel(), dto.getCellShard())));
        Map<String, CellWaferWeightMaintenanceCalculateResultDTO> cellGoodRateMap = new HashMap<>();
        for (Map.Entry<String, List<CellWaferWeightMaintenanceCalculateResultDTO>> cellEntry : cellGoodMap.entrySet()) {
            String cellEntryKey = cellEntry.getKey();
            if (cellGoodRateSumMap.get(cellEntryKey) == null) {
                log.info("电池兆瓦系数车间对应的电池排产量不存在产品系列{}", cellEntryKey);
                continue;
            }
            List<CellWaferWeightMaintenanceCalculateResultDTO> cellValueList = cellEntry.getValue();

            //汇总 平均电池良率
            BigDecimal cell1Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM1Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell2Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM2Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell3Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM3Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell4Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM4Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell5Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM5Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell6Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM6Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell7Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM7Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell8Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM8Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell9Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM9Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell10Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM10Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell11Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM11Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal cell12Sum = cellGoodRateSumMap.get(cellEntryKey).stream().map(CellDistributionDTO::getM12Quantity).reduce(BigDecimal.ZERO, BigDecimal::add);

            //平均电池良率 = （各车间各电池型号分片方式排产量*电池良率）/汇总电池排产量
            for (CellWaferWeightMaintenanceCalculateResultDTO resultDTO : cellValueList) {
                //平均电池良率 key
                String cellGoodKey = String.join(DEFAULT_REGEX, resultDTO.getWorkshop(), resultDTO.getCellModel(), resultDTO.getCellShard());
                aveCellGoodRate(resultDTO, cell1Sum, cell2Sum, cell3Sum, cell4Sum, cell5Sum, cell6Sum, cell7Sum, cell8Sum, cell9Sum, cell10Sum, cell11Sum, cell12Sum);
                cellGoodRateMap.put(cellGoodKey, resultDTO);
            }
        }
        return cellGoodRateMap;
    }

    private void deleteSameYearData(List<CellWaferWeightMaintenance> cellWaferWeightMaintenanceList, ProductTypeEnum productType) {
        if (CollectionUtils.isNotEmpty(cellWaferWeightMaintenanceList)) {
            //收集年份
            List<Integer> years = cellWaferWeightMaintenanceList.stream().map(CellWaferWeightMaintenance::getYear).distinct().collect(Collectors.toList());
            List<String> cellModels = cellWaferWeightMaintenanceList.stream().map(CellWaferWeightMaintenance::getCellModel).distinct().collect(Collectors.toList());
            //清除相同年份数据
            QCellWaferWeightMaintenance qCellWaferWeightMaintenance = QCellWaferWeightMaintenance.cellWaferWeightMaintenance;
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            booleanBuilder.and(qCellWaferWeightMaintenance.year.in(years));
            //电池类型
            if (ProductTypeEnum.CELL.equals(productType)) {
                booleanBuilder.and(qCellWaferWeightMaintenance.productType.eq(ProductTypeEnum.CELL.getCode()));
            } else {//硅片类型
                booleanBuilder.and(qCellWaferWeightMaintenance.productType.in(ProductTypeEnum.WAFER.getCode(),ProductTypeEnum.CELL.getCode()));
            }
            booleanBuilder.and(qCellWaferWeightMaintenance.isDeleted.eq(DeleteEnum.NO.getCode()));
            List<CellWaferWeightMaintenance> deleteCellWaferWeightMaintenances = IterableUtils.toList(repository.findAll(booleanBuilder));
            if (CollectionUtils.isNotEmpty(deleteCellWaferWeightMaintenances)) {
                //逻辑删除
                List<Long> ids = deleteCellWaferWeightMaintenances.stream().map(CellWaferWeightMaintenance::getId).collect(Collectors.toList());
                this.logicDeleteByIds(ids);
            }
        }
    }

    private List<CellWaferWeightMaintenanceCalculateResultDTO> fillData(List<ImportActualCapacityDTO> importActualCapacityDTOS, String dataVersion, Map<String, List<ImportActualCapacityDTO>> importActualCapacityMap, Map<String, List<CellDistributionDTO>> cellDistributionDTOMap, Map<String, List<ConfigCellGoodDTO>> configCellGoodDTOMap, Map<String, List<ProductInfoDTO>> productSeriesMap, Map<String, List<LovLineDTO>> apsPowerCellProdlovMap, Map<String, List<ProductGroupPercentDTO>> productGroupPercentDTOMap, Map<String, List<ConfigModuleGoodDTO>> configModuleGoodDTOMap, Map<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> productFamilyCalculationPowerMaintenanceDTOMap, Map<String, List<HorizontalOrVerticalDTO>> horizontalOrVerticalDTOMap) {
        List<CellWaferWeightMaintenanceCalculateResultDTO> resultDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(importActualCapacityDTOS)) {
            //维度 ：国内海外 + 电池型号 + 电池分片方式 + 车间 + 年份

            for (Map.Entry<String, List<ImportActualCapacityDTO>> importActualCapacityEntry : importActualCapacityMap.entrySet()) {
                String importActualCapacityEntryKey = importActualCapacityEntry.getKey();
                List<ImportActualCapacityDTO> importActualCapacityDTOList = importActualCapacityEntry.getValue();
                log.info("组件排产数据处理begin:,key{{}}", importActualCapacityEntryKey);
                ImportActualCapacityDTO importActualCapacityDTO = importActualCapacityDTOList.stream().findFirst().get();
                log.info("组件排产{{}}", JSONUtil.toJsonStr(importActualCapacityDTO));

                //电池型号 + 电池分片方式
                String countryFlag = importActualCapacityDTO.getCountryFlag();
                String workshop = importActualCapacityDTO.getWorkshop();
                String year = String.valueOf(importActualCapacityDTO.getYear());
                String productSeries = importActualCapacityDTO.getProductSeries();
                CellWaferWeightMaintenanceCalculateResultDTO resultDTO = new CellWaferWeightMaintenanceCalculateResultDTO();
                resultDTO.setDataVersion(dataVersion);
                resultDTO.setCountryFlag(importActualCapacityDTO.getCountryFlag());
                resultDTO.setWorkshop(importActualCapacityDTO.getWorkshop());
                resultDTO.setYear(String.valueOf(importActualCapacityDTO.getYear()));
                resultDTO.setProductSeries(productSeries);
                resultDTO.setWaferModel(importActualCapacityDTO.getWaferModel());
                //补充组件排产量
                resultDTO.setM1Quantity(importActualCapacityDTO.getM1Quantity());
                resultDTO.setM2Quantity(importActualCapacityDTO.getM2Quantity());
                resultDTO.setM3Quantity(importActualCapacityDTO.getM3Quantity());
                resultDTO.setM4Quantity(importActualCapacityDTO.getM4Quantity());
                resultDTO.setM5Quantity(importActualCapacityDTO.getM5Quantity());
                resultDTO.setM6Quantity(importActualCapacityDTO.getM6Quantity());
                resultDTO.setM7Quantity(importActualCapacityDTO.getM7Quantity());
                resultDTO.setM8Quantity(importActualCapacityDTO.getM8Quantity());
                resultDTO.setM9Quantity(importActualCapacityDTO.getM9Quantity());
                resultDTO.setM10Quantity(importActualCapacityDTO.getM10Quantity());
                resultDTO.setM11Quantity(importActualCapacityDTO.getM11Quantity());
                resultDTO.setM12Quantity(importActualCapacityDTO.getM12Quantity());

                //维度 ：产品序列 + 产品族
                String productSeriesKey = String.join(DEFAULT_REGEX, productSeries);
                List<ProductInfoDTO> productInfoDTOList = productSeriesMap.get(productSeriesKey);
                if (CollectionUtils.isEmpty(productInfoDTOList)) {
                    log.info("[产品信息对照表]分组后数据不存在,key{{}}", productSeriesKey);
//                    throw new BizException(String.format("电池分配表ID[%s]，电池型号[%s]，分片方式[%s]，关联产品信息对照表失败，请检查数据！",
//                            importActualCapacityId,cellModel, cellShard));
                    continue;
                }
                for (ProductInfoDTO productInfoDTO : productInfoDTOList) {
                    CellWaferWeightMaintenanceCalculateResultDTO resultDTOTemp = new CellWaferWeightMaintenanceCalculateResultDTO();
                    BeanUtils.copyProperties(resultDTO, resultDTOTemp);
                    log.info("产品信息对照表{{}}", JSONUtil.toJsonStr(productInfoDTO));
                    Long productInfoDTOId = productInfoDTO.getId();
                    String cellModel = productInfoDTO.getCellModel();
                    String cellShard = productInfoDTO.getCellShard();
                    String notNullproductSeries = StringUtils.isBlank(productSeries) ? "" : productSeries;
                    String productGroup = StringUtils.isBlank(productInfoDTO.getProductGroup()) ? "" : productInfoDTO.getProductGroup();

                    resultDTOTemp.setCellModel(cellModel);
                    resultDTOTemp.setCellShard(cellShard);
                    resultDTOTemp.setProductGroup(productGroup);
                    resultDTOTemp.setProductSeries(productSeries);

                    //电池排产量
                    String cellDistributionKey = String.join(DEFAULT_REGEX, countryFlag, cellModel, cellShard, workshop, year);
                    List<CellDistributionDTO> cellDistributionDTOSList = cellDistributionDTOMap.get(cellDistributionKey);

                    if (CollectionUtils.isNotEmpty(cellDistributionDTOSList)) {
                        CellDistributionDTO cellDistributionDTO = cellDistributionDTOSList.stream().findFirst().get();
                        log.info("电池排产量{{}}", JSONUtil.toJsonStr(cellDistributionDTO));

                        //补充电池排产量
                        resultDTOTemp.setM1CellQuantity(cellDistributionDTO.getM1Quantity());
                        resultDTOTemp.setM2CellQuantity(cellDistributionDTO.getM2Quantity());
                        resultDTOTemp.setM3CellQuantity(cellDistributionDTO.getM3Quantity());
                        resultDTOTemp.setM4CellQuantity(cellDistributionDTO.getM4Quantity());
                        resultDTOTemp.setM5CellQuantity(cellDistributionDTO.getM5Quantity());
                        resultDTOTemp.setM6CellQuantity(cellDistributionDTO.getM6Quantity());
                        resultDTOTemp.setM7CellQuantity(cellDistributionDTO.getM7Quantity());
                        resultDTOTemp.setM8CellQuantity(cellDistributionDTO.getM8Quantity());
                        resultDTOTemp.setM9CellQuantity(cellDistributionDTO.getM9Quantity());
                        resultDTOTemp.setM10CellQuantity(cellDistributionDTO.getM10Quantity());
                        resultDTOTemp.setM11CellQuantity(cellDistributionDTO.getM11Quantity());
                        resultDTOTemp.setM12CellQuantity(cellDistributionDTO.getM12Quantity());

                        // 电池良率
                        //国内海外、（品类、晶体类型）电池型号、主栅（分片方式）、车间、年份
                        String configCellGoodKey = String.join(DEFAULT_REGEX, countryFlag, cellModel, cellShard, workshop, year);
                        List<ConfigCellGoodDTO> configCellGoodDTOSList = configCellGoodDTOMap.get(configCellGoodKey);
                        if (CollectionUtils.isNotEmpty(configCellGoodDTOSList)) {
                            ConfigCellGoodDTO configCellGoodDTO = configCellGoodDTOSList.stream().findFirst().get();
                            log.info("电池良率{{}}", JSONUtil.toJsonStr(configCellGoodDTO));

                            resultDTOTemp.setProductCategory(configCellGoodDTO.getProductCategory());
                            resultDTOTemp.setCrystalType(configCellGoodDTO.getCrystalType());
                            resultDTOTemp.setMainGrid(configCellGoodDTO.getMainGrid());
                            resultDTOTemp.setQuantityM1CG(configCellGoodDTO.getQuantityM1());
                            resultDTOTemp.setQuantityM2CG(configCellGoodDTO.getQuantityM2());
                            resultDTOTemp.setQuantityM3CG(configCellGoodDTO.getQuantityM3());
                            resultDTOTemp.setQuantityM4CG(configCellGoodDTO.getQuantityM4());
                            resultDTOTemp.setQuantityM5CG(configCellGoodDTO.getQuantityM5());
                            resultDTOTemp.setQuantityM6CG(configCellGoodDTO.getQuantityM6());
                            resultDTOTemp.setQuantityM7CG(configCellGoodDTO.getQuantityM7());
                            resultDTOTemp.setQuantityM8CG(configCellGoodDTO.getQuantityM8());
                            resultDTOTemp.setQuantityM9CG(configCellGoodDTO.getQuantityM9());
                            resultDTOTemp.setQuantityM10CG(configCellGoodDTO.getQuantityM10());
                            resultDTOTemp.setQuantityM11CG(configCellGoodDTO.getQuantityM11());
                            resultDTOTemp.setQuantityM12CG(configCellGoodDTO.getQuantityM12());
                        }
                    }

                    // 电池片数
                    String apsPowerCellProdKey = String.join(DEFAULT_REGEX, productGroup, notNullproductSeries);
                    List<LovLineDTO> lovLineDTOSList = apsPowerCellProdlovMap.get(apsPowerCellProdKey);
                    if (CollectionUtils.isEmpty(lovLineDTOSList)) {
                        log.info("[产品信息对照表]中关联[电池片数]不存在,key{{}}", apsPowerCellProdKey);
//                        throw new BizException(String.format("电池分配表ID[%s]，关联产品信息对照表ID[%s]，产品族[%s]，产品系列[%s]，关联电池片数失败，请检查数据！",
//                                importActualCapacityId, productInfoDTOId,productGroup,notNullproductSeries));
                        continue;
                    }
                    LovLineDTO apsPowerCellLovLineDTO = lovLineDTOSList.stream().findFirst().get();

                    log.info("电池片数{{}}", JSONUtil.toJsonStr(apsPowerCellLovLineDTO));

                    int slices = 0;
                    if(productSeries.contains("2.0") && "Half-cut".equals(cellShard)){
                        slices = MathUtils.divideBigDecimal(new BigDecimal(apsPowerCellLovLineDTO.getAttribute2()), new BigDecimal("2")).intValue();
                    } else if (productSeries.contains("2.0") && "3-cut".equals(cellShard)){
                        slices = MathUtils.divideBigDecimal(new BigDecimal(apsPowerCellLovLineDTO.getAttribute2()), new BigDecimal("3")).intValue();
                    }else{
                        slices = Integer.parseInt(apsPowerCellLovLineDTO.getAttribute2());
                    }

                    resultDTOTemp.setSlices(slices);

                    // 产品族比例 按季度
                    String productGroupPercentKey = String.join(DEFAULT_REGEX, countryFlag, productGroup, notNullproductSeries, year);
                    List<ProductGroupPercentDTO> productGroupPercentDTOSList = productGroupPercentDTOMap.get(productGroupPercentKey);
                    if (CollectionUtils.isEmpty(productGroupPercentDTOSList)) {
                        log.info(String.format("[产品信息对照表]中关联[产品族比例]不存在,key{%s}", productGroupPercentKey));
//                        throw new BizException(String.format("电池分配表ID[%s]，关联产品信息对照表ID[%s]，国内海外[%s]，产品族[%s]，产品系列[%s]，年份[%s]，关联产品族比例失败，请检查数据！",
//                                importActualCapacityId, productInfoDTOId,countryFlag,productGroup,notNullproductSeries,year));
                        continue;
                    }
                    ProductGroupPercentDTO productGroupPercentDTO = productGroupPercentDTOSList.stream().findFirst().get();

                    log.info("产品族比例{{}}", JSONUtil.toJsonStr(productGroupPercentDTO));

                    resultDTOTemp.setQ1Percent(productGroupPercentDTO.getQ1Percent());
                    resultDTOTemp.setQ2Percent(productGroupPercentDTO.getQ1Percent());
                    resultDTOTemp.setQ3Percent(productGroupPercentDTO.getQ1Percent());
                    resultDTOTemp.setQ4Percent(productGroupPercentDTO.getQ2Percent());
                    resultDTOTemp.setQ5Percent(productGroupPercentDTO.getQ2Percent());
                    resultDTOTemp.setQ6Percent(productGroupPercentDTO.getQ2Percent());
                    resultDTOTemp.setQ7Percent(productGroupPercentDTO.getQ3Percent());
                    resultDTOTemp.setQ8Percent(productGroupPercentDTO.getQ3Percent());
                    resultDTOTemp.setQ9Percent(productGroupPercentDTO.getQ3Percent());
                    resultDTOTemp.setQ10Percent(productGroupPercentDTO.getQ4Percent());
                    resultDTOTemp.setQ11Percent(productGroupPercentDTO.getQ4Percent());
                    resultDTOTemp.setQ12Percent(productGroupPercentDTO.getQ4Percent());

                    // 组件良率 按月
                    String configModuleGoodKey = String.join(DEFAULT_REGEX, countryFlag, productGroup, notNullproductSeries, workshop, year);
                    List<ConfigModuleGoodDTO> configModuleGoodDTOSList = configModuleGoodDTOMap.get(configModuleGoodKey);
                    if (CollectionUtils.isEmpty(configModuleGoodDTOSList)) {
                        log.info("[产品信息对照表]中关联[组件良率]不存在,key{{}}", configModuleGoodKey);
//                        throw new BizException(String.format("电池分配表ID[%s]，关联产品信息对照表ID[%s]，国内海外[%s]，产品族[%s]，产品序列[%s]，车间[%s]，年份[%s]，关联组件良率失败，请检查数据！",
//                                importActualCapacityId, productInfoDTOId,countryFlag, productGroup, notNullproductSeries, workshop, year));
                        continue;
                    }
                    ConfigModuleGoodDTO configModuleGoodDTO = configModuleGoodDTOSList.stream().findFirst().get();

                    log.info("组件良率{{}}", JSONUtil.toJsonStr(configModuleGoodDTO));

                    resultDTOTemp.setQuantityM1(Objects.isNull(configModuleGoodDTO.getQuantityM1()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM1());
                    resultDTOTemp.setQuantityM2(Objects.isNull(configModuleGoodDTO.getQuantityM2()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM2());
                    resultDTOTemp.setQuantityM3(Objects.isNull(configModuleGoodDTO.getQuantityM3()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM3());
                    resultDTOTemp.setQuantityM4(Objects.isNull(configModuleGoodDTO.getQuantityM4()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM4());
                    resultDTOTemp.setQuantityM5(Objects.isNull(configModuleGoodDTO.getQuantityM5()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM5());
                    resultDTOTemp.setQuantityM6(Objects.isNull(configModuleGoodDTO.getQuantityM6()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM6());
                    resultDTOTemp.setQuantityM7(Objects.isNull(configModuleGoodDTO.getQuantityM7()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM7());
                    resultDTOTemp.setQuantityM8(Objects.isNull(configModuleGoodDTO.getQuantityM8()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM8());
                    resultDTOTemp.setQuantityM9(Objects.isNull(configModuleGoodDTO.getQuantityM9()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM9());
                    resultDTOTemp.setQuantityM10(Objects.isNull(configModuleGoodDTO.getQuantityM10()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM10());
                    resultDTOTemp.setQuantityM11(Objects.isNull(configModuleGoodDTO.getQuantityM11()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM11());
                    resultDTOTemp.setQuantityM12(Objects.isNull(configModuleGoodDTO.getQuantityM12()) ? BigDecimal.ZERO : configModuleGoodDTO.getQuantityM12());

                    for (Map.Entry<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> productFamilyCalculationPowerMaintenanceEntry : productFamilyCalculationPowerMaintenanceDTOMap.entrySet()) {
                        //String productFamilyCalculationPowerMaintenanceEntryKey = productFamilyCalculationPowerMaintenanceEntry.getKey();
                        List<ProductFamilyCalculationPowerMaintenanceDTO> productFamilyCalculationPowerMaintenanceDTOList = productFamilyCalculationPowerMaintenanceEntry.getValue();
                        // 组件功率 按月
                        ProductFamilyCalculationPowerMaintenanceDTO productFamilyCalculationPowerMaintenanceDTO = productFamilyCalculationPowerMaintenanceDTOList.stream().findFirst().get();

                        if (!productFamilyCalculationPowerMaintenanceDTO.getProductFamily().equals(productGroup) || !productFamilyCalculationPowerMaintenanceDTO.getCountryFlag().equals(countryFlag)) {
                            log.info(String.format("产品族对应的组件功率不存在{%s}", productGroup));
                            continue;
                        }
                        log.info(String.format("组件功率{%s}", JSONUtil.toJsonStr(productFamilyCalculationPowerMaintenanceDTO)));
                        CellWaferWeightMaintenanceCalculateResultDTO resultDTOTemp1 = new CellWaferWeightMaintenanceCalculateResultDTO();
                        BeanUtils.copyProperties(resultDTOTemp, resultDTOTemp1);
                        Long productFamilyCalculationPowerMaintenanceDTOId = productFamilyCalculationPowerMaintenanceDTO.getId();

                        resultDTOTemp1.setDataVersion(dataVersion);
                        // p2_2898 MW折算系数计算-产品族功率统一扣减2.5W
                        // 功率预测提供的各产品族功率为测算功率（与财务保持统一），需要扣减2.5W转为入库功率再进行计算
                        resultDTOTemp1.setM1QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM1Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM1Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM1Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM2QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM2Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM2Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM2Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM3QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM3Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM3Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM3Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM4QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM4Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM4Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM4Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM5QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM5Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM5Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM5Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM6QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM6Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM6Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM6Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM7QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM7Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM7Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM7Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM8QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM8Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM8Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM8Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM9QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM9Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM9Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM9Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM10QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM10Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM10Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM10Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM11QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM11Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM11Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM11Quantity().subtract(BigDecimal.valueOf(2.5)));
                        resultDTOTemp1.setM12QuantityPFCPM(Objects.isNull(productFamilyCalculationPowerMaintenanceDTO.getM12Quantity()) || BigDecimal.ZERO.compareTo(productFamilyCalculationPowerMaintenanceDTO.getM12Quantity()) == 0 ? BigDecimal.ZERO : productFamilyCalculationPowerMaintenanceDTO.getM12Quantity().subtract(BigDecimal.valueOf(2.5)));

                        // 横竖装比例 按季度
                        String horizontalOrVerticalKey = String.join(DEFAULT_REGEX, countryFlag, notNullproductSeries, year);
                        List<HorizontalOrVerticalDTO> horizontalOrVerticalDTOSList = horizontalOrVerticalDTOMap.get(horizontalOrVerticalKey);
                        if (CollectionUtils.isEmpty(horizontalOrVerticalDTOSList)) {
                            log.info("横竖装比例不存在,key{{}}", horizontalOrVerticalKey);
//                            throw new BizException(String.format("电池分配表ID[%s]，关联产品信息对照表ID[%s]，关联组件功率ID[%s]，国内海外[%s]，产品系列[%s]，年份[%s]，关联横竖装比例失败，请检查数据！",
//                                    importActualCapacityId, productInfoDTOId, productFamilyCalculationPowerMaintenanceDTOId,countryFlag, notNullproductSeries, year));
                            continue;
                        }
                        HorizontalOrVerticalDTO horizontalOrVerticalDTO = horizontalOrVerticalDTOSList.stream().findFirst().get();

                        log.info(String.format("横竖装比例{%s}", JSONUtil.toJsonStr(horizontalOrVerticalDTO)));

                        String installType = productFamilyCalculationPowerMaintenanceDTO.getInstallType();
                        //竖装
                        if (QPConstant.InstallType.VERTICAL.getValue().equals(installType)) {
                            resultDTOTemp1.setInstallType(QPConstant.InstallType.VERTICAL.getValue());
                            resultDTOTemp1.setQ1PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent());
                            resultDTOTemp1.setQ2PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent());
                            resultDTOTemp1.setQ3PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent());
                            resultDTOTemp1.setQ4PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent());
                            resultDTOTemp1.setQ5PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent());
                            resultDTOTemp1.setQ6PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent());
                            resultDTOTemp1.setQ7PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent());
                            resultDTOTemp1.setQ8PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent());
                            resultDTOTemp1.setQ9PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent());
                            resultDTOTemp1.setQ10PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent());
                            resultDTOTemp1.setQ11PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent());
                            resultDTOTemp1.setQ12PercentHV(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent());
                        } else {//非标（横装）
                            resultDTOTemp1.setInstallType(QPConstant.InstallType.HORIZONTAL.getValue());
                            resultDTOTemp1.setQ1PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent()));
                            resultDTOTemp1.setQ2PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent()));
                            resultDTOTemp1.setQ3PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ1Percent()));
                            resultDTOTemp1.setQ4PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent()));
                            resultDTOTemp1.setQ5PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent()));
                            resultDTOTemp1.setQ6PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ2Percent()));
                            resultDTOTemp1.setQ7PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent()));
                            resultDTOTemp1.setQ8PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent()));
                            resultDTOTemp1.setQ9PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ3Percent()));
                            resultDTOTemp1.setQ10PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent()));
                            resultDTOTemp1.setQ11PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent()));
                            resultDTOTemp1.setQ12PercentHV(new BigDecimal(1).subtract(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent()) ? BigDecimal.ZERO : horizontalOrVerticalDTO.getQ4Percent()));
                        }

                        log.info("汇总数据{{}}", JSONUtil.toJsonStr(resultDTO));
                        resultDTOS.add(resultDTOTemp1);
                    }
                }
                log.info("电池分配数据处理end:,key{{}}", importActualCapacityEntryKey);
            }
        }
        return resultDTOS;
    }

    private void aveCellGoodRate(CellWaferWeightMaintenanceCalculateResultDTO resultDTO, BigDecimal cell1Sum, BigDecimal cell2Sum, BigDecimal cell3Sum, BigDecimal cell4Sum, BigDecimal cell5Sum, BigDecimal cell6Sum, BigDecimal cell7Sum, BigDecimal cell8Sum, BigDecimal cell9Sum, BigDecimal cell10Sum, BigDecimal cell11Sum, BigDecimal cell12Sum) {
        //各车间各电池型号分片方式排产量 + 电池良率
        //1月平均电池良率
        BigDecimal m1CellQuantity = resultDTO.getM1CellQuantity();
        BigDecimal quantityM1CG = resultDTO.getQuantityM1CG();
        resultDTO.setQuantityM1CG(getAveCellGoodRate(m1CellQuantity, quantityM1CG, cell1Sum));

        //2月平均电池良率
        BigDecimal m2CellQuantity = resultDTO.getM2CellQuantity();
        BigDecimal quantityM2CG = resultDTO.getQuantityM2CG();
        resultDTO.setQuantityM2CG(getAveCellGoodRate(m2CellQuantity, quantityM2CG, cell2Sum));

        //3月平均电池良率
        BigDecimal m3CellQuantity = resultDTO.getM3CellQuantity();
        BigDecimal quantityM3CG = resultDTO.getQuantityM3CG();
        resultDTO.setQuantityM3CG(getAveCellGoodRate(m3CellQuantity, quantityM3CG, cell3Sum));

        //4月平均电池良率
        BigDecimal m4CellQuantity = resultDTO.getM4CellQuantity();
        BigDecimal quantityM4CG = resultDTO.getQuantityM4CG();
        resultDTO.setQuantityM4CG(getAveCellGoodRate(m4CellQuantity, quantityM4CG, cell4Sum));

        //5月平均电池良率
        BigDecimal m5CellQuantity = resultDTO.getM5CellQuantity();
        BigDecimal quantityM5CG = resultDTO.getQuantityM5CG();
        resultDTO.setQuantityM5CG(getAveCellGoodRate(m5CellQuantity, quantityM5CG, cell5Sum));

        //6月平均电池良率
        BigDecimal m6CellQuantity = resultDTO.getM6CellQuantity();
        BigDecimal quantityM6CG = resultDTO.getQuantityM6CG();
        resultDTO.setQuantityM6CG(getAveCellGoodRate(m6CellQuantity, quantityM6CG, cell6Sum));

        //7月平均电池良率
        BigDecimal m7CellQuantity = resultDTO.getM7CellQuantity();
        BigDecimal quantityM7CG = resultDTO.getQuantityM7CG();
        resultDTO.setQuantityM7CG(getAveCellGoodRate(m7CellQuantity, quantityM7CG, cell7Sum));

        //8月平均电池良率
        BigDecimal m8CellQuantity = resultDTO.getM8CellQuantity();
        BigDecimal quantityM8CG = resultDTO.getQuantityM8CG();
        resultDTO.setQuantityM8CG(getAveCellGoodRate(m8CellQuantity, quantityM8CG, cell8Sum));

        //9月平均电池良率
        BigDecimal m9CellQuantity = resultDTO.getM9CellQuantity();
        BigDecimal quantityM9CG = resultDTO.getQuantityM9CG();
        resultDTO.setQuantityM9CG(getAveCellGoodRate(m9CellQuantity, quantityM9CG, cell9Sum));

        //10月平均电池良率
        BigDecimal m10CellQuantity = resultDTO.getM10CellQuantity();
        BigDecimal quantityM10CG = resultDTO.getQuantityM10CG();
        resultDTO.setQuantityM10CG(getAveCellGoodRate(m10CellQuantity, quantityM10CG, cell10Sum));

        //11月平均电池良率
        BigDecimal m11CellQuantity = resultDTO.getM11CellQuantity();
        BigDecimal quantityM11CG = resultDTO.getQuantityM11CG();
        resultDTO.setQuantityM11CG(getAveCellGoodRate(m11CellQuantity, quantityM11CG, cell11Sum));

        //12月平均电池良率
        BigDecimal m12CellQuantity = resultDTO.getM12CellQuantity();
        BigDecimal quantityM12CG = resultDTO.getQuantityM12CG();
        resultDTO.setQuantityM12CG(getAveCellGoodRate(m12CellQuantity, quantityM12CG, cell12Sum));
    }

    private BigDecimal getAveCellGoodRate(BigDecimal mQuantity, BigDecimal quantityMCG, BigDecimal cellSum) {
        return mQuantity.multiply(quantityMCG).divide(cellSum, 6, RoundingMode.HALF_UP);
    }

    private static CellWaferWeightMaintenance getCellWeightMaintenance(CellWaferWeightMaintenanceCalculateResultDTO resultDTO, BigDecimal q1Sum, BigDecimal q2Sum, BigDecimal q3Sum, BigDecimal q4Sum, BigDecimal q5Sum, BigDecimal q6Sum, BigDecimal q7Sum, BigDecimal q8Sum, BigDecimal q9Sum, BigDecimal q10Sum, BigDecimal q11Sum, BigDecimal q12Sum) {

        CellWaferWeightMaintenance cellWaferWeightMaintenance = new CellWaferWeightMaintenance();
        cellWaferWeightMaintenance.setCountryFlag(resultDTO.getCountryFlag());
        cellWaferWeightMaintenance.setCellModel(resultDTO.getCellModel());
        cellWaferWeightMaintenance.setCellShard(resultDTO.getCellShard());
        cellWaferWeightMaintenance.setYear(Integer.valueOf(resultDTO.getYear()));
        cellWaferWeightMaintenance.setProductType(ProductTypeEnum.CELL.getCode());

        //电池片数
        Integer slices = resultDTO.getSlices();
        //(1*100*电池片数/组件良率/组件功率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量
        BigDecimal common = new BigDecimal(1).multiply(new BigDecimal(100)).multiply(new BigDecimal(slices));


        //(组件功率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量
        // 1月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM1 = resultDTO.getQuantityM1();
        BigDecimal m1QuantityPFCPM = resultDTO.getM1QuantityPFCPM();
        BigDecimal q1PercentHV = resultDTO.getQ1PercentHV();
        BigDecimal q1Percent = resultDTO.getQ1Percent();
        BigDecimal m1Quantity = resultDTO.getM1Quantity();
        cellWaferWeightMaintenance.setM1Quantity(getCellWeightResult(q1Sum, common, quantityM1, m1QuantityPFCPM, q1Percent, q1PercentHV, m1Quantity));
        log.info("1月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM1, m1QuantityPFCPM, q1PercentHV, q1Percent, m1Quantity, q1Sum);
        cellWaferWeightMaintenance.setAveragePower1(getAveragePower(q1Sum, m1QuantityPFCPM, q1Percent, q1PercentHV, m1Quantity));





        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM2 = resultDTO.getQuantityM2();
        BigDecimal m2QuantityPFCPM = resultDTO.getM2QuantityPFCPM();
        BigDecimal q2PercentHV = resultDTO.getQ2PercentHV();
        BigDecimal q2Percent = resultDTO.getQ2Percent();
        BigDecimal m2Quantity = resultDTO.getM2Quantity();
        cellWaferWeightMaintenance.setM2Quantity(getCellWeightResult(q2Sum, common, quantityM2, m2QuantityPFCPM, q2Percent, q2PercentHV, m2Quantity));
        log.info("2月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM2, m2QuantityPFCPM, q2PercentHV, q2Percent, m2Quantity, q2Sum);
        cellWaferWeightMaintenance.setAveragePower2(getAveragePower(q2Sum, m2QuantityPFCPM, q2Percent, q2PercentHV, m2Quantity));
        // 3月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM3 = resultDTO.getQuantityM3();
        BigDecimal m3QuantityPFCPM = resultDTO.getM3QuantityPFCPM();
        BigDecimal q3PercentHV = resultDTO.getQ3PercentHV();
        BigDecimal q3Percent = resultDTO.getQ3Percent();
        BigDecimal m3Quantity = resultDTO.getM3Quantity();
        cellWaferWeightMaintenance.setM3Quantity(getCellWeightResult(q3Sum, common, quantityM3, m3QuantityPFCPM, q3Percent, q3PercentHV, m3Quantity));
        log.info("3月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM3, m3QuantityPFCPM, q3PercentHV, q3Percent, m3Quantity, q3Sum);
        cellWaferWeightMaintenance.setAveragePower3(getAveragePower(q3Sum, m3QuantityPFCPM, q3Percent, q3PercentHV, m3Quantity));
        // 4月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM4 = resultDTO.getQuantityM4();
        BigDecimal m4QuantityPFCPM = resultDTO.getM4QuantityPFCPM();
        BigDecimal q4PercentHV = resultDTO.getQ4PercentHV();
        BigDecimal q4Percent = resultDTO.getQ4Percent();
        BigDecimal m4Quantity = resultDTO.getM4Quantity();
        cellWaferWeightMaintenance.setM4Quantity(getCellWeightResult(q4Sum, common, quantityM4, m4QuantityPFCPM, q4Percent, q4PercentHV, m4Quantity));
        log.info("4月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM4, m4QuantityPFCPM, q4PercentHV, q4Percent, m4Quantity, q4Sum);
        cellWaferWeightMaintenance.setAveragePower4(getAveragePower(q4Sum, m4QuantityPFCPM, q4Percent, q4PercentHV, m4Quantity));
        // 5月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM5 = resultDTO.getQuantityM5();
        BigDecimal m5QuantityPFCPM = resultDTO.getM5QuantityPFCPM();
        BigDecimal q5PercentHV = resultDTO.getQ5PercentHV();
        BigDecimal q5Percent = resultDTO.getQ5Percent();
        BigDecimal m5Quantity = resultDTO.getM5Quantity();
        cellWaferWeightMaintenance.setM5Quantity(getCellWeightResult(q5Sum, common, quantityM5, m5QuantityPFCPM, q5Percent, q5PercentHV, m5Quantity));
        log.info("5月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM5, m5QuantityPFCPM, q5PercentHV, q5Percent, m5Quantity, q5Sum);
        cellWaferWeightMaintenance.setAveragePower5(getAveragePower(q5Sum, m5QuantityPFCPM, q5Percent, q5PercentHV, m5Quantity));
        // 6月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM6 = resultDTO.getQuantityM6();
        BigDecimal m6QuantityPFCPM = resultDTO.getM6QuantityPFCPM();
        BigDecimal q6PercentHV = resultDTO.getQ6PercentHV();
        BigDecimal q6Percent = resultDTO.getQ6Percent();
        BigDecimal m6Quantity = resultDTO.getM6Quantity();
        cellWaferWeightMaintenance.setM6Quantity(getCellWeightResult(q6Sum, common, quantityM6, m6QuantityPFCPM, q6Percent, q6PercentHV, m6Quantity));
        log.info("6月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM6, m6QuantityPFCPM, q6PercentHV, q6Percent, m6Quantity, q6Sum);
        cellWaferWeightMaintenance.setAveragePower6(getAveragePower(q6Sum, m6QuantityPFCPM, q6Percent, q6PercentHV, m6Quantity));

        // 7月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM7 = resultDTO.getQuantityM7();
        BigDecimal m7QuantityPFCPM = resultDTO.getM7QuantityPFCPM();
        BigDecimal q7PercentHV = resultDTO.getQ7PercentHV();
        BigDecimal q7Percent = resultDTO.getQ7Percent();
        BigDecimal m7Quantity = resultDTO.getM7Quantity();
        cellWaferWeightMaintenance.setM7Quantity(getCellWeightResult(q7Sum, common, quantityM7, m7QuantityPFCPM, q7Percent, q7PercentHV, m7Quantity));
        log.info("7月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM7, m7QuantityPFCPM, q7PercentHV, q7Percent, m7Quantity, q7Sum);
        cellWaferWeightMaintenance.setAveragePower7(getAveragePower(q7Sum, m7QuantityPFCPM, q7Percent, q7PercentHV, m7Quantity));

        // 8月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM8 = resultDTO.getQuantityM8();
        BigDecimal m8QuantityPFCPM = resultDTO.getM8QuantityPFCPM();
        BigDecimal q8PercentHV = resultDTO.getQ8PercentHV();
        BigDecimal q8Percent = resultDTO.getQ8Percent();
        BigDecimal m8Quantity = resultDTO.getM8Quantity();
        cellWaferWeightMaintenance.setM8Quantity(getCellWeightResult(q8Sum, common, quantityM8, m8QuantityPFCPM, q8Percent, q8PercentHV, m8Quantity));
        log.info("8月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM8, m8QuantityPFCPM, q8PercentHV, q8Percent, m8Quantity, q8Sum);
        cellWaferWeightMaintenance.setAveragePower8(getAveragePower(q8Sum, m8QuantityPFCPM, q8Percent, q8PercentHV, m8Quantity));

        // 9月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM9 = resultDTO.getQuantityM9();
        BigDecimal m9QuantityPFCPM = resultDTO.getM9QuantityPFCPM();
        BigDecimal q9PercentHV = resultDTO.getQ9PercentHV();
        BigDecimal q9Percent = resultDTO.getQ9Percent();
        BigDecimal m9Quantity = resultDTO.getM9Quantity();
        cellWaferWeightMaintenance.setM9Quantity(getCellWeightResult(q9Sum, common, quantityM9, m9QuantityPFCPM, q9Percent, q9PercentHV, m9Quantity));
        log.info("9月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM9, m9QuantityPFCPM, q9PercentHV, q9Percent, m9Quantity, q9Sum);
        cellWaferWeightMaintenance.setAveragePower9(getAveragePower(q9Sum, m9QuantityPFCPM, q9Percent, q9PercentHV, m9Quantity));

        // 10月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM10 = resultDTO.getQuantityM10();
        BigDecimal m10QuantityPFCPM = resultDTO.getM10QuantityPFCPM();
        BigDecimal q10PercentHV = resultDTO.getQ10PercentHV();
        BigDecimal q10Percent = resultDTO.getQ10Percent();
        BigDecimal m10Quantity = resultDTO.getM10Quantity();
        cellWaferWeightMaintenance.setM10Quantity(getCellWeightResult(q10Sum, common, quantityM10, m10QuantityPFCPM, q10Percent, q10PercentHV, m10Quantity));
        log.info("10月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM10, m10QuantityPFCPM, q10PercentHV, q10Percent, m10Quantity, q10Sum);
        cellWaferWeightMaintenance.setAveragePower10(getAveragePower(q10Sum, m10QuantityPFCPM, q10Percent, q10PercentHV, m10Quantity));

        // 11月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM11 = resultDTO.getQuantityM11();
        BigDecimal m11QuantityPFCPM = resultDTO.getM11QuantityPFCPM();
        BigDecimal q11PercentHV = resultDTO.getQ11PercentHV();
        BigDecimal q11Percent = resultDTO.getQ11Percent();
        BigDecimal m11Quantity = resultDTO.getM11Quantity();
        cellWaferWeightMaintenance.setM11Quantity(getCellWeightResult(q11Sum, common, quantityM11, m11QuantityPFCPM, q11Percent, q11PercentHV, m11Quantity));
        log.info("11月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM11, m11QuantityPFCPM, q11PercentHV, q11Percent, m11Quantity, q11Sum);
        cellWaferWeightMaintenance.setAveragePower11(getAveragePower(q11Sum, m11QuantityPFCPM, q11Percent, q11PercentHV, m11Quantity));

        // 12月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM12 = resultDTO.getQuantityM12();
        BigDecimal m12QuantityPFCPM = resultDTO.getM12QuantityPFCPM();
        BigDecimal q12PercentHV = resultDTO.getQ12PercentHV();
        BigDecimal q12Percent = resultDTO.getQ12Percent();
        BigDecimal m12Quantity = resultDTO.getM12Quantity();
        cellWaferWeightMaintenance.setM12Quantity(getCellWeightResult(q12Sum, common, quantityM12, m12QuantityPFCPM, q12Percent, q12PercentHV, m12Quantity));
        log.info("12月-计算参数-组件良率{{}}-组件功率{{}}-横竖装比例{{}}-产品族比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM12, m12QuantityPFCPM, q12PercentHV, q12Percent, m12Quantity, q12Sum);
        cellWaferWeightMaintenance.setAveragePower12(getAveragePower(q12Sum, m12QuantityPFCPM, q12Percent, q12PercentHV, m12Quantity));
        return cellWaferWeightMaintenance;
    }

    private static BigDecimal getCellWeightResult(BigDecimal qSum, BigDecimal common, BigDecimal quantityM, BigDecimal mQuantityPFCPM, BigDecimal qPercent, BigDecimal qPercentHV, BigDecimal mQuantity) {
        //计算公式
        //电池MW折算系数
        //（按电池型号+分片方式、月份维度）
        //各电池产品=
        //(1*100*电池片数/组件良率(产品族+产品系列)/组件功率（产品族+横竖装）) * (产品族比例*横竖装比例*组件排产量）/汇总组件排产量
        if (BigDecimal.ZERO.compareTo(qSum) == 0 || BigDecimal.ZERO.compareTo(quantityM) == 0 || BigDecimal.ZERO.compareTo(mQuantityPFCPM) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal one = common.divide(quantityM, 6, RoundingMode.HALF_UP).divide(mQuantityPFCPM, 6, RoundingMode.HALF_UP);
        BigDecimal two = qPercent.multiply(qPercentHV).multiply(mQuantity);
        return one.multiply(two).divide(qSum, 6, RoundingMode.HALF_UP);
    }

    private static BigDecimal getAveragePower(BigDecimal qSum,  BigDecimal mQuantityPFCPM, BigDecimal qPercent, BigDecimal qPercentHV, BigDecimal mQuantity) {
        //计算公式

        //(组件功率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量
        if (BigDecimal.ZERO.compareTo(qSum) == 0 || BigDecimal.ZERO.compareTo(mQuantityPFCPM) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal two = qPercent.multiply(qPercentHV).multiply(mQuantity);
        return mQuantityPFCPM.multiply(two).divide(qSum, 6, RoundingMode.HALF_UP);
    }



    private CellWaferWeightMaintenance getWaferWeightMaintenance(CellWaferWeightMaintenanceCalculateResultDTO resultDTO, BigDecimal q1Sum, BigDecimal q2Sum, BigDecimal q3Sum, BigDecimal q4Sum, BigDecimal q5Sum, BigDecimal q6Sum, BigDecimal q7Sum, BigDecimal q8Sum, BigDecimal q9Sum, BigDecimal q10Sum, BigDecimal q11Sum, BigDecimal q12Sum) {
        //每月电池良率
        BigDecimal  quantityM1CG = resultDTO.getQuantityM1CG();
        BigDecimal quantityM2CG = resultDTO.getQuantityM2CG();
        BigDecimal quantityM3CG = resultDTO.getQuantityM3CG();
        BigDecimal quantityM4CG = resultDTO.getQuantityM4CG();
        BigDecimal quantityM5CG = resultDTO.getQuantityM5CG();
        BigDecimal quantityM6CG = resultDTO.getQuantityM6CG();
        BigDecimal quantityM7CG = resultDTO.getQuantityM7CG();
        BigDecimal quantityM8CG = resultDTO.getQuantityM8CG();
        BigDecimal quantityM9CG = resultDTO.getQuantityM9CG();
        BigDecimal quantityM10CG = resultDTO.getQuantityM10CG();
        BigDecimal quantityM11CG = resultDTO.getQuantityM11CG();
        BigDecimal quantityM12CG = resultDTO.getQuantityM12CG();

        CellWaferWeightMaintenance cellWaferWeightMaintenance = new CellWaferWeightMaintenance();
        cellWaferWeightMaintenance.setCountryFlag(resultDTO.getCountryFlag());
        cellWaferWeightMaintenance.setCellModel(resultDTO.getWaferModel());
        cellWaferWeightMaintenance.setCellShard(resultDTO.getCellShard());
        cellWaferWeightMaintenance.setYear(Integer.valueOf(resultDTO.getYear()));
        cellWaferWeightMaintenance.setProductType(ProductTypeEnum.WAFER.getCode());

        //电池片数
        Integer slices = resultDTO.getSlices();
        log.info(String.format("计算参数-电池片数{%s}", slices));
        //(1*100*电池片数/组件良率/组件功率/平均电池良率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量
        BigDecimal common = new BigDecimal(1).multiply(new BigDecimal(100)).multiply(new BigDecimal(slices));

        // 1月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM1 = resultDTO.getQuantityM1();
        BigDecimal m1QuantityPFCPM = resultDTO.getM1QuantityPFCPM();
        BigDecimal q1PercentHV = resultDTO.getQ1PercentHV();
        BigDecimal q1Percent = resultDTO.getQ1Percent();
        BigDecimal m1Quantity = resultDTO.getM1Quantity();
        log.info("1月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM1, m1QuantityPFCPM, quantityM1CG, q1Percent, q1PercentHV, m1Quantity, q1Sum);

        cellWaferWeightMaintenance.setM1Quantity(getWaterWeightResult(q1Sum, common, quantityM1, m1QuantityPFCPM, q1Percent, q1PercentHV, m1Quantity, quantityM1CG));
        cellWaferWeightMaintenance.setAverageYieldRate1(quantityM1CG);
        // 2月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM2 = resultDTO.getQuantityM2();
        BigDecimal m2QuantityPFCPM = resultDTO.getM2QuantityPFCPM();
        BigDecimal q2PercentHV = resultDTO.getQ2PercentHV();
        BigDecimal q2Percent = resultDTO.getQ2Percent();
        BigDecimal m2Quantity = resultDTO.getM2Quantity();
        log.info("2月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM2, m2QuantityPFCPM, quantityM2CG, q2Percent, q2PercentHV, m2Quantity, q2Sum);
        cellWaferWeightMaintenance.setM2Quantity(getWaterWeightResult(q2Sum, common, quantityM2, m2QuantityPFCPM, q2Percent, q2PercentHV, m2Quantity, quantityM2CG));
        cellWaferWeightMaintenance.setAverageYieldRate2(quantityM2CG);
        // 3月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM3 = resultDTO.getQuantityM3();
        BigDecimal m3QuantityPFCPM = resultDTO.getM3QuantityPFCPM();
        BigDecimal q3PercentHV = resultDTO.getQ3PercentHV();
        BigDecimal q3Percent = resultDTO.getQ3Percent();
        BigDecimal m3Quantity = resultDTO.getM3Quantity();
        log.info("3月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM3, m3QuantityPFCPM, quantityM3CG, q3Percent, q3PercentHV, m3Quantity, q3Sum);
        cellWaferWeightMaintenance.setM3Quantity(getWaterWeightResult(q3Sum, common, quantityM3, m3QuantityPFCPM, q3Percent, q3PercentHV, m3Quantity, quantityM3CG));
        cellWaferWeightMaintenance.setAverageYieldRate3(quantityM3CG);
        // 4月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM4 = resultDTO.getQuantityM4();
        BigDecimal m4QuantityPFCPM = resultDTO.getM4QuantityPFCPM();
        BigDecimal q4PercentHV = resultDTO.getQ4PercentHV();
        BigDecimal q4Percent = resultDTO.getQ4Percent();
        BigDecimal m4Quantity = resultDTO.getM4Quantity();
        log.info("4月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM4, m4QuantityPFCPM, quantityM4CG, q4Percent, q4PercentHV, m4Quantity, q4Sum);
        cellWaferWeightMaintenance.setM4Quantity(getWaterWeightResult(q4Sum, common, quantityM4, m4QuantityPFCPM, q4Percent, q4PercentHV, m4Quantity, quantityM4CG));
        cellWaferWeightMaintenance.setAverageYieldRate4(quantityM4CG);
        // 5月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM5 = resultDTO.getQuantityM5();
        BigDecimal m5QuantityPFCPM = resultDTO.getM5QuantityPFCPM();
        BigDecimal q5PercentHV = resultDTO.getQ5PercentHV();
        BigDecimal q5Percent = resultDTO.getQ5Percent();
        BigDecimal m5Quantity = resultDTO.getM5Quantity();
        log.info("5月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM5, m5QuantityPFCPM, quantityM5CG, q5Percent, q5PercentHV, m5Quantity, q5Sum);
        cellWaferWeightMaintenance.setM5Quantity(getWaterWeightResult(q5Sum, common, quantityM5, m5QuantityPFCPM, q5Percent, q5PercentHV, m5Quantity, quantityM5CG));
        cellWaferWeightMaintenance.setAverageYieldRate5(quantityM5CG);
        // 6月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM6 = resultDTO.getQuantityM6();
        BigDecimal m6QuantityPFCPM = resultDTO.getM6QuantityPFCPM();
        BigDecimal q6PercentHV = resultDTO.getQ6PercentHV();
        BigDecimal q6Percent = resultDTO.getQ6Percent();
        BigDecimal m6Quantity = resultDTO.getM6Quantity();
        log.info("6月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM6, m6QuantityPFCPM, quantityM6CG, q6Percent, q6PercentHV, m6Quantity, q6Sum);
        cellWaferWeightMaintenance.setM6Quantity(getWaterWeightResult(q6Sum, common, quantityM6, m6QuantityPFCPM, q6Percent, q6PercentHV, m6Quantity, quantityM6CG));
        cellWaferWeightMaintenance.setAverageYieldRate6(quantityM6CG);
        // 7月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM7 = resultDTO.getQuantityM7();
        BigDecimal m7QuantityPFCPM = resultDTO.getM7QuantityPFCPM();
        BigDecimal q7PercentHV = resultDTO.getQ7PercentHV();
        BigDecimal q7Percent = resultDTO.getQ7Percent();
        BigDecimal m7Quantity = resultDTO.getM7Quantity();
        log.info("7月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM7, m7QuantityPFCPM, quantityM7CG, q7Percent, q7PercentHV, m7Quantity, q7Sum);
        cellWaferWeightMaintenance.setM7Quantity(getWaterWeightResult(q7Sum, common, quantityM7, m7QuantityPFCPM, q7Percent, q7PercentHV, m7Quantity, quantityM7CG));
        cellWaferWeightMaintenance.setAverageYieldRate7(quantityM7CG);
        // 8月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM8 = resultDTO.getQuantityM8();
        BigDecimal m8QuantityPFCPM = resultDTO.getM8QuantityPFCPM();
        BigDecimal q8PercentHV = resultDTO.getQ8PercentHV();
        BigDecimal q8Percent = resultDTO.getQ8Percent();
        BigDecimal m8Quantity = resultDTO.getM8Quantity();
        log.info("8月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM8, m8QuantityPFCPM, quantityM8CG, q8Percent, q8PercentHV, m8Quantity, q8Sum);
        cellWaferWeightMaintenance.setM8Quantity(getWaterWeightResult(q8Sum, common, quantityM8, m8QuantityPFCPM, q8Percent, q8PercentHV, m8Quantity, quantityM8CG));
        cellWaferWeightMaintenance.setAverageYieldRate8(quantityM8CG);
        // 9月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM9 = resultDTO.getQuantityM9();
        BigDecimal m9QuantityPFCPM = resultDTO.getM9QuantityPFCPM();
        BigDecimal q9PercentHV = resultDTO.getQ9PercentHV();
        BigDecimal q9Percent = resultDTO.getQ9Percent();
        BigDecimal m9Quantity = resultDTO.getM9Quantity();
        log.info("9月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM9, m9QuantityPFCPM, quantityM9CG, q9Percent, q9PercentHV, m9Quantity, q9Sum);
        cellWaferWeightMaintenance.setM9Quantity(getWaterWeightResult(q9Sum, common, quantityM9, m9QuantityPFCPM, q9Percent, q9PercentHV, m9Quantity, quantityM9CG));
        cellWaferWeightMaintenance.setAverageYieldRate9(quantityM9CG);
        // 10月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM10 = resultDTO.getQuantityM10();
        BigDecimal m10QuantityPFCPM = resultDTO.getM10QuantityPFCPM();
        BigDecimal q10PercentHV = resultDTO.getQ10PercentHV();
        BigDecimal q10Percent = resultDTO.getQ10Percent();
        BigDecimal m10Quantity = resultDTO.getM10Quantity();
        log.info("10月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM10, m10QuantityPFCPM, quantityM10CG, q10Percent, q10PercentHV, m10Quantity, q10Sum);
        cellWaferWeightMaintenance.setM10Quantity(getWaterWeightResult(q10Sum, common, quantityM10, m10QuantityPFCPM, q10Percent, q10PercentHV, m10Quantity, quantityM10CG));
        cellWaferWeightMaintenance.setAverageYieldRate10(quantityM10CG);
        // 11月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM11 = resultDTO.getQuantityM11();
        BigDecimal m11QuantityPFCPM = resultDTO.getM11QuantityPFCPM();
        BigDecimal q11PercentHV = resultDTO.getQ11PercentHV();
        BigDecimal q11Percent = resultDTO.getQ11Percent();
        BigDecimal m11Quantity = resultDTO.getM11Quantity();
        log.info("11月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM11, m11QuantityPFCPM, quantityM11CG, q11Percent, q11PercentHV, m11Quantity, q11Sum);
        cellWaferWeightMaintenance.setM11Quantity(getWaterWeightResult(q11Sum, common, quantityM11, m11QuantityPFCPM, q11Percent, q11PercentHV, m11Quantity, quantityM11CG));
        cellWaferWeightMaintenance.setAverageYieldRate11(quantityM11CG);
        // 12月计算
        //组件良率 + 组件功率 + 横竖装比例 + 产品族比例 + 排产量
        BigDecimal quantityM12 = resultDTO.getQuantityM12();
        BigDecimal m12QuantityPFCPM = resultDTO.getM12QuantityPFCPM();
        BigDecimal q12PercentHV = resultDTO.getQ12PercentHV();
        BigDecimal q12Percent = resultDTO.getQ12Percent();
        BigDecimal m12Quantity = resultDTO.getM12Quantity();
        log.info("12月-计算参数-组件良率{{}}-组件功率{{}}-平均电池良率{{}}-产品系列系数{{}}-横竖装比例{{}}-组件排产量{{}}-汇总组件排产量{{}}", quantityM12, m12QuantityPFCPM, quantityM12CG, q12Percent, q12PercentHV, m12Quantity, q12Sum);
        cellWaferWeightMaintenance.setM12Quantity(getWaterWeightResult(q12Sum, common, quantityM12, m12QuantityPFCPM, q12Percent, q12PercentHV, m12Quantity, quantityM12CG));
        cellWaferWeightMaintenance.setAverageYieldRate12(quantityM12CG);
        return cellWaferWeightMaintenance;
    }

    private static BigDecimal getWaterWeightResult(BigDecimal qSum, BigDecimal common, BigDecimal quantityM, BigDecimal mQuantityPFCPM, BigDecimal qPercent, BigDecimal qPercentHV, BigDecimal mQuantity, BigDecimal quantityMCG) {
        //硅片MW折算系数
        //（按电池型号、月份维度）
        //            各电池产品=
        //                    (1*100*电池片数/组件良率/组件功率/平均电池良率) * (产品系列系数*横竖装比例*组件排产量）/汇总组件排产量
        if (BigDecimal.ZERO.compareTo(qSum) == 0 || BigDecimal.ZERO.compareTo(quantityM) == 0 || BigDecimal.ZERO.compareTo(mQuantityPFCPM) == 0 || BigDecimal.ZERO.compareTo(quantityMCG) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal one = common.divide(quantityM, 6, RoundingMode.HALF_UP).divide(mQuantityPFCPM, 6, RoundingMode.HALF_UP).divide(quantityMCG, 6, RoundingMode.HALF_UP);
        BigDecimal two = qPercent.multiply(qPercentHV).multiply(mQuantity);
        return one.multiply(two).divide(qSum, 6, RoundingMode.HALF_UP);
    }

    private List<CellDistributionDTO> getCellDistributionDto(String dataVersion) {
        QCellDistribution qCellDistribution = QCellDistribution.cellDistribution;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<CellDistributionDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(CellDistributionDTO.class, qCellDistribution.id.as("id"), qCellDistribution.countryFlag.as("countryFlag"), qCellDistribution.calculateVersion.as("calculateVersion"), qCellDistribution.workshop.as("workshop"), qCellDistribution.productSeries.as("productSeries"), qCellDistribution.cellShard.as("cellShard"), qCellDistribution.year.as("year"), qCellDistribution.basePlace.as("basePlace"), qCellDistribution.m1Quantity.as("m1Quantity"), qCellDistribution.m2Quantity.as("m2Quantity"), qCellDistribution.m3Quantity.as("m3Quantity"), qCellDistribution.m4Quantity.as("m4Quantity"), qCellDistribution.m5Quantity.as("m5Quantity"), qCellDistribution.m6Quantity.as("m6Quantity"), qCellDistribution.m7Quantity.as("m7Quantity"), qCellDistribution.m8Quantity.as("m8Quantity"), qCellDistribution.m9Quantity.as("m9Quantity"), qCellDistribution.m10Quantity.as("m10Quantity"), qCellDistribution.m11Quantity.as("m11Quantity"), qCellDistribution.m12Quantity.as("m12Quantity"))).from(qCellDistribution);
        jpaQuery.where(qCellDistribution.calculateVersion.eq(dataVersion));
        jpaQuery.where(qCellDistribution.isDeleted.eq(DeleteEnum.NO.getCode()));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ProductFamilyCalculationPowerMaintenanceDTO> getProductFamilyCalculationPowerMaintenanceDTO(List<String> productGroupList, List<String> allYears) {
        QProductFamilyCalculationPowerMaintenance qProductFamilyCalculationPowerMaintenance = QProductFamilyCalculationPowerMaintenance.productFamilyCalculationPowerMaintenance;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ProductFamilyCalculationPowerMaintenanceDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ProductFamilyCalculationPowerMaintenanceDTO.class, qProductFamilyCalculationPowerMaintenance.id.as("id"), qProductFamilyCalculationPowerMaintenance.countryFlag.as("countryFlag"), qProductFamilyCalculationPowerMaintenance.productFamily.as("productFamily"), qProductFamilyCalculationPowerMaintenance.installType.as("installType"), qProductFamilyCalculationPowerMaintenance.year.as("year"), qProductFamilyCalculationPowerMaintenance.m1Quantity.as("m1Quantity"), qProductFamilyCalculationPowerMaintenance.m2Quantity.as("m2Quantity"), qProductFamilyCalculationPowerMaintenance.m3Quantity.as("m3Quantity"), qProductFamilyCalculationPowerMaintenance.m4Quantity.as("m4Quantity"), qProductFamilyCalculationPowerMaintenance.m5Quantity.as("m5Quantity"), qProductFamilyCalculationPowerMaintenance.m6Quantity.as("m6Quantity"), qProductFamilyCalculationPowerMaintenance.m7Quantity.as("m7Quantity"), qProductFamilyCalculationPowerMaintenance.m8Quantity.as("m8Quantity"), qProductFamilyCalculationPowerMaintenance.m9Quantity.as("m9Quantity"), qProductFamilyCalculationPowerMaintenance.m10Quantity.as("m10Quantity"), qProductFamilyCalculationPowerMaintenance.m11Quantity.as("m11Quantity"), qProductFamilyCalculationPowerMaintenance.m12Quantity.as("m12Quantity"))).from(qProductFamilyCalculationPowerMaintenance);
        jpaQuery.where(qProductFamilyCalculationPowerMaintenance.productFamily.in(productGroupList));
        jpaQuery.where(qProductFamilyCalculationPowerMaintenance.isDeleted.eq(DeleteEnum.NO.getCode()));
        jpaQuery.where(qProductFamilyCalculationPowerMaintenance.year.in(allYears.stream().map(Integer::valueOf).collect(Collectors.toList())));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ConfigCellGoodDTO> getConfigCellGoodDTO(List<String> allYears) {
        QConfigCellGood qConfigCellGood = QConfigCellGood.configCellGood;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ConfigCellGoodDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ConfigCellGoodDTO.class, qConfigCellGood.id.as("id"), qConfigCellGood.isOversea.as("isOversea"), qConfigCellGood.year.as("year"), qConfigCellGood.batchNo.as("batchNo"), qConfigCellGood.groupId.as("groupId"), qConfigCellGood.parameterType.as("parameterType"), qConfigCellGood.productType.as("productType"), qConfigCellGood.productCategory.as("productCategory"), qConfigCellGood.mainGrid.as("mainGrid"), qConfigCellGood.crystalType.as("crystalType"), qConfigCellGood.crystalSpec.as("crystalSpec"), qConfigCellGood.cellHigh.as("cellHigh"), qConfigCellGood.highUnit.as("highUnit"), qConfigCellGood.workshop.as("workshop"), qConfigCellGood.indexType.as("indexType"), qConfigCellGood.fragmentType.as("fragmentType"),qConfigCellGood.quantityM1.as("quantityM1"), qConfigCellGood.quantityM2.as("quantityM2"), qConfigCellGood.quantityM3.as("quantityM3"), qConfigCellGood.quantityM4.as("quantityM4"), qConfigCellGood.quantityM5.as("quantityM5"), qConfigCellGood.quantityM6.as("quantityM6"), qConfigCellGood.quantityM7.as("quantityM7"), qConfigCellGood.quantityM8.as("quantityM8"), qConfigCellGood.quantityM9.as("quantityM9"), qConfigCellGood.quantityM10.as("quantityM10"), qConfigCellGood.quantityM11.as("quantityM11"), qConfigCellGood.quantityM12.as("quantityM12"))).from(qConfigCellGood);
        if (CollectionUtils.isNotEmpty(allYears)) {
            jpaQuery.where(qConfigCellGood.year.in(allYears));
        }
        jpaQuery.where(qConfigCellGood.parameterType.in("10"));
        jpaQuery.where(qConfigCellGood.isDeleted.eq(DeleteEnum.NO.getCode()));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ConfigModuleGoodDTO> getConfigModuleGoodDTO(List<String> allYears) {
        QConfigModuleGood qConfigModuleGood = QConfigModuleGood.configModuleGood;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ConfigModuleGoodDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ConfigModuleGoodDTO.class, qConfigModuleGood.id.as("id"), qConfigModuleGood.isOversea.as("isOversea"), qConfigModuleGood.year.as("year"), qConfigModuleGood.productSeries.as("productSeries"), qConfigModuleGood.batchNo.as("batchNo"), qConfigModuleGood.groupId.as("groupId"), qConfigModuleGood.parameterType.as("parameterType"), qConfigModuleGood.crystalSpec.as("crystalSpec"), qConfigModuleGood.productFamily.as("productFamily"), qConfigModuleGood.workshop.as("workshop"), qConfigModuleGood.indexType.as("indexType"), qConfigModuleGood.quantityM1.subtract(1).multiply(-1).as("quantityM1"), qConfigModuleGood.quantityM2.subtract(1).multiply(-1).as("quantityM2"), qConfigModuleGood.quantityM3.subtract(1).multiply(-1).as("quantityM3"), qConfigModuleGood.quantityM4.subtract(1).multiply(-1).as("quantityM4"), qConfigModuleGood.quantityM5.subtract(1).multiply(-1).as("quantityM5"), qConfigModuleGood.quantityM6.subtract(1).multiply(-1).as("quantityM6"), qConfigModuleGood.quantityM7.subtract(1).multiply(-1).as("quantityM7"), qConfigModuleGood.quantityM8.subtract(1).multiply(-1).as("quantityM8"), qConfigModuleGood.quantityM9.subtract(1).multiply(-1).as("quantityM9"), qConfigModuleGood.quantityM10.subtract(1).multiply(-1).as("quantityM10"), qConfigModuleGood.quantityM11.subtract(1).multiply(-1).as("quantityM11"), qConfigModuleGood.quantityM12.subtract(1).multiply(-1).as("quantityM12"))).from(qConfigModuleGood);
        jpaQuery.where(qConfigModuleGood.indexType.eq(IndexTypeEnum.ORIGINAL_LOSS.getCode()));
        jpaQuery.where(qConfigModuleGood.isDeleted.eq(DeleteEnum.NO.getCode()));
        jpaQuery.where(qConfigModuleGood.year.in(allYears));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ProductInfoDTO> getProductInfoDTO(List<String> allProductSeries) {
        QProductInfo qProductInfo = QProductInfo.productInfo;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ProductInfoDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ProductInfoDTO.class, qProductInfo.id.as("id"), qProductInfo.productGroup.as("productGroup"), qProductInfo.productSeries.as("productSeries"), qProductInfo.cellModel.as("cellModel"), qProductInfo.waferModel.as("waferModel"), qProductInfo.cellShard.as("cellShard"), qProductInfo.componentType.as("componentType"), qProductInfo.productType.as("productType"), qProductInfo.flag.as("flag"))).from(qProductInfo);
        jpaQuery.where(qProductInfo.productSeries.in(allProductSeries));
        jpaQuery.where(qProductInfo.isDeleted.eq(DeleteEnum.NO.getCode()));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ProductGroupPercentDTO> getProductGroupPercentDTO(String dataVersion, List<String> countryFlagList, List<String> productSerieslist) {
        QProductGroupPercent qProductGroupPercent = QProductGroupPercent.productGroupPercent;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ProductGroupPercentDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ProductGroupPercentDTO.class, qProductGroupPercent.id.as("id"), qProductGroupPercent.countryFlag.as("countryFlag"), qProductGroupPercent.calculateVersion.as("calculateVersion"), qProductGroupPercent.productSeries.as("productSeries"), qProductGroupPercent.productGroup.as("productGroup"), qProductGroupPercent.year.as("year"), qProductGroupPercent.q1Percent.as("q1Percent"), qProductGroupPercent.q2Percent.as("q2Percent"), qProductGroupPercent.q3Percent.as("q3Percent"), qProductGroupPercent.q4Percent.as("q4Percent"))).from(qProductGroupPercent);
        jpaQuery.where(qProductGroupPercent.calculateVersion.eq(dataVersion));
        jpaQuery.where(qProductGroupPercent.countryFlag.in(countryFlagList));
        jpaQuery.where(qProductGroupPercent.productSeries.in(productSerieslist));
        jpaQuery.where(qProductGroupPercent.isDeleted.eq(DeleteEnum.NO.getCode()));
        return IterableUtils.toList(jpaQuery.fetch());
    }

    private List<ImportActualCapacityDTO> getImportActualCapacityDto(String dataVersion) {
        QImportActualCapacity qImportActualCapacity = QImportActualCapacity.importActualCapacity;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ImportActualCapacityDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ImportActualCapacityDTO.class, qImportActualCapacity.id.as("id"), qImportActualCapacity.countryFlag.as("countryFlag"), qImportActualCapacity.calculateVersion.as("calculateVersion"), qImportActualCapacity.productType.as("productType"), qImportActualCapacity.workshop.as("workshop"), qImportActualCapacity.productSeries.as("productSeries"), qImportActualCapacity.productGroup.as("productGroup"), qImportActualCapacity.year.as("year"), qImportActualCapacity.basePlace.as("basePlace"), qImportActualCapacity.m1Quantity.as("m1Quantity"), qImportActualCapacity.m2Quantity.as("m2Quantity"), qImportActualCapacity.m3Quantity.as("m3Quantity"), qImportActualCapacity.m4Quantity.as("m4Quantity"), qImportActualCapacity.m5Quantity.as("m5Quantity"), qImportActualCapacity.m6Quantity.as("m6Quantity"), qImportActualCapacity.m7Quantity.as("m7Quantity"), qImportActualCapacity.m8Quantity.as("m8Quantity"), qImportActualCapacity.m9Quantity.as("m9Quantity"), qImportActualCapacity.m10Quantity.as("m10Quantity"), qImportActualCapacity.m11Quantity.as("m11Quantity"), qImportActualCapacity.m12Quantity.as("m12Quantity"), qImportActualCapacity.productFrom.as("productFrom"))).from(qImportActualCapacity);
        jpaQuery.where(qImportActualCapacity.calculateVersion.eq(dataVersion));
        jpaQuery.where(qImportActualCapacity.productType.in(Collections.singletonList(ProductTypeEnum.MODULE.getCode())));
        jpaQuery.where(qImportActualCapacity.productFrom.eq(ProductFromEnum.SELF_PRODUCTION.getCode()));
        jpaQuery.where(qImportActualCapacity.isDeleted.eq(DeleteEnum.NO.getCode()));
        return IterableUtils.toList(jpaQuery.fetch());
    }
}
