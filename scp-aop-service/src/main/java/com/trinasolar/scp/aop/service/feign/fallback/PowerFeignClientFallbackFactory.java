package com.trinasolar.scp.aop.service.feign.fallback;

import com.alibaba.fastjson.JSONObject;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.service.feign.client.PowerFeignClient;
import com.trinasolar.scp.common.api.util.Results;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/1/5
 */
@Component
@Slf4j
public class PowerFeignClientFallbackFactory implements FallbackFactory<PowerFeignClient> {
    @Override
    public PowerFeignClient create(Throwable ex) {
        return new PowerFeignClient() {
            @Override
            public ResponseEntity<Results<PowerSupplyAopDTO>> saveAll(List<PowerSupplyAopSaveDTO> saveDTOs) {
                log.error("【PowerFeign-saveAll】发生异常：{}", ex.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MwCoefficientDTO>>> allList() {
                log.error("【PowerFeign-allList】发生异常：{}", ex.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<JSONObject>> getTargetEfficiencyPage(TargetEfficiencyQuery query) {
                log.error("【PowerFeign-queryTargetEfficiencyPage】发生异常：{}", ex.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<PushBatteryEfficiencyForAopDTO>>> getBatteryEfficiencyForAop(PushBatteryEfficiencyQuery query) {
                log.error("【PowerFeign-queryByMonthAndBatteryEfficiency】发生异常：{}", ex.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<JSONObject>> getObtainInitialInventory(CellInventoryQuery query) {
                log.error("【PowerFeign-getObtainInitialInventory】发生异常：{}", ex.getMessage());
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<JSONObject>> queryByPage(CellShippingDaysQuery query) {
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<MwCoefficientDTO>>> listMW(MwCoefficientQuery mwCoefficientQuery) {
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<ActualEfficiencyGapForAopDTO>>> queryByMonth(ActualEfficiencyGapQuery actualEfficiencyGapQuery) {
                return Results.createFailRes();
            }

            @Override
            public ResponseEntity<Results<List<CellInventoryTjForAAOPDTO>>> getCellInventoryForAAOPList(CellInventoryTjForAAOPQuery cellInventoryTjForAAOPQuery) {
                return Results.createFailRes();
            }
        };
    }
}
