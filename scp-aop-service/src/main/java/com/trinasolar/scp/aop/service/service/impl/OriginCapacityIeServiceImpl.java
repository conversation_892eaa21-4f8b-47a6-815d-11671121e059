package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.ibm.dpf.base.core.util.DateUtils;
import com.ibm.dpf.common.domain.entity.User;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.aop.domain.base.*;
import com.trinasolar.scp.aop.domain.constant.QPConstant;
import com.trinasolar.scp.aop.domain.convert.OriginCapacityIeDEConvert;
import com.trinasolar.scp.aop.domain.convert.OriginalCapacityIeDEConvert;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.*;
import com.trinasolar.scp.aop.domain.priority.Priority;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.query.OriginCapacityIeQuery;
import com.trinasolar.scp.aop.domain.query.SelectOriginCapacityIeAllVersionQuery;
import com.trinasolar.scp.aop.domain.save.OriginCapacityIeSaveDTO;
import com.trinasolar.scp.aop.domain.utils.MathUtils;
import com.trinasolar.scp.aop.service.feign.client.ApsFeignClient;
import com.trinasolar.scp.aop.service.repository.OriginCapacityIeRepository;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.aop.service.utils.AutoGenCode;
import com.trinasolar.scp.aop.service.utils.DataHandleUtils;
import com.trinasolar.scp.aop.service.utils.IterableUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.aop.domain.enums.CountryFlagEnum;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * IE原始产能表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-25 10:03:46
 */
@Slf4j
@Service("OriginCapacityIeService")
public class OriginCapacityIeServiceImpl implements OriginCapacityIeService {
    @Autowired
    OriginCapacityIeRepository repository;
    @Autowired
    ApsFeignClient apsFeignClient;
    @Autowired
    OriginSaleService originSaleService;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    OriginCapacityIeDEConvert originCapacityIeDEConvert;

    @Autowired
    CellWaferWeightMaintenanceService cellWaferWeightMaintenanceService;

    @Autowired
    @Lazy
    HorizontalOrVerticalService horizontalOrVerticalService;

    private static final String DEFAULT_REGEX = "-";

    @PersistenceContext
    EntityManager entityManager;

    @Resource(name = "defaultValidator")
    private Validator validator;

    /**
     * 汇总主页
     *
     * @param query
     * @return
     */
    @Override
    public Page<OriginCapacityIe> queryIndex(OriginCapacityIeQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        JPAQuery<OriginCapacityIe> originCapacityIeJPAQuery = factory.select(Projections.fields(OriginCapacityIe.class,
                        originCapacityIe.productType.as("productType"),
                        originCapacityIe.dataVersion.as("dataVersion"),
                        originCapacityIe.dataType.as("dataType"),
                        originCapacityIe.createdTime.max().as("createdTime"),
                        originCapacityIe.remark.as("remark"),
                        originCapacityIe.isIEConfirm.as("isIEConfirm"),
                        originCapacityIe.year.as("year"),
                        originCapacityIe.createdBy.as("createdBy")
                )).from(originCapacityIe)
                .groupBy(originCapacityIe.productType, originCapacityIe.dataVersion, originCapacityIe.remark, originCapacityIe.dataType, originCapacityIe.isIEConfirm, originCapacityIe.year, originCapacityIe.createdBy)
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).orderBy(originCapacityIe.updatedTime.max().desc());
        JPAQuery<Long> count = factory.select(originCapacityIe.dataVersion.count()).from(originCapacityIe)
                .groupBy(originCapacityIe.productType, originCapacityIe.dataVersion);
        if (ObjectUtils.isNotEmpty(query.getProductType())) {
            originCapacityIeJPAQuery.where(originCapacityIe.productType.eq(query.getProductType().getCode()));
            count.where(originCapacityIe.productType.eq(query.getProductType().getCode()));
        }
        if (StringUtils.isNotBlank(query.getDataType())) {
            originCapacityIeJPAQuery.where(originCapacityIe.dataType.eq(query.getDataType()));
            count.where(originCapacityIe.dataType.eq(query.getDataType()));
        }

        if (query.getYear() != null) {
            originCapacityIeJPAQuery.where(originCapacityIe.year.eq(query.getYear()));
            count.where(originCapacityIe.year.eq(query.getYear()));
        }

        if (ObjectUtils.isNotEmpty(query.getDataVersion())) {
            originCapacityIeJPAQuery.where(originCapacityIe.dataVersion.eq(query.getDataVersion()));
            count.where(originCapacityIe.dataVersion.eq(query.getDataVersion()));
        }
        List<Long> fetch = count.fetch();
        List<OriginCapacityIe> originCapacityIeList = originCapacityIeJPAQuery.fetch();
        List<OriginCapacityIe> result = new ArrayList<>();
        originCapacityIeList.stream().collect(Collectors.groupingBy(item -> item.getDataVersion() + item.getProductType())).forEach((k, v) -> {
            List<OriginCapacityIe> collect = v.stream().filter(item -> StringUtils.isNotBlank(item.getRemark())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                result.add(collect.get(0));
            } else {
                result.add(v.get(0));
            }
        });


        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize());
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        result.forEach(item -> {
            if (ObjectUtils.isNotEmpty(item.getProductType())) {
                lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_PRODUCT_TYPE, item.getProductType()));
            }
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        result.forEach(item -> {
            if (ProductTypeEnum.CELL.getCode().equals(item.getProductType()) || ProductTypeEnum.WAFER.getCode().equals(item.getProductType())) {
                item.setProductTypeName(null);
                item.setDataType(null);
            } else {
                item.setDataTypeName(DataTypeEnum.getDesc(item.getDataType()));
            }
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_PRODUCT_TYPE, item.getProductType()));
            if (Objects.nonNull(lovLineDTO01) && ObjectUtils.isNotEmpty(item.getProductType())) {
                if (!item.getProductType().equals(ProductTypeEnum.MODULE.getCode())) {
                    item.setDataTypeName(null);
                }
                item.setProductTypeName(lovLineDTO01.getLovName());
            } else {
                throw new BizException(item.getCountryFlag() + "不存在");
            }
        });
        result.sort((o1, o2) -> o2.getCreatedTime().compareTo(o1.getCreatedTime()));
        List<String> ids = result.stream().map(OriginCapacityIe::getCreatedBy).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<User> userByIds = UserUtil.getUserByIds(ids);
            result.forEach(item -> {
                User user = userByIds.stream().filter(bean -> item.getCreatedBy().equals(bean.getId())).findFirst().orElse(null);
                if (ObjectUtils.isNotEmpty(user)) {
                    item.setCreatedBy(user.getName());
                }
            });
        }

        return new PageImpl<>(result, pageable, fetch.size());
    }

    @Override
    public void sopSupplyConfirm(OriginCapacityIeSaveDTO originCapacityIeSaveDTO) {
        List<OriginCapacityIe> originCapacityIeList = null;

        if (StringUtils.isBlank(originCapacityIeSaveDTO.getRemark())) {
            originCapacityIeList = repository.loadByDataVersionAndProductTypeAndDataTypeAndRemarkIsNull(originCapacityIeSaveDTO.getDataVersion(), originCapacityIeSaveDTO.getProductType(), originCapacityIeSaveDTO.getDataType());
        } else {
            originCapacityIeList = repository.findAllByDataVersionAndProductTypeAndDataTypeAndRemark(originCapacityIeSaveDTO.getDataVersion(), originCapacityIeSaveDTO.getProductType(), originCapacityIeSaveDTO.getDataType(), originCapacityIeSaveDTO.getRemark());
        }
        if (CollectionUtils.isEmpty(originCapacityIeList)) {
            throw new BizException(String.format("根据版本号{%s},产品大类{%s}未获取到数据,修改失败", originCapacityIeSaveDTO.getDataVersion(), originCapacityIeSaveDTO.getProductType()));
        }
        originCapacityIeList.stream().forEach(item -> {
            item.setIsIEConfirm(originCapacityIeSaveDTO.getIsIEConfirm());
            repository.save(item);
        });
    }

    @Override
    public Page<OriginCapacityIe> queryByPage(OriginCapacityIeQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        if (Objects.nonNull(query.getCountryFlag())) {
            booleanBuilder.and(originCapacityIe.countryFlag.eq(query.getCountryFlag().getCode()));
        }
        if (StringUtils.isNotBlank(query.getDataVersion())) {
            booleanBuilder.and(originCapacityIe.dataVersion.eq(query.getDataVersion()));
        }
        if (Objects.nonNull(query.getLatestFlag())) {
            booleanBuilder.and(originCapacityIe.latestFlag.eq(query.getLatestFlag().getCode()));
        }
        if (Objects.nonNull(query.getBusinessType())) {
            booleanBuilder.and(originCapacityIe.businessType.eq(query.getBusinessType().getCode()));
        }
        if (Objects.nonNull(query.getProductFrom())) {
            booleanBuilder.and(originCapacityIe.productFrom.eq(query.getProductFrom().getCode()));
        }
        if (Objects.nonNull(query.getProductType())) {
            booleanBuilder.and(originCapacityIe.productType.eq(query.getProductType().getCode()));
        }
        if (Objects.nonNull(query.getDataFrom())) {
            booleanBuilder.and(originCapacityIe.dataFrom.eq(query.getDataFrom().getCode()));
        }
        if (Objects.nonNull(query.getYear())) {
            booleanBuilder.and(originCapacityIe.year.eq(query.getYear()));
        }
        if (StringUtils.isNotBlank(query.getWorkshop())) {
            booleanBuilder.and(originCapacityIe.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotBlank(query.getProductGroup())) {
            booleanBuilder.and(originCapacityIe.productGroup.eq(query.getProductGroup()));
        }
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            booleanBuilder.and(originCapacityIe.productSeries.eq(query.getProductSeries()));
        }
        if (StringUtils.isNotBlank(query.getDataType())) {
            LovUtils.addName(LovHeaderCodeConstant.AOP_DATA_TYPE, DataTypeEnum.getCode(query.getDataType()));
            LovLineDTO lovLineDTO = LovUtils.get(LovHeaderCodeConstant.AOP_DATA_TYPE, query.getDataType());
            if (lovLineDTO != null) {
                query.setDataType(lovLineDTO.getLovValue());
                booleanBuilder.and(originCapacityIe.dataType.eq(query.getDataType()));
            }
        }

        Sort sort = Sort.by(Sort.Direction.DESC, "updatedTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        Page<OriginCapacityIe> all = repository.findAll(booleanBuilder, pageable);
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        all.getContent().forEach(item -> {
            if (ObjectUtils.isNotEmpty(item.getProductType())) {
                lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_PRODUCT_TYPE, item.getProductType()));
                lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            }
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        all.getContent().forEach(item -> {
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_PRODUCT_TYPE, item.getProductType()));
            if (Objects.nonNull(lovLineDTO01) && ObjectUtils.isNotEmpty(item.getProductType())) {
                item.setProductType(lovLineDTO01.getLovName());
            } else {
                throw new BizException(item.getProductType() + "不存在");
            }
            LovLineDTO lovLineDTO02 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            if (Objects.nonNull(lovLineDTO02) && ObjectUtils.isNotEmpty(item.getCountryFlag())) {
                item.setCountryFlag(lovLineDTO02.getLovName());
            } else {
                throw new BizException(item.getCountryFlag() + "不存在");
            }
        });
        return all;
    }

    @Override
    public OriginCapacityIeDTO queryById(Long id) {
        OriginCapacityIe queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        OriginCapacityIeDTO result = new OriginCapacityIeDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public OriginCapacityIeDTO save(OriginCapacityIeSaveDTO saveDTO) {
        OriginCapacityIe newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new OriginCapacityIe());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    /**
     * 基地产能汇总
     *
     * @param query
     * @return
     */
    @Override
    public Page<CapacitySummaryResultDTO> baseCapacitySummary(OriginCapacityIeSummaryDTO query) {
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        if (ObjectUtils.isEmpty(query.getProductType())) {
            return new PageImpl<>(new ArrayList<>());
        }
        if(StringUtils.isEmpty(query.getDataVersion())){
            List<String> newByYearAndProductType = queryVersionNewByYearAndProductType(query.getYear(), query.getProductType());
            if(CollectionUtils.isEmpty(newByYearAndProductType)){
                return new PageImpl<>(new ArrayList<>());
            }
            query.setDataVersion(newByYearAndProductType.get(0));
        }
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        booleanBuilder.and(originCapacityIe.dataVersion.eq(query.getDataVersion()).and(originCapacityIe.productType.eq(query.getProductType())));
        Sort sort = Sort.by(Sort.Direction.DESC, "updatedTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        List<CapacitySummaryResultDTO> fetch = factory.select(Projections.fields(CapacitySummaryResultDTO.class,
                originCapacityIe.basePlace,
                originCapacityIe.year,
                originCapacityIe.m1Quantity.sum().as("m1Quantity"),
                originCapacityIe.m2Quantity.sum().as("m2Quantity"),
                originCapacityIe.m3Quantity.sum().as("m3Quantity"),
                originCapacityIe.m4Quantity.sum().as("m4Quantity"),
                originCapacityIe.m5Quantity.sum().as("m5Quantity"),
                originCapacityIe.m6Quantity.sum().as("m6Quantity"),
                originCapacityIe.m7Quantity.sum().as("m7Quantity"),
                originCapacityIe.m8Quantity.sum().as("m8Quantity"),
                originCapacityIe.m9Quantity.sum().as("m9Quantity"),
                originCapacityIe.m10Quantity.sum().as("m10Quantity"),
                originCapacityIe.m11Quantity.sum().as("m11Quantity"),
                originCapacityIe.m12Quantity.sum().as("m12Quantity"),
                originCapacityIe.m1Wquantity.sum().as("m1Wquantity"),
                originCapacityIe.m2Wquantity.sum().as("m2Wquantity"),
                originCapacityIe.m3Wquantity.sum().as("m3Wquantity"),
                originCapacityIe.m4Wquantity.sum().as("m4Wquantity"),
                originCapacityIe.m5Wquantity.sum().as("m5Wquantity"),
                originCapacityIe.m6Wquantity.sum().as("m6Wquantity"),
                originCapacityIe.m7Wquantity.sum().as("m7Wquantity"),
                originCapacityIe.m8Wquantity.sum().as("m8Wquantity"),
                originCapacityIe.m9Wquantity.sum().as("m9Wquantity"),
                originCapacityIe.m10Wquantity.sum().as("m10Wquantity"),
                originCapacityIe.m11Wquantity.sum().as("m11Wquantity"),
                originCapacityIe.m12Wquantity.sum().as("m12Wquantity")
        )).from(originCapacityIe).where(booleanBuilder).groupBy(originCapacityIe.basePlace,
                originCapacityIe.year).fetch();
        List<Tuple> list = factory.select(originCapacityIe.basePlace,
                originCapacityIe.year).from(originCapacityIe).where(booleanBuilder).groupBy(originCapacityIe.basePlace,
                originCapacityIe.year).fetch();
        if (CollectionUtils.isNotEmpty(fetch)) {
            fetch.forEach(item -> {
                item.roundOne();
                item.roundOne2();
                item.sum();
                item.sumPieces();
                Integer basePlace = Priority.BasePlace.getByCode(item.getBasePlace());
                item.setPriority(basePlace);
            });
        }
        return new PageImpl<>(fetch.stream().sorted().collect(Collectors.toList()), pageable, list.size());
    }

    /***
     * 产品系列汇总
     * @param query
     * @return
     */
    @Override
    public Page<CapacitySummaryResultDTO> productSeriesCapacitySummary(OriginCapacityIeSummaryDTO query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        if (ObjectUtils.isEmpty(query.getProductType())) {
            return new PageImpl<>(new ArrayList<>());
        }
        if(ObjectUtils.isEmpty(query.getDataVersion())){
            List<String> newByYearAndProductType = queryVersionNewByYearAndProductType(query.getYear(), query.getProductType());
            if(CollectionUtils.isEmpty(newByYearAndProductType)){
                return new PageImpl<>(new ArrayList<>());
            }
            query.setDataVersion(newByYearAndProductType.get(0));
        }
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        List<CapacitySummaryResultDTO> fetch = jpaQueryFactory.select(Projections.fields(CapacitySummaryResultDTO.class,
                        originCapacityIe.countryFlag.as("countryFlag"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.year.as("year"),
                        originCapacityIe.m1Quantity.sum().as("m1Quantity"),
                        originCapacityIe.m2Quantity.sum().as("m2Quantity"),
                        originCapacityIe.m3Quantity.sum().as("m3Quantity"),
                        originCapacityIe.m4Quantity.sum().as("m4Quantity"),
                        originCapacityIe.m5Quantity.sum().as("m5Quantity"),
                        originCapacityIe.m6Quantity.sum().as("m6Quantity"),
                        originCapacityIe.m7Quantity.sum().as("m7Quantity"),
                        originCapacityIe.m8Quantity.sum().as("m8Quantity"),
                        originCapacityIe.m9Quantity.sum().as("m9Quantity"),
                        originCapacityIe.m10Quantity.sum().as("m10Quantity"),
                        originCapacityIe.m11Quantity.sum().as("m11Quantity"),
                        originCapacityIe.m12Quantity.sum().as("m12Quantity"),
                        originCapacityIe.m1Wquantity.sum().as("m1Wquantity"),
                        originCapacityIe.m2Wquantity.sum().as("m2Wquantity"),
                        originCapacityIe.m3Wquantity.sum().as("m3Wquantity"),
                        originCapacityIe.m4Wquantity.sum().as("m4Wquantity"),
                        originCapacityIe.m5Wquantity.sum().as("m5Wquantity"),
                        originCapacityIe.m6Wquantity.sum().as("m6Wquantity"),
                        originCapacityIe.m7Wquantity.sum().as("m7Wquantity"),
                        originCapacityIe.m8Wquantity.sum().as("m8Wquantity"),
                        originCapacityIe.m9Wquantity.sum().as("m9Wquantity"),
                        originCapacityIe.m10Wquantity.sum().as("m10Wquantity"),
                        originCapacityIe.m11Wquantity.sum().as("m11Wquantity"),
                        originCapacityIe.m12Wquantity.sum().as("m12Wquantity")

                        )).from(originCapacityIe)
                .where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.countryFlag, originCapacityIe.productSeries, originCapacityIe.year)
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
        setQuarterQuantity(fetch);
        List<Tuple> list = jpaQueryFactory.select(originCapacityIe.countryFlag.as("countryFlag"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.year.as("year")).from(originCapacityIe).where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.countryFlag, originCapacityIe.productSeries, originCapacityIe.year).fetch();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //值集转换
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        fetch.forEach(item -> {
            Integer country = Priority.Country.getByCode(item.getCountryFlag());
            Integer series = 0;
            if (ProductTypeEnum.MODULE.getCode().equals(query.getProductType())) {
                series = Priority.Module.getByCode(item.getProductSeries());
            } else if (ProductTypeEnum.CELL.getCode().equals(query.getProductType())) {
                series = Priority.Cell.getByCode(item.getProductSeries());
            } else {
                series = Priority.Wafer.getByCode(item.getProductSeries());
            }
            item.setPriority(country * 100 + series);
            if (ObjectUtils.isNotEmpty(item.getCountryFlag())) {
                lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            }
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(item -> {
            item.roundOne();
            item.roundOne2();
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, item.getCountryFlag()));
            if (Objects.nonNull(lovLineDTO01) && ObjectUtils.isNotEmpty(item.getCountryFlag())) {
                item.setCountryFlag(lovLineDTO01.getLovName());
            } else {
                throw new BizException(item.getCountryFlag() + "不存在");
            }
            item.sum();
            item.sumPieces();
        });
        return new PageImpl<>(fetch.stream().sorted().collect(Collectors.toList()), pageable, list.size());
    }

    @Override
    public CapacitySummaryResultDTO productSeriesCapacitySummaryTotal(String dataVersion, String productType, int year) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        if (ObjectUtils.isEmpty(dataVersion) || StringUtils.isEmpty(productType)) {
            return new CapacitySummaryResultDTO();
        }
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        List<CapacitySummaryResultDTO> fetch = jpaQueryFactory.select(Projections.fields(CapacitySummaryResultDTO.class,
                        originCapacityIe.year.as("year"),
                        originCapacityIe.m1Quantity.sum().as("m1Quantity"),
                        originCapacityIe.m2Quantity.sum().as("m2Quantity"),
                        originCapacityIe.m3Quantity.sum().as("m3Quantity"),
                        originCapacityIe.m4Quantity.sum().as("m4Quantity"),
                        originCapacityIe.m5Quantity.sum().as("m5Quantity"),
                        originCapacityIe.m6Quantity.sum().as("m6Quantity"),
                        originCapacityIe.m7Quantity.sum().as("m7Quantity"),
                        originCapacityIe.m8Quantity.sum().as("m8Quantity"),
                        originCapacityIe.m9Quantity.sum().as("m9Quantity"),
                        originCapacityIe.m10Quantity.sum().as("m10Quantity"),
                        originCapacityIe.m11Quantity.sum().as("m11Quantity"),
                        originCapacityIe.m12Quantity.sum().as("m12Quantity"))
                ).from(originCapacityIe)
                .where(originCapacityIe.dataVersion.eq(dataVersion)
                        .and(originCapacityIe.productType.eq(productType))
                        .and(originCapacityIe.year.eq(year)))
                .groupBy(originCapacityIe.year).fetch();
        setQuarterQuantity(fetch);
        return CollectionUtils.isEmpty(fetch) ? new CapacitySummaryResultDTO() : fetch.get(0);
    }

    /**
     * 车间+产品系列产能汇总
     *
     * @param query
     * @return
     */
    @Override
    public Page<CapacitySummaryResultDTO> workshopAndProductSeriesSummary(OriginCapacityIeSummaryDTO query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        if (ObjectUtils.isEmpty(query.getProductType())) {
            return new PageImpl<>(new ArrayList<>());
        }
        if(StringUtils.isEmpty(query.getDataVersion())){
            List<String> newByYearAndProductType = queryVersionNewByYearAndProductType(query.getYear(), query.getProductType());
            if(CollectionUtils.isEmpty(newByYearAndProductType)){
                return new PageImpl<>(new ArrayList<>());
            }
            query.setDataVersion(newByYearAndProductType.get(0));
        }

        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        List<CapacitySummaryResultDTO> fetch = jpaQueryFactory.select(Projections.fields(CapacitySummaryResultDTO.class,
                        originCapacityIe.basePlace.as("basePlace"),
                        originCapacityIe.workshop.as("workshop"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.year.as("year"),
                        originCapacityIe.m1Quantity.sum().as("m1Quantity"),
                        originCapacityIe.m2Quantity.sum().as("m2Quantity"),
                        originCapacityIe.m3Quantity.sum().as("m3Quantity"),
                        originCapacityIe.m4Quantity.sum().as("m4Quantity"),
                        originCapacityIe.m5Quantity.sum().as("m5Quantity"),
                        originCapacityIe.m6Quantity.sum().as("m6Quantity"),
                        originCapacityIe.m7Quantity.sum().as("m7Quantity"),
                        originCapacityIe.m8Quantity.sum().as("m8Quantity"),
                        originCapacityIe.m9Quantity.sum().as("m9Quantity"),
                        originCapacityIe.m10Quantity.sum().as("m10Quantity"),
                        originCapacityIe.m11Quantity.sum().as("m11Quantity"),
                        originCapacityIe.m12Quantity.sum().as("m12Quantity"),
                        originCapacityIe.m1Wquantity.sum().as("m1Wquantity"),
                        originCapacityIe.m2Wquantity.sum().as("m2Wquantity"),
                        originCapacityIe.m3Wquantity.sum().as("m3Wquantity"),
                        originCapacityIe.m4Wquantity.sum().as("m4Wquantity"),
                        originCapacityIe.m5Wquantity.sum().as("m5Wquantity"),
                        originCapacityIe.m6Wquantity.sum().as("m6Wquantity"),
                        originCapacityIe.m7Wquantity.sum().as("m7Wquantity"),
                        originCapacityIe.m8Wquantity.sum().as("m8Wquantity"),
                        originCapacityIe.m9Wquantity.sum().as("m9Wquantity"),
                        originCapacityIe.m10Wquantity.sum().as("m10Wquantity"),
                        originCapacityIe.m11Wquantity.sum().as("m11Wquantity"),
                        originCapacityIe.m12Wquantity.sum().as("m12Wquantity")
                        )).from(originCapacityIe)
                .where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.workshop, originCapacityIe.productSeries, originCapacityIe.year, originCapacityIe.basePlace)
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
        setQuarterQuantity(fetch);
        fetch.forEach(item -> {
            item.roundOne();
            item.sum();
            Integer basePlace = Priority.BasePlace.getByCode(item.getBasePlace());
            Integer workshop = Priority.Workshop.getByCode(item.getWorkshop());
            Integer series = 0;
            if (ProductTypeEnum.MODULE.getCode().equals(query.getProductType())) {
                series = Priority.Module.getByCode(item.getProductSeries());
            } else if (ProductTypeEnum.CELL.getCode().equals(query.getProductType())) {
                series = Priority.Cell.getByCode(item.getProductSeries());
            } else {
                series = Priority.Wafer.getByCode(item.getProductSeries());
            }
            item.setPriority(basePlace * 1100000 + workshop * 100 + series);
        });
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        List<Tuple> list = jpaQueryFactory.select(originCapacityIe.basePlace.as("basePlace"),
                        originCapacityIe.workshop.as("workshop"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.year.as("year")).from(originCapacityIe).where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.workshop, originCapacityIe.productSeries, originCapacityIe.year, originCapacityIe.basePlace).fetch();
        return new PageImpl<>(fetch.stream().sorted().collect(Collectors.toList()), pageable, list.size());
    }

    /**
     * 车间+产品+产品族系列产能汇总
     *
     * @param query
     * @return
     */
    @Override
    public Page<CapacitySummaryResultDTO> workSAndProSAndGroupSummary(OriginCapacityIeSummaryDTO query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        if (ObjectUtils.isEmpty(query.getProductType())) {
            return new PageImpl<>(new ArrayList<>());
        }
        if(ObjectUtils.isEmpty(query.getDataVersion())){
            List<String> newByYearAndProductType = queryVersionNewByYearAndProductType(query.getYear(), query.getProductType());
            if(CollectionUtils.isEmpty(newByYearAndProductType)){
                return new PageImpl<>(new ArrayList<>());
            }
            query.setDataVersion(newByYearAndProductType.get(0));
        }
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        List<CapacitySummaryResultDTO> fetch = jpaQueryFactory.select(Projections.fields(CapacitySummaryResultDTO.class,
                        originCapacityIe.basePlace.as("basePlace"),
                        originCapacityIe.workshop.as("workshop"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.productGroup.as("productGroup"),
                        originCapacityIe.year.as("year"),
                        originCapacityIe.m1Quantity.sum().as("m1Quantity"),
                        originCapacityIe.m2Quantity.sum().as("m2Quantity"),
                        originCapacityIe.m3Quantity.sum().as("m3Quantity"),
                        originCapacityIe.m4Quantity.sum().as("m4Quantity"),
                        originCapacityIe.m5Quantity.sum().as("m5Quantity"),
                        originCapacityIe.m6Quantity.sum().as("m6Quantity"),
                        originCapacityIe.m7Quantity.sum().as("m7Quantity"),
                        originCapacityIe.m8Quantity.sum().as("m8Quantity"),
                        originCapacityIe.m9Quantity.sum().as("m9Quantity"),
                        originCapacityIe.m10Quantity.sum().as("m10Quantity"),
                        originCapacityIe.m11Quantity.sum().as("m11Quantity"),
                        originCapacityIe.m12Quantity.sum().as("m12Quantity"),
                        originCapacityIe.m1Wquantity.sum().as("m1Wquantity"),
                        originCapacityIe.m2Wquantity.sum().as("m2Wquantity"),
                        originCapacityIe.m3Wquantity.sum().as("m3Wquantity"),
                        originCapacityIe.m4Wquantity.sum().as("m4Wquantity"),
                        originCapacityIe.m5Wquantity.sum().as("m5Wquantity"),
                        originCapacityIe.m6Wquantity.sum().as("m6Wquantity"),
                        originCapacityIe.m7Wquantity.sum().as("m7Wquantity"),
                        originCapacityIe.m8Wquantity.sum().as("m8Wquantity"),
                        originCapacityIe.m9Wquantity.sum().as("m9Wquantity"),
                        originCapacityIe.m10Wquantity.sum().as("m10Wquantity"),
                        originCapacityIe.m11Wquantity.sum().as("m11Wquantity"),
                        originCapacityIe.m12Wquantity.sum().as("m12Wquantity"))
                ).from(originCapacityIe)
                .where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.workshop, originCapacityIe.productSeries, originCapacityIe.productGroup, originCapacityIe.year, originCapacityIe.basePlace)
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
        setQuarterQuantity(fetch);
        fetch.forEach(item -> {
            item.roundOne();
            item.roundOne2();
            item.sum();
            item.sumPieces();
            Integer basePlace = Priority.BasePlace.getByCode(item.getBasePlace());
            Integer workshop = Priority.Workshop.getByCode(item.getWorkshop());
            Integer series = 0;
            if (ProductTypeEnum.MODULE.getCode().equals(query.getProductType())) {
                series = Priority.Module.getByCode(item.getProductSeries());
            } else if (ProductTypeEnum.CELL.getCode().equals(query.getProductType())) {
                series = Priority.Cell.getByCode(item.getProductSeries());
            } else {
                series = Priority.Wafer.getByCode(item.getProductSeries());
            }
            item.setPriority(basePlace * 1100000 + workshop * 100 + series);
        });

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        List<Tuple> list = jpaQueryFactory.select(originCapacityIe.basePlace.as("basePlace"),
                        originCapacityIe.workshop.as("workshop"),
                        originCapacityIe.productSeries.as("productSeries"),
                        originCapacityIe.productGroup.as("productGroup"),
                        originCapacityIe.year.as("year")).from(originCapacityIe)
                .where(originCapacityIe.dataVersion.eq(query.getDataVersion())
                        .and(originCapacityIe.productType.eq(query.getProductType())))
                .groupBy(originCapacityIe.workshop, originCapacityIe.productSeries, originCapacityIe.productGroup, originCapacityIe.year, originCapacityIe.basePlace).fetch();
        return new PageImpl<>(fetch.stream().sorted().collect(Collectors.toList()), pageable, list.size());
    }

    private void setQuarterQuantity(List<CapacitySummaryResultDTO> fetch) {
        fetch.forEach(item -> {
            item.perfect();
            item.perfect2();
            item.setQ1Quantity(item.getM1Quantity().add(item.getM2Quantity()).add(item.getM3Quantity()));
            item.setQ2Quantity(item.getM4Quantity().add(item.getM5Quantity()).add(item.getM6Quantity()));
            item.setQ3Quantity(item.getM7Quantity().add(item.getM8Quantity()).add(item.getM9Quantity()));
            item.setQ4Quantity(item.getM10Quantity().add(item.getM11Quantity()).add(item.getM12Quantity()));
            item.setQ1Wquantity(item.getM1Wquantity().add(item.getM2Wquantity()).add(item.getM3Wquantity()));
            item.setQ2Wquantity(item.getM4Wquantity().add(item.getM5Wquantity()).add(item.getM6Wquantity()));
            item.setQ3Wquantity(item.getM7Wquantity().add(item.getM8Wquantity()).add(item.getM9Wquantity()));
            item.setQ4Wquantity(item.getM10Wquantity().add(item.getM11Wquantity()).add(item.getM12Wquantity()));
        });
    }

    /**
     * 查看产能表所有产能
     *
     * @return
     */
    @Override
    public List<String> queryAllVersion(ProductTypeQueryDTO queryDTO) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<OriginCapacityIeDTO> sqlFrom = jpaQueryFactory.select(
                        Projections.fields(OriginCapacityIeDTO.class,
                                originCapacityIe.dataVersion.as("dataVersion"),
                                originCapacityIe.productType.as("productType")))
                .from(originCapacityIe);
        if (queryDTO.getProductType() != null) {
            sqlFrom.where(originCapacityIe.productType.eq(queryDTO.getProductType().getCode()));
        }
        if (StringUtils.isNotBlank(queryDTO.getDataType()) && queryDTO.getProductType() == ProductTypeEnum.MODULE) {
            String code = DataTypeEnum.getCode(queryDTO.getDataType());
            if (StringUtils.isNotBlank(code)) {
                sqlFrom.where(originCapacityIe.dataType.eq(code));
            }
        }
        if (ObjectUtils.isNotEmpty(queryDTO.getStartTime())) {
            int year = queryDTO.getStartTime().getYear();
            sqlFrom.where(originCapacityIe.year.eq(year));
        }
        if (ObjectUtils.isNotEmpty(queryDTO.getEndTime())) {
            int year = queryDTO.getEndTime().getYear();
            sqlFrom.where(originCapacityIe.year.eq(year));
        }
        List<OriginCapacityIeDTO> query = sqlFrom.orderBy(originCapacityIe.updatedTime.desc()).fetch();


        //        query.stream().collect(Collectors.groupingBy(item -> item.getDataVersion() + item.getProductType())).forEach((k, v) -> {
//            result.add(v.get(0).getDataVersion());
//        });
        return query.stream().map(OriginCapacityIeDTO::getDataVersion).distinct().collect(Collectors.toList());
    }

    @Override
    public List<String> queryAllVersion(SelectOriginCapacityIeAllVersionQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        BooleanBuilder booleanBuilder = new BooleanBuilder();

        if (ObjectUtils.isNotEmpty(query.getDataFrom())) {
            booleanBuilder.and(originCapacityIe.dataFrom.eq(query.getDataFrom().toString()));
        }
        if (ObjectUtils.isNotEmpty(query.getProductTypeEnum())) {
            booleanBuilder.and(originCapacityIe.productType.eq(query.getProductTypeEnum().getCode()));
        }
        if (ObjectUtils.isNotEmpty(query.getDateFrom()) && ObjectUtils.isNotEmpty(query.getDateTo())) {
            booleanBuilder.and(originCapacityIe.createdTime.between(query.getDateFrom(), query.getDateTo()));
        }
        return jpaQueryFactory.select(originCapacityIe.dataVersion).from(originCapacityIe).where(booleanBuilder).orderBy(originCapacityIe.updatedTime.desc()).fetch().stream().distinct().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
    }

    private List<OriginCapacityIe> queryByDataVersion(String dataVersion, String productType) {
        return repository.findAllByDataVersionAndProductType(dataVersion, productType);
    }

    private boolean judgeEqual(ContrastQuery query) {
        return query.getDataVersion().equals(query.getContrastDataVersion());
    }

    /**
     * 源数据预处理，产能对比-基地产能对比
     *
     * @param query
     * @return
     */
    @Override
    public List<ContrastCapacityResultDTO> capacityComparisonByBasePlace(ContrastQuery query) {
        if (judgeEqual(query)) {
            throw new BizException("data version and contrast data version can not be equal!");
        }
        //1.拿到版本原始产能,并进行空校验
        List<OriginCapacityIe> CapacityList = queryByDataVersion(query.getDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(CapacityList, "data version get null data");
        //2.拿到对比版本原始产能，并进行空校验
        List<OriginCapacityIe> contrastCapacityList = queryByDataVersion(query.getContrastDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(contrastCapacityList, "contrast data version get null data");
        //给数据添加基地值
        Map<String, ContrastCapacityResultDTO> resultMap = new HashMap<>();
        Map<String, List<OriginalCapacityIeExtDTO>> capacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(CapacityList).stream().collect(Collectors.groupingBy(OriginalCapacityIeExtDTO::getBasePlace));
        DataHandleUtils.dataVersionHandle(capacityExtDTOList, resultMap);
        Map<String, List<OriginalCapacityIeExtDTO>> contrastCapacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(contrastCapacityList).stream().collect(Collectors.groupingBy(OriginalCapacityIeExtDTO::getBasePlace));
        DataHandleUtils.contrastDataVersionHandle(resultMap, contrastCapacityExtDTOList);
        List<ContrastCapacityResultDTO> resultList = new ArrayList<>();
        resultMap.forEach((k, v) -> resultList.add(v));
        DataHandleUtils.setContrastValue(resultList);
        ContrastCapacityResultDTO extDTO = DataHandleUtils.contrastCapacityHandle(resultList, YesOrNoEnums.N);
        resultList.add(extDTO);
        return resultList;
    }


    private List<OriginalCapacityIeExtDTO> addBasePlace(Map<String, ModuleBasePlace> workshopBasePlace, List<OriginCapacityIe> CapacityList) {
        List<OriginalCapacityIeExtDTO> capacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(CapacityList);
        capacityExtDTOList.forEach(item -> {
            ModuleBasePlace moduleBasePlace = workshopBasePlace.get(item.getWorkshop());
            if (ObjectUtils.isNotEmpty(moduleBasePlace)) {
                item.setBasePlace(moduleBasePlace.getBasePlace());
            } else {
                item.setBasePlace("");
            }
        });
        return capacityExtDTOList;
    }

    /**
     * 源数据预处理，产能对比-产品系列产能对比
     *
     * @param query
     * @return
     */
    @Override
    public List<ContrastCapacityResultDTO> capacityComparisonBySeries(ContrastQuery query) {
        if (judgeEqual(query)) {
            throw new BizException("data version and contrast data version can not be equal!");
        }
        //1.拿到版本原始产能,并进行空校验
        List<OriginCapacityIe> CapacityList = queryByDataVersion(query.getDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(CapacityList, "data version get null data");

        //2.拿到对比版本原始产能，并进行空校验
        List<OriginCapacityIe> contrastCapacityList = queryByDataVersion(query.getContrastDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(contrastCapacityList, "contrast data version get null data");

        List<OriginalCapacityIeExtDTO> capacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(CapacityList);
        List<OriginalCapacityIeExtDTO> contrastCapacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(contrastCapacityList);

        //3.1将原始数据以产品系列进行分组
        Map<String, List<OriginalCapacityIeExtDTO>> capacityMap = capacityExtDTOList.stream().collect(Collectors.groupingBy(OriginalCapacityIeExtDTO::getProductSeries));
        //4按产品系列分组的结果集
        Map<String, ContrastCapacityResultDTO> resultMap = new HashMap<>();
        //3.1.1将版本产能数据录入
        DataHandleUtils.dataVersionHandle(capacityMap, resultMap);
        //3.2将对比版本数据按照产品系列进行分组
        Map<String, List<OriginalCapacityIeExtDTO>> contrastCapacityMap = contrastCapacityExtDTOList.stream().collect(Collectors.groupingBy(OriginalCapacityIeExtDTO::getProductSeries));
        //3.2.1对对比版本数据进行数据录入
        DataHandleUtils.contrastDataVersionHandle(resultMap, contrastCapacityMap);
        List<ContrastCapacityResultDTO> resultList = new ArrayList<>();
        resultMap.forEach((k, v) -> resultList.add(v));
        DataHandleUtils.setContrastValue(resultList);
        ContrastCapacityResultDTO extDTO = DataHandleUtils.contrastCapacityHandle(resultList, YesOrNoEnums.Y);
        resultList.add(extDTO);
        return resultList;
    }


    /**
     * 源数据预处理，产能对比-基地+产品系列产能对比
     *
     * @param query
     * @return
     */
    @Override
    public List<ContrastCapacityResultDTO> capacityComparisonByTwo(ContrastQuery query) {
        if (judgeEqual(query)) {
            throw new BizException("data version and contrast data version can not be equal!");
        }
        //1.拿到版本原始产能,并进行空校验
        List<OriginCapacityIe> CapacityList = queryByDataVersion(query.getDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(CapacityList, "data version get null data");
        //2.拿到对比版本原始产能，并进行空校验
        List<OriginCapacityIe> contrastCapacityList = queryByDataVersion(query.getContrastDataVersion(), query.getProductType().getCode());
        Assert.notEmpty(contrastCapacityList, "contrast data version get null data");
        Map<String, ContrastCapacityResultDTO> resultMap = new HashMap<>();
        Map<String, List<OriginalCapacityIeExtDTO>> capacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(CapacityList).stream().collect(Collectors.groupingBy(item -> item.getBasePlace() + item.getProductSeries()));
        DataHandleUtils.dataVersionHandle(capacityExtDTOList, resultMap);
        Map<String, List<OriginalCapacityIeExtDTO>> contrastCapacityExtDTOList = OriginalCapacityIeDEConvert.INSTANCE.toDemandLinesSaveDTOList(contrastCapacityList).stream().collect(Collectors.groupingBy(item -> item.getBasePlace() + item.getProductSeries()));
        DataHandleUtils.contrastDataVersionHandle(resultMap, contrastCapacityExtDTOList);
        List<ContrastCapacityResultDTO> resultList = new ArrayList<>();
        resultMap.forEach((k, v) -> resultList.add(v));
        DataHandleUtils.setContrastValue(resultList);
        ContrastCapacityResultDTO extDTO = DataHandleUtils.contrastCapacityHandle(resultList, YesOrNoEnums.N);
        resultList.add(extDTO);
        return resultList;
    }

    @Override
    public String batchSave(List<OriginCapacityIe> originCapacityIes, String dataType, String productType, String remark) {
       if(StringUtils.isNoneBlank(productType) && ProductTypeEnum.CELL.getDesc().equals(productType)){

       } else {
          if (IterableUtils.checkListData(originCapacityIes)) {
               throw new BizException("Excel中存在重复数据，请检查！");
           }
       }
        //重复数据校验
        List<OriginCapacityIe> savelist = Lists.newArrayList();

        //Lov值对象查询 ： IE原始产能
        HashSet<LovLineQuery> lovLineQuerys = new HashSet<>();
        lovLineQuerys.add(LovLineQuery.builder().code(LovHeaderCodeConstant.AOP_CELL_SHARD).build());
        Map<String, LovLineDTO> allByHeaderCode = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        OriginCapacityIe ie = originCapacityIes.get(0);
        for (OriginCapacityIe originCapacityIe : originCapacityIes) {
            originCapacityIe.setRemark(ie.getRemark());
            //国内/海外
            lovLineQuerys.add(LovLineQuery.builder().code(LovHeaderCodeConstant.AOP_COUNTRY_FLAG).name(originCapacityIe.getCountryFlag()).build());
            if (StringUtils.isNotBlank(productType)) {
                originCapacityIe.setProductType(productType);
                //产品类型
                lovLineQuerys.add(LovLineQuery.builder().code(LovHeaderCodeConstant.AOP_PRODUCT_TYPE).name(productType).build());
            }
            if (StringUtils.isNotBlank(dataType)) {
                //数据类型：满产/公布
                lovLineQuerys.add(LovLineQuery.builder().code(LovHeaderCodeConstant.AOP_DATA_TYPE).name(dataType).build());
            }
            //单位
            lovLineQuerys.add(LovLineQuery.builder().code(LovHeaderCodeConstant.AOP_UOM).name(originCapacityIe.getUnit()).build());
        }
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        List<String> errorList = new ArrayList<>();
        Map<String, ModuleBasePlace> workshopBasePlace = Objects.requireNonNull(apsFeignClient.queryAllBasePlaceWorkshop().getBody()).getData();
        List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
        List<String> colList = productInfoList.stream().map(productInfo -> {
            return productInfo.getProductSeries() + productInfo.getProductGroup();
        }).collect(Collectors.toList());
        List<String> waferModelList = productInfoList.stream().map(ProductInfo::getWaferModel).collect(Collectors.toList());
        List<String> cellModelList = productInfoList.stream().map(ProductInfo::getCellModel).collect(Collectors.toList());
        Integer count = 0;
        for (OriginCapacityIe data : originCapacityIes) {
            count++;
            if (ProductTypeEnum.MODULE.getDesc().equals(data.getProductType())) {
                if (!colList.contains(data.getProductSeries() + data.getProductGroup())) {
                    errorList.add(String.format("第 %s 行:组件产品系列和产品族的关系无效", count));
                }
            } else if (ProductTypeEnum.CELL.getDesc().equals(data.getProductType())) {
                if (!cellModelList.contains(data.getProductSeries())) {
                    errorList.add(String.format("第 %s 行：电池产品系列无效", count));
                }
                if (allByHeaderCode.get(data.getFragmentType()) == null) {
                    return "分片方式 值错误";
                }
            } else if (ProductTypeEnum.WAFER.getDesc().equals(data.getProductType())) {
                if (!waferModelList.contains(data.getProductSeries())) {
                    errorList.add(String.format("第 %s 行：硅片产品系列无效", count));
                }
            } else {
                errorList.add(String.format("第 %s 行:产品大类值异常", count));
            }
            ModuleBasePlace moduleBasePlace = workshopBasePlace.get(data.getWorkshop());
            if (!productType.equals(ProductTypeEnum.WAFER.getDesc())) {
                if (moduleBasePlace == null) {
                    errorList.add(String.format("第 %s 行:车间(WorkShop) 值异常", count));
                    continue;
                } else {
                    if (StringUtils.isEmpty(moduleBasePlace.getWorkshop())) {
                        errorList.add(String.format("第 %s 行:车间(WorkShop) 值异常", count));
                        continue;
                    }
                }
            }
            String errorLog = data.setLovValue(lovMap, dataType, productType);
            if (StringUtils.isNotEmpty(errorLog)) {
                errorList.add("第" + count + "行" + errorLog);
                continue;
            }

            if (!productType.equals(ProductTypeEnum.WAFER.getDesc())) {
                if (StringUtils.isNotBlank(data.getCountryFlag())) {
                    if (!moduleBasePlace.getIsOversea().equals(data.getCountryFlag())) {
                        errorList.add("第" + count + "行" + "国内/海外不匹配");
                        continue;
                    }
                } else {
                    data.setCountryFlag(moduleBasePlace.getIsOversea());
                }
            }

//            OriginCapacityIe originCapacityIe = new OriginCapacityIe();
//            originCapacityIe.setInitialValue(data);
//            if (StringUtils.isNotEmpty(originCapacityIe.setInitialValue(data))) {
//                errorList.add("第" + count + "行" + originCapacityIe.setInitialValue(data));
//                continue;
//            }
            if (!productType.equals(ProductTypeEnum.WAFER.getDesc())) {
                //基地
                data.setBasePlace(moduleBasePlace.getBasePlace());
            }
            //最新数据标记 默认"Y"
            data.setLatestFlag(ExcelDefaultValue.EXCEL_FLAG_Y.getCode());
            //产品来源 默认“IE”
            data.setProductFrom(ExcelDefaultValue.EXCEL_PRODUCT_FROM_IE.getCode());
            //数据来源 GSM导入
            data.setDataFrom(ExcelDefaultValue.EXCEL_AOP_SALES_DATA_SOURCE_EXCEL.getCode());
            data.setM1Wquantity(data.getM1Quantity());
            data.setM2Wquantity(data.getM2Quantity());
            data.setM3Wquantity(data.getM3Quantity());
            data.setM4Wquantity(data.getM4Quantity());
            data.setM5Wquantity(data.getM5Quantity());
            data.setM6Wquantity(data.getM6Quantity());
            data.setM7Wquantity(data.getM7Quantity());
            data.setM8Wquantity(data.getM8Quantity());
            data.setM9Wquantity(data.getM9Quantity());
            data.setM10Wquantity(data.getM10Quantity());
            data.setM11Wquantity(data.getM11Quantity());
            data.setM12Wquantity(data.getM12Quantity());
            data.setBusinessType("SALE");
            data.setRemark(remark);
            savelist.add(data);

        }

        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(errorList.toString());
        }
        //生成 IE原始自产产能版本号
        String salCode = AutoGenCode.getCode(GenCodeKeyEnum.AOP_CAP_IE);
        if (CollectionUtils.isNotEmpty(savelist)) {

            List<String> productGroupList = savelist.stream().map(OriginCapacityIe::getProductGroup).collect(Collectors.toList());
            List<String> productSeriesList = savelist.stream().map(OriginCapacityIe::getProductSeries).collect(Collectors.toList());
            List<String> countryFlagList = savelist.stream().map(OriginCapacityIe::getCountryFlag).collect(Collectors.toList());
            List<Integer> yearList =  savelist.stream().map(OriginCapacityIe::getYear).collect(Collectors.toList());

            //硅片折算兆瓦系数

            List<CellWaferWeightMaintenanceDTO> waferList = getCellWaferWeightMaintenanceList(yearList,ProductTypeEnum.WAFER);
            if (waferList.isEmpty()) {
                throw new BizException("获取硅片兆瓦系数失败，请检查数据！");
            }
            List<String> dataVersions = waferList.stream().map(CellWaferWeightMaintenanceDTO::getDataVersion).collect(Collectors.toList());

            if (StringUtils.isNoneBlank(dataType) && ProductTypeEnum.MODULE.getDesc().equals(productType)) {
                //长期功率维护（根据产品族获取长期预测功率） 维度：国内海外+产品族+横竖装+年份  -- 按月
                List<ProductFamilyCalculationPowerMaintenanceDTO> productFamilyCalculationPowerMaintenanceDTOS = getProductFamilyCalculationPowerMaintenanceDTO(productGroupList);
                if (productFamilyCalculationPowerMaintenanceDTOS.isEmpty()) {
                    throw new BizException(String.format("产品族[%s],获取产品族测算功率失败，请检查数据！", JSONUtil.toJsonStr(productGroupList)));
                }
                //横竖装比例（根据国内海外、产品系列获取比例系数） 维度：国内海外+产品系列+年份   注意:横竖装系数（竖装+非标（根据竖装计算得到）） -- 按季度
                List<HorizontalOrVerticalDTO> horizontalOrVerticalDTOS = horizontalOrVerticalService.getHorizontalOrVerticalDTO(countryFlagList, productSeriesList,  dataVersions);
                if (horizontalOrVerticalDTOS.isEmpty()) {
                    throw new BizException(String.format("根据国内海外[%s]、产品系列[%s]获取横竖装比例失败，请检查数据！", JSONUtil.toJsonStr(countryFlagList), JSONUtil.toJsonStr(productSeriesList)));
                }
                Map<String, List<ProductFamilyCalculationPowerMaintenanceDTO>> checkProductFamilyCalculationPowerMaintenanceMap = productFamilyCalculationPowerMaintenanceDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductFamily(),dto.getInstallType(), String.valueOf(dto.getYear()))));
                Map<String, List<HorizontalOrVerticalDTO>> horizontalOrVerticalDTOMap = horizontalOrVerticalDTOS.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getProductSeries(), String.valueOf(dto.getYear()))));
                savelist.forEach(originCapacityIe -> {
                    /* Optional<ProductInfo> first = byProductGroups.stream().filter(i -> i.getProductGroup().equals(originCapacityIe.getProductGroup())).findFirst();
                    if(!first.isPresent()){
                        throw new BizException(String.format("产品族[%s],产品信息对照表关系不存在，请检查数据！", originCapacityIe.getProductGroup()));
                    } */
                    List<ProductFamilyCalculationPowerMaintenanceDTO> horizontalList = checkProductFamilyCalculationPowerMaintenanceMap.get(String.join(DEFAULT_REGEX, originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup(), QPConstant.InstallType.NONSTANDARD.getValue(), String.valueOf(originCapacityIe.getYear())));
                    List<ProductFamilyCalculationPowerMaintenanceDTO> verticalList = checkProductFamilyCalculationPowerMaintenanceMap.get(String.join(DEFAULT_REGEX, originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup(), QPConstant.InstallType.VERTICAL.getValue(), String.valueOf(originCapacityIe.getYear())));
                    List<HorizontalOrVerticalDTO> horizontalOrVerticalDTOList = horizontalOrVerticalDTOMap.get(String.join(DEFAULT_REGEX, originCapacityIe.getCountryFlag(), originCapacityIe.getProductSeries(), String.valueOf(originCapacityIe.getYear())));
                    if(CollectionUtils.isEmpty(verticalList) || CollectionUtils.isEmpty(horizontalList)){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品族[%s],获取产品族横装或竖装测算功率失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }
                    if(CollectionUtils.isEmpty(horizontalOrVerticalDTOList)){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品系列[%s]获取横竖装比例失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }
                    ProductFamilyCalculationPowerMaintenanceDTO horizontalDTO = horizontalList.get(0);
                    ProductFamilyCalculationPowerMaintenanceDTO verticaLDTO = verticalList.get(0);
                    HorizontalOrVerticalDTO horizontalOrVerticalDTO = horizontalOrVerticalDTOList.get(0);

                    if(Objects.isNull(horizontalOrVerticalDTO.getQ1Percent())){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品系列[%s]获取Q1横竖装比例失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }
                    if(Objects.isNull(horizontalOrVerticalDTO.getQ2Percent())){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品系列[%s]获取Q2横竖装比例失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }
                    if(Objects.isNull(horizontalOrVerticalDTO.getQ3Percent())){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品系列[%s]获取Q3横竖装比例失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }
                    if(Objects.isNull(horizontalOrVerticalDTO.getQ4Percent())){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、产品系列[%s]获取Q4横竖装比例失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductGroup()));
                    }

                    originCapacityIe.setM1Quantity(originCapacityIe.getM1Quantity().multiply(horizontalDTO.getM1Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ1Percent()))).add(verticaLDTO.getM1Quantity().multiply(horizontalOrVerticalDTO.getQ1Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM2Quantity(originCapacityIe.getM2Quantity().multiply(horizontalDTO.getM2Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ1Percent()))).add(verticaLDTO.getM2Quantity().multiply(horizontalOrVerticalDTO.getQ1Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM3Quantity(originCapacityIe.getM3Quantity().multiply(horizontalDTO.getM3Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ1Percent()))).add(verticaLDTO.getM3Quantity().multiply(horizontalOrVerticalDTO.getQ1Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM4Quantity(originCapacityIe.getM4Quantity().multiply(horizontalDTO.getM4Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ2Percent()))).add(verticaLDTO.getM4Quantity().multiply(horizontalOrVerticalDTO.getQ2Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM5Quantity(originCapacityIe.getM5Quantity().multiply(horizontalDTO.getM5Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ2Percent()))).add(verticaLDTO.getM5Quantity().multiply(horizontalOrVerticalDTO.getQ2Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM6Quantity(originCapacityIe.getM6Quantity().multiply(horizontalDTO.getM6Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ2Percent()))).add(verticaLDTO.getM6Quantity().multiply(horizontalOrVerticalDTO.getQ2Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM7Quantity(originCapacityIe.getM7Quantity().multiply(horizontalDTO.getM7Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ3Percent()))).add(verticaLDTO.getM7Quantity().multiply(horizontalOrVerticalDTO.getQ3Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM8Quantity(originCapacityIe.getM8Quantity().multiply(horizontalDTO.getM8Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ3Percent()))).add(verticaLDTO.getM8Quantity().multiply(horizontalOrVerticalDTO.getQ3Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM9Quantity(originCapacityIe.getM9Quantity().multiply(horizontalDTO.getM9Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ3Percent()))).add(verticaLDTO.getM9Quantity().multiply(horizontalOrVerticalDTO.getQ3Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM10Quantity(originCapacityIe.getM10Quantity().multiply(horizontalDTO.getM10Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ4Percent()))).add(verticaLDTO.getM10Quantity().multiply(horizontalOrVerticalDTO.getQ4Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM11Quantity(originCapacityIe.getM11Quantity().multiply(horizontalDTO.getM11Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ4Percent()))).add(verticaLDTO.getM11Quantity().multiply(horizontalOrVerticalDTO.getQ4Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setM12Quantity(originCapacityIe.getM12Quantity().multiply(horizontalDTO.getM12Quantity().multiply((MathUtils.subtractBigDecimal(BigDecimal.ONE, horizontalOrVerticalDTO.getQ4Percent()))).add(verticaLDTO.getM12Quantity().multiply(horizontalOrVerticalDTO.getQ4Percent()))).divide(BigDecimal.valueOf(100),6, RoundingMode.HALF_UP));
                    originCapacityIe.setDataVersion(salCode);
//
                    originCapacityIe.setProductType(ProductTypeEnum.MODULE.getCode());
                });
            }



            // 产品类型 ： 电池 CELL
            if (StringUtils.isNoneBlank(productType) && ProductTypeEnum.CELL.getDesc().equals(productType)) {
                List<CellWaferWeightMaintenanceDTO> celList = getCellWaferWeightMaintenanceList(yearList,ProductTypeEnum.CELL);
                Map<String, List<CellWaferWeightMaintenanceDTO>> cellMap = celList.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getCellModel(),dto.getCellShard(), String.valueOf(dto.getYear()))));
                savelist.forEach(originCapacityIe -> {
                    String cellModel = originCapacityIe.getProductSeries();
                    String cellShard = allByHeaderCode.get(StringUtils.isNotBlank(originCapacityIe.getFragmentType()) ? originCapacityIe.getFragmentType() : "二分片").getLovValue();
                    List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS = cellMap.get(String.join(DEFAULT_REGEX, originCapacityIe.getCountryFlag(), cellModel,cellShard, String.valueOf(originCapacityIe.getYear())));
                    if(CollectionUtils.isEmpty(cellWaferWeightMaintenanceDTOS)){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、电池型号[%s],分片方式[%s]获取电池片兆瓦系数失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductSeries(),originCapacityIe.getFragmentType()));
                    }
                    CellWaferWeightMaintenanceDTO cellWaferWeightMaintenanceDTO = cellWaferWeightMaintenanceDTOS.get(0);

                    if (Objects.nonNull(originCapacityIe.getM1Quantity()) && originCapacityIe.getM1Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM1Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM1Quantity()))) {
                        throw new BizException("电池片兆瓦系数一月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM1Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM1Quantity())){
                         //originCapacityIe.setM1Quantity(originCapacityIe.getM1Quantity().divide(cellWaferWeightMaintenanceDTO.getM1Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate1(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM1Quantity(originCapacityIe.getM1Quantity().divide(cellWaferWeightMaintenanceDTO.getM1Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM2Quantity()) && originCapacityIe.getM2Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM2Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM2Quantity()))) {
                        throw new BizException("电池片兆瓦系数二月为空，无法进行计算");
                    } else if (Objects.nonNull(originCapacityIe.getM2Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM2Quantity())){
                        //originCapacityIe.setM2Quantity(originCapacityIe.getM2Quantity().divide(cellWaferWeightMaintenanceDTO.getM2Quantity(),6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate2(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM2Quantity(originCapacityIe.getM2Quantity().divide(cellWaferWeightMaintenanceDTO.getM2Quantity(),6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM3Quantity()) && originCapacityIe.getM3Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM3Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM3Quantity()))) {
                        throw new BizException("电池片兆瓦系数三月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM3Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM3Quantity())){
                        //originCapacityIe.setM3Quantity(originCapacityIe.getM3Quantity().divide(cellWaferWeightMaintenanceDTO.getM3Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate3(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM3Quantity(originCapacityIe.getM3Quantity().divide(cellWaferWeightMaintenanceDTO.getM3Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM4Quantity()) && originCapacityIe.getM4Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM4Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM4Quantity()))) {
                        throw new BizException("电池片兆瓦系数四月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM4Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM4Quantity())) {
                        //originCapacityIe.setM4Quantity(originCapacityIe.getM4Quantity().divide(cellWaferWeightMaintenanceDTO.getM4Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate4(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM4Quantity(originCapacityIe.getM4Quantity().divide(cellWaferWeightMaintenanceDTO.getM4Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM5Quantity()) && originCapacityIe.getM5Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM5Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM5Quantity()))) {
                        throw new BizException("电池片兆瓦系数五月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM5Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM5Quantity())){
                        //originCapacityIe.setM5Quantity(originCapacityIe.getM5Quantity().divide(cellWaferWeightMaintenanceDTO.getM5Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate5(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM5Quantity(originCapacityIe.getM5Quantity().divide(cellWaferWeightMaintenanceDTO.getM5Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM6Quantity()) && originCapacityIe.getM6Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM6Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM6Quantity()))) {
                        throw new BizException("电池片兆瓦系数六月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM6Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM6Quantity())){
                        //originCapacityIe.setM5Quantity(originCapacityIe.getM6Quantity().divide(cellWaferWeightMaintenanceDTO.getM6Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate6(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM5Quantity(originCapacityIe.getM6Quantity().divide(cellWaferWeightMaintenanceDTO.getM6Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM7Quantity()) && originCapacityIe.getM7Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM7Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM7Quantity()))) {
                        throw new BizException("电池片兆瓦系数七月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM7Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM7Quantity())){
                        //originCapacityIe.setM7Quantity(originCapacityIe.getM7Quantity().divide(cellWaferWeightMaintenanceDTO.getM7Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate7(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM7Quantity(originCapacityIe.getM7Quantity().divide(cellWaferWeightMaintenanceDTO.getM7Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM8Quantity()) && originCapacityIe.getM8Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM8Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM8Quantity()))) {
                        throw new BizException("电池片兆瓦系数八月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM8Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM8Quantity())){
                        //originCapacityIe.setM8Quantity(originCapacityIe.getM8Quantity().divide(cellWaferWeightMaintenanceDTO.getM8Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate8(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM8Quantity(originCapacityIe.getM8Quantity().divide(cellWaferWeightMaintenanceDTO.getM8Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM9Quantity()) && originCapacityIe.getM9Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM9Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM9Quantity()))) {
                        throw new BizException("电池片兆瓦系数九月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM9Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM9Quantity())){
                        //originCapacityIe.setM9Quantity(originCapacityIe.getM9Quantity().divide(cellWaferWeightMaintenanceDTO.getM9Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate9(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM9Quantity(originCapacityIe.getM9Quantity().divide(cellWaferWeightMaintenanceDTO.getM9Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM10Quantity()) && originCapacityIe.getM10Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM10Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM10Quantity()))) {
                        throw new BizException("电池片兆瓦系数十月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM10Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM10Quantity())){
                        //originCapacityIe.setM10Quantity(originCapacityIe.getM10Quantity().divide(cellWaferWeightMaintenanceDTO.getM10Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate10(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM10Quantity(originCapacityIe.getM10Quantity().divide(cellWaferWeightMaintenanceDTO.getM10Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM11Quantity()) && originCapacityIe.getM11Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM11Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM11Quantity()))) {
                        throw new BizException("电池片兆瓦系数十一月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM11Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM11Quantity())){
                        //originCapacityIe.setM11Quantity(originCapacityIe.getM11Quantity().divide(cellWaferWeightMaintenanceDTO.getM11Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate11(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM11Quantity(originCapacityIe.getM11Quantity().divide(cellWaferWeightMaintenanceDTO.getM11Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM12Quantity()) && originCapacityIe.getM12Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM12Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM12Quantity()))) {
                        throw new BizException("电池片兆瓦系数十二月为空，无法进行计算");
                    } else if (Objects.nonNull(originCapacityIe.getM12Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM12Quantity())){
                        //originCapacityIe.setM12Quantity(originCapacityIe.getM12Quantity().divide(cellWaferWeightMaintenanceDTO.getM12Quantity(), 6, RoundingMode.HALF_UP).divide(cellWaferWeightMaintenanceDTO.getAverageYieldRate12(), 6, RoundingMode.HALF_UP));
                        originCapacityIe.setM12Quantity(originCapacityIe.getM12Quantity().divide(cellWaferWeightMaintenanceDTO.getM12Quantity(), 6, RoundingMode.HALF_UP));
                    }
                    originCapacityIe.setDataVersion(salCode);
                    originCapacityIe.setProductType(ProductTypeEnum.CELL.getCode());
                });
            }
            //产品类型 ： 硅片 WAFER
            if (StringUtils.isNoneBlank(productType) && ProductTypeEnum.WAFER.getDesc().equals(productType)) {
                Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMaintenanceDTOMap = waferList.stream().collect(Collectors.groupingBy(dto -> String.join(DEFAULT_REGEX, dto.getCountryFlag(), dto.getCellModel(), String.valueOf(dto.getYear()))));
                savelist.forEach(originCapacityIe -> {
                    List<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS = cellWaferWeightMaintenanceDTOMap.get(String.join(DEFAULT_REGEX, originCapacityIe.getCountryFlag(), originCapacityIe.getProductSeries(), String.valueOf(originCapacityIe.getYear())));
                    if(CollectionUtils.isEmpty(cellWaferWeightMaintenanceDTOS)){
                        throw new BizException(String.format("根据年份[%s]、国内海外[%s]、电池型号[%s],获取硅片兆瓦系数失败，请检查数据！",originCapacityIe.getYear(), originCapacityIe.getCountryFlag(), originCapacityIe.getProductSeries()));
                    }
                    CellWaferWeightMaintenanceDTO cellWaferWeightMaintenanceDTO = cellWaferWeightMaintenanceDTOS.get(0);
                    if (Objects.nonNull(originCapacityIe.getM1Quantity()) && originCapacityIe.getM1Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM1Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM1Quantity()))) {
                        throw new BizException("硅片兆瓦系数一月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM1Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM1Quantity())){
                        originCapacityIe.setM1Quantity(originCapacityIe.getM1Quantity().divide(cellWaferWeightMaintenanceDTO.getM1Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM2Quantity()) && originCapacityIe.getM2Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM2Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM2Quantity()))) {
                        throw new BizException("硅片兆瓦系数二月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM2Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM2Quantity())){
                        originCapacityIe.setM2Quantity(originCapacityIe.getM2Quantity().divide(cellWaferWeightMaintenanceDTO.getM2Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM3Quantity()) && originCapacityIe.getM3Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM3Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM3Quantity()))) {
                        throw new BizException("硅片兆瓦系数三月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM3Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM3Quantity())){
                        originCapacityIe.setM3Quantity(originCapacityIe.getM3Quantity().divide(cellWaferWeightMaintenanceDTO.getM3Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM4Quantity()) && originCapacityIe.getM4Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM4Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM4Quantity()))) {
                        throw new BizException("硅片兆瓦系数四月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM4Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM4Quantity())){
                        originCapacityIe.setM4Quantity(originCapacityIe.getM4Quantity().divide(cellWaferWeightMaintenanceDTO.getM4Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM5Quantity()) && originCapacityIe.getM5Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM5Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM5Quantity()))) {
                        throw new BizException("硅片兆瓦系数五月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM5Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM5Quantity())){
                        originCapacityIe.setM5Quantity(originCapacityIe.getM5Quantity().divide(cellWaferWeightMaintenanceDTO.getM5Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM6Quantity()) && originCapacityIe.getM6Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM6Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM6Quantity()))) {
                        throw new BizException("硅片兆瓦系数六月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM6Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM6Quantity())){
                        originCapacityIe.setM6Quantity(originCapacityIe.getM6Quantity().divide(cellWaferWeightMaintenanceDTO.getM6Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM7Quantity()) && originCapacityIe.getM7Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM7Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM7Quantity()))) {
                        throw new BizException("硅片兆瓦系数七月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM7Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM7Quantity())){
                        originCapacityIe.setM7Quantity(originCapacityIe.getM7Quantity().divide(cellWaferWeightMaintenanceDTO.getM7Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM8Quantity()) && originCapacityIe.getM8Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM8Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM8Quantity()))) {
                        throw new BizException("硅片兆瓦系数八月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM8Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM8Quantity())){
                        originCapacityIe.setM8Quantity(originCapacityIe.getM8Quantity().divide(cellWaferWeightMaintenanceDTO.getM8Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM9Quantity()) && originCapacityIe.getM9Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM9Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM9Quantity()))) {
                        throw new BizException("硅片兆瓦系数九月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM9Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM9Quantity())){
                        originCapacityIe.setM9Quantity(originCapacityIe.getM9Quantity().divide(cellWaferWeightMaintenanceDTO.getM9Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM10Quantity()) && originCapacityIe.getM10Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM10Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM10Quantity()))) {
                        throw new BizException("硅片兆瓦系数十月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM10Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM10Quantity())){
                        originCapacityIe.setM10Quantity(originCapacityIe.getM10Quantity().divide(cellWaferWeightMaintenanceDTO.getM10Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM11Quantity()) && originCapacityIe.getM11Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM11Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM11Quantity()))) {
                        throw new BizException("硅片兆瓦系数十一月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM11Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM11Quantity())){
                        originCapacityIe.setM11Quantity(originCapacityIe.getM11Quantity().divide(cellWaferWeightMaintenanceDTO.getM11Quantity(), 6, RoundingMode.HALF_UP));
                    }

                    if (Objects.nonNull(originCapacityIe.getM12Quantity()) && originCapacityIe.getM12Quantity().compareTo(BigDecimal.ZERO) > 0 && (cellWaferWeightMaintenanceDTO.getM12Quantity() == null || MathUtils.equalToZero(cellWaferWeightMaintenanceDTO.getM12Quantity()))) {
                        throw new BizException("硅片兆瓦系数十二月为空，无法进行计算");
                    } else if(Objects.nonNull(originCapacityIe.getM12Quantity()) && !MathUtils.equalToZero(originCapacityIe.getM12Quantity())){
                        originCapacityIe.setM12Quantity(originCapacityIe.getM12Quantity().divide(cellWaferWeightMaintenanceDTO.getM12Quantity(), 6, RoundingMode.HALF_UP));
                    }
                    originCapacityIe.setDataVersion(salCode);
                    originCapacityIe.setProductType(ProductTypeEnum.WAFER.getCode());
                });
            }
//            return null;

            List<OriginCapacityIe> originCapacityIeList = repository.saveAll(savelist);
        }

        return "SUCCESS:" + salCode;

    }

    public List<CellWaferWeightMaintenanceDTO> getCellWaferWeightMaintenanceList(List<Integer> years, ProductTypeEnum productTypeEnum) {
        CellWaferWeightMaintenanceQuery query = new CellWaferWeightMaintenanceQuery();
        query.setYears(years);
        query.setProductType(productTypeEnum);
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        Page<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMaintenanceService.queryByPage(query);
        return maintenanceDTOS.getContent();
    }


    private List<ProductFamilyCalculationPowerMaintenanceDTO> getProductFamilyCalculationPowerMaintenanceDTO(List<String> productGroupList) {
        QProductFamilyCalculationPowerMaintenance qProductFamilyCalculationPowerMaintenance = QProductFamilyCalculationPowerMaintenance.productFamilyCalculationPowerMaintenance;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ProductFamilyCalculationPowerMaintenanceDTO> jpaQuery = jpaQueryFactory.select(Projections.fields(ProductFamilyCalculationPowerMaintenanceDTO.class, qProductFamilyCalculationPowerMaintenance.id.as("id"), qProductFamilyCalculationPowerMaintenance.countryFlag.as("countryFlag"), qProductFamilyCalculationPowerMaintenance.productFamily.as("productFamily"), qProductFamilyCalculationPowerMaintenance.installType.as("installType"), qProductFamilyCalculationPowerMaintenance.year.as("year"), qProductFamilyCalculationPowerMaintenance.m1Quantity.as("m1Quantity"), qProductFamilyCalculationPowerMaintenance.m2Quantity.as("m2Quantity"), qProductFamilyCalculationPowerMaintenance.m3Quantity.as("m3Quantity"), qProductFamilyCalculationPowerMaintenance.m4Quantity.as("m4Quantity"), qProductFamilyCalculationPowerMaintenance.m5Quantity.as("m5Quantity"), qProductFamilyCalculationPowerMaintenance.m6Quantity.as("m6Quantity"), qProductFamilyCalculationPowerMaintenance.m7Quantity.as("m7Quantity"), qProductFamilyCalculationPowerMaintenance.m8Quantity.as("m8Quantity"), qProductFamilyCalculationPowerMaintenance.m9Quantity.as("m9Quantity"), qProductFamilyCalculationPowerMaintenance.m10Quantity.as("m10Quantity"), qProductFamilyCalculationPowerMaintenance.m11Quantity.as("m11Quantity"), qProductFamilyCalculationPowerMaintenance.m12Quantity.as("m12Quantity"))).from(qProductFamilyCalculationPowerMaintenance);
        jpaQuery.where(qProductFamilyCalculationPowerMaintenance.productFamily.in(productGroupList));
        jpaQuery.where(qProductFamilyCalculationPowerMaintenance.isDeleted.eq(DeleteEnum.NO.getCode()));
        return org.apache.commons.collections4.IterableUtils.toList(jpaQuery.fetch());
    }

    public List<OriginCapacityIe> queryAllOriginCapacity(OriginCapacityIeQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        return jpaQueryFactory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(query.getDataVersion()).and(originCapacityIe.dataFrom.eq(query.getDataFrom().getCode()))).fetch();
    }

    /**
     * 按年份查询组件满产产能
     *
     * @param year
     * @return
     */
    @Override
    public List<String> queryModuleVersionByYear(Integer year) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.select(originCapacityIe.dataVersion).from(originCapacityIe).where(originCapacityIe.year.eq(year).and(originCapacityIe.productType.eq(ProductTypeEnum.MODULE.getCode())).and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()))).fetch();
    }

    /**
     * 按年份查询组件满产产能
     *
     * @param year
     * @return
     */
    @Override
    public List<String> queryVersionNewByYearAndProductType(Integer year ,String productType) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.select(originCapacityIe.dataVersion).from(originCapacityIe).where(originCapacityIe.year.eq(year).and(originCapacityIe.productType.eq(productType))).orderBy(originCapacityIe.updatedTime.desc()).fetch();
    }

    /**
     * 版本号查原始产能
     *
     * @param dataVersion
     * @return
     */
    @Override
    public List<OriginCapacityIe> findByDataVersion(String dataVersion) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(dataVersion)).fetch();
    }

    /**
     * 通过版本号和产品大类查询产能
     *
     * @param dataVersion
     * @param productType
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryByVersionAndTypeAndCountryYear(String dataVersion, ProductTypeEnum productType, CountryFlagEnum countryFlag, Integer year) {
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQuery<OriginCapacityIe> where = factory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(dataVersion).and(originCapacityIe.productType.eq(productType.getCode())));
        if (ObjectUtils.isNotEmpty(countryFlag)) {
            where.where(originCapacityIe.countryFlag.eq(countryFlag.getCode()));
        }
        if (ObjectUtils.isNotEmpty(year)) {
            where.where(originCapacityIe.year.eq(year));
        }
        return where.fetch();
    }

    /**
     * 条件查询产能数据
     *
     * @param builder
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryCapacityByBuilder(BooleanBuilder builder) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.selectFrom(originCapacityIe).where(builder).fetch();
    }

    /**
     * 根据年份和产品大类查询最新满产产能的版本号
     *
     * @param year
     * @param productType
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryOriginCapacityByYearAndTypeAndCountry(Integer year, ProductTypeEnum productType, CountryFlagEnum countryFlag) {

        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        List<String> fetch = factory.select(originCapacityIe.dataVersion).from(originCapacityIe).where(originCapacityIe.productType.eq(productType.getCode()).and(originCapacityIe.year.eq(year)).and(originCapacityIe.countryFlag.eq(countryFlag.getCode())).and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()))).orderBy(originCapacityIe.updatedTime.desc()).fetch();
        if (CollectionUtils.isEmpty(fetch)) {
            return null;
//            throw new BizException(String.format("%s in %s has empty origin capacity", productType.getCode(), year));
        }
        return factory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(fetch.get(0))
                .and(originCapacityIe.countryFlag.eq(countryFlag.getCode()))
                .and(originCapacityIe.productType.eq(productType.getCode()))).fetch();
    }

    /**
     * 根据年份和产品大类查询最新满产产能的版本号
     *
     * @param year
     * @param productType
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryOriginCapacityByParams(Integer year, ProductTypeEnum productType, CountryFlagEnum countryFlag, String productSeries, String productFamily, List<String> workshopList) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        JPAQuery<String> jpaQuery = factory.select(originCapacityIe.dataVersion).from(originCapacityIe)
                .where(originCapacityIe.productType.eq(productType.getCode())
                        .and(originCapacityIe.year.eq(year))
                        .and(originCapacityIe.countryFlag.eq(countryFlag.getCode()))
                        .and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()))
                        .and(originCapacityIe.workshop.in(workshopList)));
        if (ProductTypeEnum.MODULE.equals(productType)) {
            jpaQuery.where(originCapacityIe.productSeries.eq(productSeries));
            jpaQuery.where(originCapacityIe.productGroup.eq(productFamily));
        } else {
            jpaQuery.where(originCapacityIe.productSeries.eq(productSeries));
        }
        List<String> fetch = jpaQuery.orderBy(originCapacityIe.updatedTime.desc()).fetch();

        if (CollectionUtils.isEmpty(fetch)) {
            log.error(String.format("%s in %s has empty origin capacity", productType.getCode(), year));
            return Lists.newArrayList();
        }

        JPAQuery<OriginCapacityIe> ieJPAQuery = factory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(fetch.get(0))
                .and(originCapacityIe.year.eq(year))
                .and(originCapacityIe.countryFlag.eq(countryFlag.getCode()))
                .and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()))
                .and(originCapacityIe.productType.eq(productType.getCode()))
                .and(originCapacityIe.workshop.in(workshopList)));
        if (ProductTypeEnum.MODULE.equals(productType)) {
            ieJPAQuery.where(originCapacityIe.productSeries.eq(productSeries));
            ieJPAQuery.where(originCapacityIe.productGroup.eq(productFamily));
        } else {
            ieJPAQuery.where(originCapacityIe.productSeries.eq(productSeries));
        }
        return ieJPAQuery.fetch();
    }

    /**
     * 根据年份和产品大类查询最新的版本号的公布产能
     *
     * @param year
     * @param productType
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryPublishCapacityByYearAndTypeAndCountry(Integer year, ProductTypeEnum productType, CountryFlagEnum countryFlag) {
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        return factory.selectFrom(originCapacityIe).where(originCapacityIe.year.eq(year)
                .and(originCapacityIe.countryFlag.eq(countryFlag.getCode()))
                .and(originCapacityIe.productType.eq(productType.getCode()).and(originCapacityIe.dataType.eq(DataTypeEnum.IE_ANNOUNCED_PRODUCTION.getCode())))).fetch();
    }

    @Override
    public List<OriginCapacityIeDTO> queryByList(OriginCapacityIeQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        BooleanBuilder booleanBuilder = setBooleanBuilder(query, originCapacityIe);
        booleanBuilder.and(originCapacityIe.productType.eq(ProductTypeEnum.MODULE.getCode()).and(originCapacityIe.productFrom.eq(ProductFromEnum.IE.getCode()).and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()))));
        List<OriginCapacityIe> originCapacityIeList = IterableUtils.iterableToList(repository.findAll(booleanBuilder));
        return originCapacityIeDEConvert.toDto(originCapacityIeList);
    }

    @Override
    public List<OriginCapacityIeDTO> findByWorkshopAndYear(OriginCapacityIeQuery query) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        BooleanBuilder booleanBuilder = setBooleanBuilder(query, originCapacityIe).and(originCapacityIe.dataType.eq(DataTypeEnum.IE_FULL_PRODUCTION.getCode()));
        List<OriginCapacityIe> originCapacityIeList = IterableUtils.iterableToList(repository.findAll(booleanBuilder));
        return originCapacityIeDEConvert.toDto(originCapacityIeList);
    }

    private BooleanBuilder setBooleanBuilder(OriginCapacityIeQuery query, QOriginCapacityIe originCapacityIe) {
        BooleanBuilder sql = new BooleanBuilder();
        sql.and(originCapacityIe.isDeleted.eq(0));
        if (query.getYear() != null) {
            sql.and(originCapacityIe.year.eq(query.getYear()));
        }
        if (StringUtils.isNotBlank(query.getProductGroup())) {
            sql.and(originCapacityIe.productGroup.eq(query.getProductGroup()));
        }
        if (CollectionUtils.isNotEmpty(query.getWorkshopList())) {
            sql.and(originCapacityIe.workshop.in(query.getWorkshopList()));
        }
        if (query.getCountryFlag() != null) {
            sql.and(originCapacityIe.countryFlag.eq(query.getCountryFlag().getCode()));
        }
        if (StringUtils.isNotBlank(query.getWorkshop())) {
            sql.and(originCapacityIe.workshop.eq(query.getWorkshop()));
        }
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            sql.and(originCapacityIe.productSeries.eq(query.getProductSeries()));
        }
        if (StringUtils.isNotBlank(query.getDataVersion())) {
            sql.and(originCapacityIe.dataVersion.eq(query.getDataVersion()));
        }
        return sql;
    }

    @SneakyThrows
    @Override
    public void exportData(OriginCapacityIeQuery query, HttpServletResponse response) {
        List<OriginCapacityIe> capacityIeList = queryByPage(query).getContent();
        if (CollectionUtils.isEmpty(capacityIeList)) {
            throw new BizException("数据库中没有该条数据");
        }
        OriginCapacityIe originCapacityIe = capacityIeList.get(0);
        ExcelPara excelPara = query.getExcelPara();
        List<List<Object>> excelData = ExcelUtils.getList(capacityIeList, excelPara);

        ExcelUtils.exportEx(response, "IE" + ProductTypeEnum.getDesc(originCapacityIe.getProductType()) + "产能"+originCapacityIe.getDataVersion()+"_" + DateUtils.formatDate(new Date()), "IE" + ProductTypeEnum.getDesc(originCapacityIe.getProductType()) + "产能", excelPara.getSimpleHeader(), excelData);
//        EasyExcel.write(response.getOutputStream(), OriginCapacityIe.class).excludeColumnFiledNames(set).registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle)).sheet().doWrite(capacityIeList);


    }

    @Override
    public List<String> queryByYear(Integer year) {
        return queryModuleVersionByYear(year).stream().distinct().collect(Collectors.toList());
    }

    /**
     * 通过版本号和产品大类查询产能
     *
     * @param version
     * @param productType
     * @return
     */
    @Override
    public List<OriginCapacityIe> queryByYearAndType(String version, ProductTypeEnum productType) {
        QOriginCapacityIe originCapacityIe = QOriginCapacityIe.originCapacityIe;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        return factory.selectFrom(originCapacityIe).where(originCapacityIe.dataVersion.eq(version).and(originCapacityIe.productType.eq(productType.getCode()))).fetch();
    }

    @Override
    public void capacityComparisonBySeriesExport(ContrastQuery query, HttpServletResponse response) {
        List<ContrastCapacityResultDTO> dtoList = capacityComparisonBySeries(query);
        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        getHeaderData(1, simpleHeader);
        List<List<Object>> objList = ExcelUtils.getList(dtoList, excelPara);
        ExcelUtils.exportEx(response, query.getProductType().getDesc() + "产品系列对比", query.getProductType().getDesc() + "产品系列对比", simpleHeader, objList);
    }

    @Override
    public void capacityComparisonByBasePlaceExport(ContrastQuery query, HttpServletResponse response) {
        List<ContrastCapacityResultDTO> dtoList = capacityComparisonByBasePlace(query);
        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        getHeaderData(1, simpleHeader);
        List<List<Object>> objList = ExcelUtils.getList(dtoList, excelPara);
        ExcelUtils.exportEx(response, query.getProductType().getDesc() + "基地产能对比", query.getProductType().getDesc() + "基地产能对比", simpleHeader, objList);
    }

    @Override
    public void capacityComparisonByTwoExport(ContrastQuery query, HttpServletResponse response) {
        List<ContrastCapacityResultDTO> dtoList = capacityComparisonByTwo(query);
        ExcelPara excelPara = query.getExcelPara();
        List<List<String>> simpleHeader = excelPara.getSimpleHeader();
        getHeaderData(2, simpleHeader);
        List<List<Object>> objList = ExcelUtils.getList(dtoList, excelPara);
        ExcelUtils.exportEx(response, query.getProductType().getDesc() + "基地+产品系列对比", query.getProductType().getDesc() + "基地+产品系列对比", simpleHeader, objList);
    }

    @Override
    public List<OriginalCapacityIeExtDTO> getMaxDateData(OriginCapacityIeQuery query) {
        String dataType = "1";
        if (StringUtils.isNotBlank(query.getDataType())) {
            dataType = query.getDataType();
        }
        OriginCapacityIe originCapacityIe = repository.findFirstByYearAndProductTypeAndDataTypeAndProductFromAndIsIEConfirmOrderByCreatedTimeDesc(query.getYear(), "MODULE", dataType, "IE", YesOrNoEnums.Y.getCode());
        query.setDataVersion(originCapacityIe.getDataVersion());
        List<Map<String, Object>> mapList = repository.getMaxDateData(query);
        List<OriginalCapacityIeExtDTO> list = Lists.newArrayList();
        for (Map<String, Object> map : mapList) {
            OriginalCapacityIeExtDTO dto = new OriginalCapacityIeExtDTO();
            BeanUtil.fillBeanWithMap(map, dto, false);

            list.add(dto);
        }
        return list;
    }

    private void getHeaderData(int row, List<List<String>> simpleHeader) {
        for (int i = 0; i < simpleHeader.size(); i++) {
            List<String> headList = simpleHeader.get(i);
            if (i < row) {
                headList.add(0, "月份");
            } else if (i < row + 3 * 1) {
                headList.add(0, "January");
            } else if (i < row + 3 * 2) {
                headList.add(0, "February");
            } else if (i < row + 3 * 3) {
                headList.add(0, "March");
            } else if (i < row + 3 * 4) {
                headList.add(0, "April");
            } else if (i < row + 3 * 5) {
                headList.add(0, "May");
            } else if (i < row + 3 * 6) {
                headList.add(0, "June");
            } else if (i < row + 3 * 7) {
                headList.add(0, "July");
            } else if (i < row + 3 * 8) {
                headList.add(0, "August");
            } else if (i < row + 3 * 9) {
                headList.add(0, "September");
            } else if (i < row + 3 * 10) {
                headList.add(0, "October");
            } else if (i < row + 3 * 11) {
                headList.add(0, "November");
            } else if (i < row + 3 * 12) {
                headList.add(0, "December");
            }
        }
    }

    /**
     * 版本号删除
     *
     * @param dataVersion
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteByVersion(String dataVersion) {
        repository.deleteAllByDataVersion(dataVersion);
        return "删除成功";
    }

    @Override
    public String batchSaveExcel(List<OriginCapacityIeImportExcelDTO> originCapacityIeImportExcelDTOList, String dataType, String productType, String remark) {
        if (CollectionUtils.isEmpty(originCapacityIeImportExcelDTOList)) {
            throw new BizException("请导入数据");
        }
        //校验
        ValidUtils.valid(validator, originCapacityIeImportExcelDTOList, new ValidUtils.ValidationResult() {
            @Override
            public <T> void process(Map<Integer, Set<ConstraintViolation<T>>> resultMap) {
                if (resultMap != null && !resultMap.isEmpty()) {
                    StringBuilder sb = new StringBuilder();
                    resultMap.forEach((index, value) -> sb.append(String.format("第【%d】行数据检验不通过:", index + 2)).append(
                            value.stream().map(ConstraintViolation::getMessage).collect(Collectors.joining(","))).append(";\n"));
                    throw new BizException(sb.toString());
                }
            }
        });
        //year 检验
        Set<String> yearSet = originCapacityIeImportExcelDTOList.stream().map(OriginCapacityIeImportExcelDTO::getYear).collect(Collectors.toSet());
        if (yearSet.size() > 1) {
            throw new BizException(String.format("数据检验不通过：存在不同年份数据【%s】", String.join(",", yearSet)));
        }
        List<OriginCapacityIe> originCapacityIes = originCapacityIeDEConvert.toCapacityIe(originCapacityIeImportExcelDTOList);
        return batchSave(originCapacityIes, dataType, productType, remark);
    }
}
