package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.trinasolar.scp.aop.domain.constant.CountryMapping;
import com.trinasolar.scp.aop.domain.constant.TJConstant;
import com.trinasolar.scp.aop.domain.convert.ActualOutputDEConvert;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.dto.aps.CellRelationDTO;
import com.trinasolar.scp.aop.domain.dto.aps.ModuleBasePlaceQuery;
import com.trinasolar.scp.aop.domain.dto.aps.OpActualOutputTempDTO;
import com.trinasolar.scp.aop.domain.dto.tj.TjResponse;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.BigdataTypeEnums;
import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.aop.domain.priority.Priority;
import com.trinasolar.scp.aop.domain.query.ActualOutputQuery;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.query.OrganizationDefinitionsQuery;
import com.trinasolar.scp.aop.domain.save.ActualOutputSaveDTO;
import com.trinasolar.scp.aop.domain.utils.MathUtils;
import com.trinasolar.scp.aop.service.feign.client.ApsFeignClient;
import com.trinasolar.scp.aop.service.feign.client.PowerFeignClient;
import com.trinasolar.scp.aop.service.feign.client.SystemFeign;
import com.trinasolar.scp.aop.service.repository.ActualOtherRepository;
import com.trinasolar.scp.aop.service.repository.ActualOutputRepository;
import com.trinasolar.scp.aop.service.repository.ActualOutputTempRepository;
import com.trinasolar.scp.aop.service.repository.AopThirdAModuleProduceRepository;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.aop.service.utils.AdjustConstant;
import com.trinasolar.scp.aop.service.utils.FeignReturnsParameterUtils;
import com.trinasolar.scp.aop.service.utils.IterableUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.base.LovLineQuery;
import com.trinasolar.scp.aop.domain.enums.CountryFlagEnum;
import com.trinasolar.scp.common.api.enums.DeleteEnum;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import com.trinasolar.scp.common.api.util.SpringContextUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.*;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 实际产出
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-05-25 09:21:03
 */
@Slf4j
@Service("actualOutputService")
public class ActualOutputServiceImpl implements ActualOutputService {
    @Autowired
    ActualOutputRepository repository;
    @Autowired
    ActualOutputTempRepository actualOutputTempRepository;

    @PersistenceContext
    EntityManager entityManager;
    @Autowired
    ProductInfoService productInfoService;
    @Autowired
    ActualOutputDEConvert actualOutputDEConvert;

    @Autowired
    private ApsFeignClient apsFeignClient;

    @Autowired
    @Lazy
     CellWaferWeightService cellWaferWeightService;

    @Autowired
    ActualOtherService actualOtherService;

    @Autowired
    BigdataApiTypeFactory bigdataApiTypeFactory;

    @Autowired
    private CellWaferWeightMaintenanceService cellWaferWeightMaintenanceService;

    @Autowired
    private AopThirdAModuleProduceRepository aopThirdAModuleProduceRepository;

    @Autowired
    private PowerFeignClient powerFeignClient;

    @Autowired
    ActualOtherRepository actualOtherRepository;

    /**
     * 电池/硅片 分页
     *
     * @param query
     * @return
     */
    @Override
    public Page<ActualOutputDTO> queryByPage(ActualOutputQuery query) {
        QActualOutput qActualOutput = QActualOutput.actualOutput;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ActualOutputDTO> fromSql = jpaQueryFactory.select(Projections.fields(ActualOutputDTO.class,
                qActualOutput.year.as("year"),
                qActualOutput.productSeries.as("productSeries"),
                qActualOutput.productGroup.as("productGroup"),
                qActualOutput.productType.as("productType"),
                qActualOutput.productFrom.as("productFrom"),
                qActualOutput.countryFlag.as("countryFlag"),
                qActualOutput.workshop.as("workshop"),
                qActualOutput.cellShard.as("cellShard"),
                qActualOutput.area.as("area"),
                qActualOutput.basePlace.as("basePlace"),
                qActualOutput.m1Quantity.sum().as("m1Quantity"),
                qActualOutput.m2Quantity.sum().as("m2Quantity"),
                qActualOutput.m3Quantity.sum().as("m3Quantity"),
                qActualOutput.m4Quantity.sum().as("m4Quantity"),
                qActualOutput.m5Quantity.sum().as("m5Quantity"),
                qActualOutput.m6Quantity.sum().as("m6Quantity"),
                qActualOutput.m7Quantity.sum().as("m7Quantity"),
                qActualOutput.m8Quantity.sum().as("m8Quantity"),
                qActualOutput.m9Quantity.sum().as("m9Quantity"),
                qActualOutput.m10Quantity.sum().as("m10Quantity"),
                qActualOutput.m11Quantity.sum().as("m11Quantity"),
                qActualOutput.m12Quantity.sum().as("m12Quantity")
        )).from(qActualOutput);
        BooleanBuilder builder = new BooleanBuilder();
        setFromSql(query, fromSql, builder);

        JPAQuery<ActualOutputDTO> actualOutputDTOJPAQuery = fromSql.groupBy(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.area, qActualOutput.countryFlag, qActualOutput.workshop, qActualOutput.productGroup, qActualOutput.productFrom, qActualOutput.cellShard)
                .orderBy(qActualOutput.basePlace.asc().nullsLast(), qActualOutput.year.asc().nullsLast(),
                        qActualOutput.productSeries.asc().nullsLast(), qActualOutput.area.asc().nullsLast(),
                        qActualOutput.countryFlag.asc().nullsLast(), qActualOutput.workshop.asc().nullsLast(),
                        qActualOutput.productGroup.asc().nullsLast(), qActualOutput.productFrom.asc().nullsLast(),
                        qActualOutput.cellShard.asc().nullsLast());

        List<ActualOutputDTO> fetch = actualOutputDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
//        List<ActualOutputDTO> cellList = fetch.stream().filter(item -> ProductTypeEnum.CELL.getCode().equals(item.getProductType())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(cellList)) {
//            //电池万片换算成MW
//            calculateCell(cellList);
//        }
        List<Tuple> list = jpaQueryFactory.select(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.area, qActualOutput.countryFlag, qActualOutput.workshop, qActualOutput.productGroup, qActualOutput.productFrom, qActualOutput.cellShard).from(qActualOutput)
                .where(builder).groupBy(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.area, qActualOutput.countryFlag, qActualOutput.workshop, qActualOutput.productGroup, qActualOutput.productFrom, qActualOutput.cellShard)
                .orderBy(qActualOutput.basePlace.asc().nullsLast(), qActualOutput.year.asc().nullsLast(),
                        qActualOutput.productSeries.asc().nullsLast(), qActualOutput.area.asc().nullsLast(),
                        qActualOutput.countryFlag.asc().nullsLast(), qActualOutput.workshop.asc().nullsLast(),
                        qActualOutput.productGroup.asc().nullsLast(), qActualOutput.productFrom.asc().nullsLast(),
                        qActualOutput.cellShard.asc().nullsLast())
                .fetch();

        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        //电池/硅片 值集转换
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        fetch.forEach(actualOutputDTO -> {
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, actualOutputDTO.getCountryFlag()));
            //电池分片方式
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_CELL_SHARD, actualOutputDTO.getCellShard()));
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(actualOutputDTO -> {
            LovLineDTO lovLineDTO03 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, actualOutputDTO.getCountryFlag()));
            LovLineDTO lovLineDTO01 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_CELL_SHARD, actualOutputDTO.getCellShard()));
            if (Objects.nonNull(lovLineDTO03) && ObjectUtils.isNotEmpty(actualOutputDTO.getCountryFlag())) {
                actualOutputDTO.setCountryFlag(lovLineDTO03.getLovName());
            } else {
                throw new BizException(actualOutputDTO.getCountryFlag() + "值集列表不存在");
            }

            if (ObjectUtils.isNotEmpty(actualOutputDTO.getCellShard())) {
                if (Objects.nonNull(lovLineDTO01)) {
                    actualOutputDTO.setCellShardName(lovLineDTO01.getLovName());
                } else {
                    throw new BizException(actualOutputDTO.getCellShard() + "值集列表不存在");
                }
            }

        });

        setQuantity(fetch);

        return new PageImpl<>(fetch, pageable, list.size());
    }


    /**
     * 组件分页
     *
     * @param query 查询对象
     * @return
     */
    @Override
    public Page<ActualOutputDTO> queryByModulePage(ActualOutputQuery query) {
        QActualOutput qActualOutput = QActualOutput.actualOutput;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ActualOutputDTO> fromSql = jpaQueryFactory
                .select(Projections.fields(
                        ActualOutputDTO.class, qActualOutput.year.as("year"),
                        qActualOutput.productSeries.as("productSeries"),
                        qActualOutput.productGroup.as("productGroup"),
                        qActualOutput.countryFlag.as("countryFlag"),
                        qActualOutput.workshop.as("workshop"),
                        qActualOutput.basePlace.as("basePlace"),
                        qActualOutput.m1Quantity.sum().as("m1Quantity"),
                        qActualOutput.m2Quantity.sum().as("m2Quantity"),
                        qActualOutput.m3Quantity.sum().as("m3Quantity"),
                        qActualOutput.m4Quantity.sum().as("m4Quantity"),
                        qActualOutput.m5Quantity.sum().as("m5Quantity"),
                        qActualOutput.m6Quantity.sum().as("m6Quantity"),
                        qActualOutput.m7Quantity.sum().as("m7Quantity"),
                        qActualOutput.m8Quantity.sum().as("m8Quantity"),
                        qActualOutput.m9Quantity.sum().as("m9Quantity"),
                        qActualOutput.m10Quantity.sum().as("m10Quantity"),
                        qActualOutput.m11Quantity.sum().as("m11Quantity"),
                        qActualOutput.m12Quantity.sum().as("m12Quantity")
                ))
                .from(qActualOutput);
        BooleanBuilder builder = new BooleanBuilder();
        setFromSql(query, fromSql, builder);

        JPAQuery<ActualOutputDTO> actualOutputDTOJPAQuery = fromSql.groupBy(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.productGroup, qActualOutput.countryFlag, qActualOutput.workshop);

        Sort sort = Sort.by(Sort.Direction.DESC, "updatedTime");
        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize(), sort);
        List<ActualOutputDTO> fetch = actualOutputDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();
        List<Tuple> list = jpaQueryFactory.select(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.productGroup, qActualOutput.countryFlag, qActualOutput.workshop).from(qActualOutput).where(builder).groupBy(qActualOutput.basePlace, qActualOutput.year, qActualOutput.productSeries, qActualOutput.productGroup, qActualOutput.countryFlag, qActualOutput.workshop).fetch();
        //组件实际产出 值集转换
        Set<LovLineQuery> lovLineQuerys = Sets.newHashSet();
        fetch.forEach(actualOutputDTO -> {
            //国内/海外
            lovLineQuerys.add(new LovLineQuery().buildWithValue(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, actualOutputDTO.getCountryFlag()));
        });
        Map<String, LovLineDTO> lovMap = LovService.findLovMap(lovLineQuerys);
        fetch.forEach(actualOutputDTO -> {
            LovLineDTO lovLineDTO04 = lovMap.get(StringUtils.join(LovHeaderCodeConstant.AOP_COUNTRY_FLAG, actualOutputDTO.getCountryFlag()));
            if (Objects.nonNull(lovLineDTO04) && ObjectUtils.isNotEmpty(actualOutputDTO.getCountryFlag())) {
                actualOutputDTO.setCountryFlag(lovLineDTO04.getLovName());
            } else {
                throw new BizException(actualOutputDTO.getCountryFlag() + "值集列表不存在");
            }
        });

        setQuantity(fetch);

        return new PageImpl<>(fetch, pageable, list.size());
    }

    private void setQuantity(List<ActualOutputDTO> fetch) {
        for (ActualOutputDTO dto : fetch) {
            dto.setQ1Quantity(MathUtils.addBigDecimal(dto.getM1Quantity(), dto.getM2Quantity(), dto.getM3Quantity()));
            dto.setQ2Quantity(MathUtils.addBigDecimal(dto.getM4Quantity(), dto.getM5Quantity(), dto.getM6Quantity()));
            dto.setQ3Quantity(MathUtils.addBigDecimal(dto.getM7Quantity(), dto.getM8Quantity(), dto.getM9Quantity()));
            dto.setQ4Quantity(MathUtils.addBigDecimal(dto.getM10Quantity(), dto.getM11Quantity(), dto.getM12Quantity()));
            dto.setSummary(MathUtils.addBigDecimal(dto.getQ1Quantity(), dto.getQ2Quantity(), dto.getQ3Quantity(), dto.getQ4Quantity()));
        }
    }

    private void setFromSql(ActualOutputQuery query, JPAQuery<ActualOutputDTO> fromSql, BooleanBuilder builder) {
        QActualOutput qActualOutput = QActualOutput.actualOutput;
        if (StringUtils.isNotBlank(query.getProductType())) {
            fromSql.where(qActualOutput.productType.eq(query.getProductType()));
            builder.and(qActualOutput.productType.eq(query.getProductType()));
        }
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            fromSql.where(qActualOutput.productSeries.eq(query.getProductSeries()));
            builder.and(qActualOutput.productSeries.eq(query.getProductSeries()));
        }
        if (StringUtils.isNotBlank(query.getProductGroup())) {
            builder.and(qActualOutput.productGroup.eq(query.getProductGroup()));
        }
        if (StringUtils.isNotBlank(query.getWorkshop())) {
            fromSql.where(qActualOutput.workshop.eq(query.getWorkshop()));
            builder.and(qActualOutput.workshop.eq(query.getWorkshop()));
        }
        if (query.getYear() != null) {
            fromSql.where(qActualOutput.year.eq(query.getYear()));
            builder.and(qActualOutput.year.eq(query.getYear()));
        }
        if (StringUtils.isNotBlank(query.getProductFrom())) {
            fromSql.where(qActualOutput.productFrom.eq(query.getProductFrom()));
            builder.and(qActualOutput.productFrom.eq(query.getProductFrom()));
        }
    }

    @Override
    public ActualOutputDTO queryById(Long id) {
        ActualOutput queryObj = repository.findById(id).orElse(null);
        if (queryObj == null) {
            return null;
        }

        ActualOutputDTO result = new ActualOutputDTO();
        BeanUtils.copyProperties(queryObj, result);

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ActualOutputDTO save(ActualOutputSaveDTO saveDTO) {
        ActualOutput newObj = Optional.ofNullable(saveDTO.getId())
                .map(id -> repository.getOne(saveDTO.getId()))
                .orElse(new ActualOutput());

        BeanUtils.copyProperties(saveDTO, newObj);
        repository.save(newObj);

        return this.queryById(newObj.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void logicDeleteByIds(List<Long> ids) {
        ids.stream().forEach(id -> repository.deleteById(id));
    }

    /**
     * 通过年份和类型查询实际产出
     *
     * @param year
     * @param productType
     * @return
     */
    @Override
    public List<ActualOutput> queryActualOutputByYearAndTypeAndCountry(Integer year, ProductTypeEnum productType, CountryFlagEnum countryFlagEnum) {
        QActualOutput qActualOutput = QActualOutput.actualOutput;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        JPAQuery<ActualOutput> query = factory.selectFrom(qActualOutput).where(qActualOutput.year.eq(year)
                .and(qActualOutput.productType.eq(productType.getCode())));
        if (ObjectUtils.isNotEmpty(countryFlagEnum)) {
            query.where(qActualOutput.countryFlag.eq(countryFlagEnum.getCode()));
        }
        List<ActualOutput> fetch = query.fetch();
//        if (productType == ProductTypeEnum.CELL) {
//            List<ActualOutputDTO> actualOutputDTOS = actualOutputDEConvert.toActualOutputDTOs(fetch);
//            calculateCell(actualOutputDTOS);
//            fetch = actualOutputDEConvert.toActualOutputs(actualOutputDTOS);
//        }
        return fetch;
    }

    @Override
    public void batchSave(List<ActualOutputDTO> dtoList, String productType) {
        log.info("--------------excel读取数据------------");
        log.info(dtoList.toString());
        if (CollectionUtils.isEmpty(dtoList)) {
            throw new BizException("导入失败");
        }
        if (IterableUtils.checkListData(dtoList)) {
            throw new BizException("Excel中存在重复数据，请检查!");
        }

        //车间 对应 基础产地
        Map<String, ModuleBasePlace> workshopMap = FeignReturnsParameterUtils.queryAllBasePlaceWorkshop();


        List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
        // 存储保存数据的集合
        List<ActualOutput> saveList = Lists.newArrayList();
        List<String> errorList = Lists.newArrayList();
        Integer count = 0;
        //产品系列
        Set<String> waferSizeSet = productInfoList.stream().map(ProductInfo::getWaferModel).collect(Collectors.toSet());
        Set<String> cellModelSet = productInfoList.stream().map(ProductInfo::getCellModel).collect(Collectors.toSet());
        //产品族+产品系列
        Map<String, ProductInfo> productInfoMap = productInfoList.stream().collect(Collectors.toMap(item -> item.getProductGroup() + item.getProductSeries(), Function.identity(), ((v1, v2) -> v1)));
        for (ActualOutputDTO dto : dtoList) {
            count++;
            if (ProductTypeEnum.MODULE.getCode().equals(productType)) {
                //组件
                //检验产品系列
                String k = dto.getProductGroup() + dto.getProductSeries();
                ProductInfo v = productInfoMap.get(k);
                if (v == null) {
                    errorList.add(" 第" + count + "行：" + "产品系列(ProductSeries)值 找不到对应 产品族(productGroup)值  错误！");
                    continue;
                }

            } else {
                //电池/硅片
                if (!waferSizeSet.contains(dto.getProductSeries())) {
                    errorList.add(" 第" + count + "行：" + "产品系列(ProductSeries)值  错误！");
                    continue;
                }

                if (!cellModelSet.contains(dto.getProductSeries())) {
                    errorList.add(" 第" + count + "行：" + "产品系列(ProductSeries)值  错误！");
                    continue;
                }

            }
            //检验车间
            ModuleBasePlace moduleBasePlace = workshopMap.get(dto.getWorkshop());
            if (moduleBasePlace == null) {
                errorList.add(" 第" + count + "行：" + "车间(workshop)值  错误！");
                continue;
            }

            //国内/海外
            dto.setCountryFlag(moduleBasePlace.getIsOversea());

            //基地
            dto.setBasePlace(moduleBasePlace.getBasePlace());

            ActualOutput actualOutput = new ActualOutput();
            actualOutput.setCountryFlag(dto.getCountryFlag());
            actualOutput.setWorkshop(dto.getWorkshop());
            actualOutput.setProductType(productType);
            actualOutput.setProductGroup(dto.getProductGroup());
            actualOutput.setProductSeries(dto.getProductSeries());
            actualOutput.setProductFrom(dto.getProductFrom());
            actualOutput.setIsDeleted(DeleteEnum.NO.getCode());
            Example<ActualOutput> example = Example.of(actualOutput);

            ActualOutput oldActualOutput = null;
            try {

                oldActualOutput = repository.findOne(example).orElse(null);

            } catch (Exception e) {
                log.error("该数据可能存在异常，可能是数据库中存在多条唯一校验数据---{},错误消息:---{}", dto, e);
                errorList.add("第" + count + "行:" + "该数据可能存在异常，可能是数据库中存在多条唯一校验数据");
                continue;
            }

            //更新
            if (oldActualOutput != null) {
                dto.setId(oldActualOutput.getId());

            }

            ActualOutput entity = actualOutputDEConvert.toEntity(dto);
            saveList.add(entity);

        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(errorList.toString());
        }
        if (CollectionUtils.isNotEmpty(saveList)) {
            List<ActualOutput> configInventories = null;
            configInventories = repository.saveAll(saveList);
            if (CollectionUtils.isEmpty(configInventories)) {
                throw new BizException("ERROR:导入失败！");
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSave(List<ActualOutputDTO> dtoList, ProductTypeEnum productType) {
        List<Integer> yearList = dtoList.stream().map(ActualOutputDTO::getYear).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(yearList) || yearList.size() != 1) {
            throw new BizException("导入年份不唯一");
        }
        List<String> errorList = new ArrayList<>();
        int i = 0;
        Map<String, ModuleBasePlace> workshopBasePlace = Objects.requireNonNull(apsFeignClient.queryAllBasePlaceWorkshop().getBody()).getData();
        List<LovLineDTO> cellShard = LovService.findByLovCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        List<String> waferList = productInfoService.queryAllWafer();
        List<String> cellList = productInfoService.queryAllCell();
        Set<String> countryFlag = new HashSet<>();
        for (ActualOutputDTO actualOutputDTO : dtoList) {
            actualOutputDTO.setProductType(productType.getCode());
            i++;
            String code = CountryFlagEnum.getCode(actualOutputDTO.getCountryFlag());
            countryFlag.add(code);
            if (StringUtils.isBlank(code)) {
                errorList.add(String.format("第%s行国内/海外值错误！", i));
            } else {
                actualOutputDTO.setCountryFlag(code);
            }
            if (productType == ProductTypeEnum.WAFER && !waferList.contains(actualOutputDTO.getProductSeries())) {
                errorList.add(String.format("第%s行硅片型号不存在", i));
            }
            if (productType == ProductTypeEnum.CELL && !cellList.contains(actualOutputDTO.getProductSeries())) {
                errorList.add(String.format("第%s行电池型号不存在", i));
            }
            ModuleBasePlace moduleBasePlace = workshopBasePlace.get(actualOutputDTO.getWorkshop());
            if (ObjectUtils.isEmpty(moduleBasePlace)) {
                errorList.add(String.format("第%s行车间不存在", i));
            } else {
                actualOutputDTO.setBasePlace(moduleBasePlace.getBasePlace());
            }
            if (ObjectUtils.isEmpty(actualOutputDTO.getCellShard()) && ObjectUtils.isNotEmpty(actualOutputDTO.getCellShardName())) {
                actualOutputDTO.setCellShard(actualOutputDTO.getCellShardName());
            }
            if (ObjectUtils.isNotEmpty(actualOutputDTO.getCellShard()) && productType == ProductTypeEnum.CELL) {
                LovLineDTO lovLineDTO = cellShard.stream().filter(item -> actualOutputDTO.getCellShard().equals(item.getLovName())).findFirst().orElse(null);
                if (ObjectUtils.isNotEmpty(lovLineDTO)) {
                    actualOutputDTO.setCellShard(lovLineDTO.getLovValue());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            throw new BizException(errorList.toString());
        }
        repository.deleteAllByYearAndProductTypeAndCountryFlagIn(yearList.get(0), productType.getCode(), countryFlag);
        List<ActualOutput> actualOutputList = actualOutputDEConvert.toActualOutputs(dtoList);
        repository.saveAll(actualOutputList);
        return "导入成功";
    }

    /**
     * 根据年份和产品大类查询实际产出数据
     *
     * @param year
     * @param productType
     * @return
     */
    @Override
    public List<ActualOutput> queryByYearAndType(Integer year, ProductTypeEnum productType) {
        QActualOutput actualOutput = QActualOutput.actualOutput;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        List<ActualOutput> fetch = factory.selectFrom(actualOutput).where(actualOutput.year.eq(year).and(actualOutput.productType.eq(productType.getCode()))).fetch();
//        if (productType == ProductTypeEnum.CELL) {
//            List<ActualOutputDTO> actualOutputDTOS = actualOutputDEConvert.toActualOutputDTOs(fetch);
//            calculateCell(actualOutputDTOS);
//            fetch = actualOutputDEConvert.toActualOutputs(actualOutputDTOS);
//        }
        return fetch;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncActualOutputData(String qYear,String qMonth,Boolean syscFlag) {

        QActualOutput qActualOutput = QActualOutput.actualOutput;

        final String defaultKeySeparator = ",";

        List<OpActualOutputTemp> actualOutputTempLists = this.loadOutputTempList(qYear,qMonth,syscFlag);
        Integer inYear = actualOutputTempLists.get(0).getYear();
        Integer inMonth = actualOutputTempLists.get(0).getMonth();
        List<OpActualOutputTempDTO> actualOutputTempList = BeanUtils.instantiateClass(actualOutputTempLists.getClass());
        for (OpActualOutputTemp source : actualOutputTempLists) {
            OpActualOutputTempDTO destination = new OpActualOutputTempDTO();
            // 执行属性复制
            BeanUtils.copyProperties(source, destination);
            // 将转换后的对象添加到目标列表
            actualOutputTempList.add(destination);
        }
        //电池料号
        List<String> itemNoList = actualOutputTempList.stream().filter(item -> TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType())).map(OpActualOutputTempDTO::getItem).distinct().collect(Collectors.toList());
        //查询组件计划，找到电池料号对应电池型号
        List<CellRelationDTO> cellList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(itemNoList)) {
            ResponseEntity<Results<List<CellRelationDTO>>> response = apsFeignClient.querySeriesByItemNo(itemNoList);
            cellList = response.getBody().getData();
        }
        cellList = cellList.stream().filter(item -> item.getDescription().contains("Q1") || item.getDescription().contains("A-")).collect(Collectors.toList());
        //规整化料号对应回来的数据
        cellList.forEach(item -> {
            if (item.getCellType().contains("二分")) {
                item.setCellShard("Half-cut");
            } else if (item.getCellType().contains("三分")) {
                item.setCellShard("3-cut");
            }
            String[] s = item.getCellType().split("_");
            String str = s[2] + "-" + s[1];
            String replace = str.replace("型", "");
            if (replace.contains("156")) {
                replace = "156-MP";
            }
            if (replace.contains("158")) {
                replace = "158";
                item.setCellShard("Half-cut");
            }
            if (replace.contains("166")) {
                replace = "166";
            }
            item.setCellType(replace);
        });
        List<CellRelationDTO> finalCellList = cellList;
        //将电池实际产出中电池类型修改为料号对应的AOP电池型号，并设置电池分片方式
        actualOutputTempList.stream().filter(item -> TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getItem())).forEach(item -> {
            CellRelationDTO cellRelationDTO = finalCellList.stream().filter(bean -> item.getItem().equals(bean.getMaterialNo())).findFirst().orElse(null);
            if (ObjectUtils.isNotEmpty(cellRelationDTO)) {
                item.setProductSeries(cellRelationDTO.getCellType());
                item.setCellShard(cellRelationDTO.getCellShard());
            } else {
                item.setProductSeries(null);
            }
            item.setProductGroup(null);
        });
        //过滤脏数据
        actualOutputTempList = actualOutputTempList.stream().filter(item -> (TJConstant.ProductType.MODULE.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getProductSeries()))
                || (TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getCellShard()))
                || (TJConstant.ProductType.CELL.getTjCode().equals(item.getItemType()) && ObjectUtils.isNotEmpty(item.getProductSeries()) && item.getProductSeries().contains("156"))
        ).collect(Collectors.toList());
        //组件实际产出，tj过来数据为W，除以百万变成MW
        actualOutputTempList.stream().filter(item -> TJConstant.ProductType.MODULE.getTjCode().equals(item.getItemType())).forEach(OpActualOutputTempDTO::divideMegabyte);
        //维度汇总
        Map<String, List<OpActualOutputTempDTO>> outputTempMap = actualOutputTempList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getProductSeries())).collect(Collectors.groupingBy(item -> {
            //国内海外
            return this.countryFlagReplace(item.getCountryFlag())
                    //产品系列
                    + defaultKeySeparator + item.getProductSeries()
                    //产品族
                    + defaultKeySeparator + item.getProductGroup()
                    //车间
                    + defaultKeySeparator + item.getWorkshop()
                    //区域
                    + defaultKeySeparator + item.getArea()
                    //年份
                    + defaultKeySeparator + item.getYear()
                    //产品大类
                    + defaultKeySeparator + this.productTypeReplace(item.getItemType())
                    //电池分片方式
                    + defaultKeySeparator + item.getCellShard();
        }));
        List<ActualOutput> allActualOutput = repository.findAll();
        if (!syscFlag){
            allActualOutput=allActualOutput.stream().filter(item->item.getYear().equals(inYear)).collect(Collectors.toList());
            allActualOutput.forEach(item->
                    {
                        for (int i = inMonth; i <=12 ; i++) {
                            ReflectUtil.setFieldValue(item, "m" + Integer.valueOf(i) + "Quantity", BigDecimal.ZERO);
                        }
                    }
            );
            repository.saveAll(allActualOutput);
        }else {
            if (Integer.parseInt(qYear)<LocalDate.now().getYear()){
                List<ActualOutput> allActualOutput1=allActualOutput.stream().filter(item->item.getYear()==Integer.parseInt(qYear)).collect(Collectors.toList());
                for (int i = Integer.parseInt(qMonth); i <=12 ; i++) {
                    int in =i;
                    allActualOutput1.forEach(item->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                List<ActualOutput> allActualOutput2=allActualOutput.stream().filter(item->item.getYear()>Integer.parseInt(qYear)&&item.getYear()<LocalDate.now().getYear()).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(allActualOutput2)){
                    for (int i = 1; i <=12 ; i++) {
                        int in =i;
                        allActualOutput2.forEach(item->
                                ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                        );
                    }
                }
                List<ActualOutput> allActualOutput3=allActualOutput.stream().filter(item->item.getYear()==LocalDate.now().getYear()).collect(Collectors.toList());
                for (int i = 1; i <LocalDate.now().getMonth().getValue() ; i++) {
                    int in =i;
                    allActualOutput3.forEach(item->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                repository.saveAll(allActualOutput1);
                repository.saveAll(allActualOutput2);
                repository.saveAll(allActualOutput3);
            }else {
                List<ActualOutput> allActualOutput4=allActualOutput.stream().filter(item->item.getYear()==Integer.parseInt(qYear)).collect(Collectors.toList());
                for (int i = Integer.parseInt(qMonth); i <LocalDate.now().getMonth().getValue() ; i++) {
                    int in =i;
                    allActualOutput4.forEach(item->
                            ReflectUtil.setFieldValue(item, "m" + in + "Quantity", BigDecimal.ZERO)
                    );
                }
                repository.saveAll(allActualOutput4);
            }


        }
        //查询所有车间
        List<String> workshopList = actualOutputTempList.stream().map(OpActualOutputTempDTO::getWorkshop).distinct().collect(Collectors.toList());
        //车间列表匹配基地
        Map<String, ModuleBasePlace> moduleBasePlaceMap = loadModuleBasePlaceByWorkshopList(workshopList);

        for (Map.Entry<String, List<OpActualOutputTempDTO>> stringListEntry : outputTempMap.entrySet()) {
            String key = stringListEntry.getKey();
            String[] keys = key.split(defaultKeySeparator);
            String s = "null";
            String countryFlag = s.equals(keys[0]) ? null : keys[0];
            String productSeries = s.equals(keys[1]) ? null : keys[1];
            String productGroup = s.equals(keys[2]) ? null : keys[2];
            String workshop = s.equals(keys[3]) ? null : keys[3];
            String area = s.equals(keys[4]) ? null : keys[4];
            String year = s.equals(keys[5]) ? null : keys[5];
            String productType = s.equals(keys[6]) ? null : keys[6];
            String cellShard = s.equals(keys[7]) ? null : keys[7];

            List<OpActualOutputTempDTO> opActualOutputTempList = stringListEntry.getValue();
            BooleanBuilder booleanBuilder = new BooleanBuilder();
            if (StringUtils.isNotBlank(countryFlag)) {
                booleanBuilder.and(qActualOutput.countryFlag.eq(countryFlag));
            }
            if (StringUtils.isNotBlank(productSeries)) {
                booleanBuilder.and(qActualOutput.productSeries.eq(productSeries));
            }
            if (StringUtils.isNotBlank(productGroup)) {
                booleanBuilder.and(qActualOutput.productGroup.eq(productGroup));
            }
            if (StringUtils.isNotBlank(workshop)) {
                booleanBuilder.and(qActualOutput.workshop.eq(workshop));
            }
            if (StringUtils.isNotBlank(area)) {
                booleanBuilder.and(qActualOutput.area.eq(areaReplace(area)));
            }
            if (StringUtils.isNotBlank(year)) {
                booleanBuilder.and(qActualOutput.year.eq(Integer.valueOf(year)));
            }
            if (StringUtils.isNotBlank(productType)) {
                booleanBuilder.and(qActualOutput.productType.eq(productType));
            }
            if (StringUtils.isNotBlank(cellShard)) {
                booleanBuilder.and(qActualOutput.cellShard.eq(cellShard));
            }
            List<ActualOutput> actualOutputList = IterableUtils.iterableToList(repository.findAll(booleanBuilder));
            if (CollectionUtils.isNotEmpty(actualOutputList)) {
                //数据库中有则增量更新
                for (ActualOutput actualOutput : actualOutputList) {
                    //ReflectUtil.setFieldValue(actualOutput, "m" + Integer.valueOf(opActualOutputTempList.get(0).getMonth()) + "Quantity", null);
                    for (OpActualOutputTempDTO opActualOutputTemp : opActualOutputTempList) {
                        if (opActualOutputTemp.getMonth() == 1 ) {
                            BigDecimal quantity = actualOutput.getM1Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM1Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 2 ) {
                            BigDecimal quantity = actualOutput.getM2Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM2Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 3) {
                            BigDecimal quantity = actualOutput.getM3Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM3Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 4 ) {
                            BigDecimal quantity = actualOutput.getM4Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM4Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 5) {
                            BigDecimal quantity = actualOutput.getM5Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM5Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 6 ) {
                            BigDecimal quantity = actualOutput.getM6Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM6Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 7) {
                            BigDecimal quantity = actualOutput.getM7Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM7Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 8 ) {
                            BigDecimal quantity = actualOutput.getM8Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM8Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 9 ) {
                            BigDecimal quantity = actualOutput.getM9Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM9Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 10 ) {
                            BigDecimal quantity = actualOutput.getM10Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM10Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 11) {
                            BigDecimal quantity = actualOutput.getM11Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM11Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                        if (opActualOutputTemp.getMonth() == 12 ) {
                            BigDecimal quantity = actualOutput.getM12Quantity();
                            if (ObjectUtils.isEmpty(quantity)) {
                                quantity = BigDecimal.ZERO;
                            }
                            actualOutput.setM12Quantity(quantity.add(opActualOutputTemp.getQuantity()));
                        }
                    }
                    actualOutput.setArea(areaReplace(area));
                }
                repository.saveAll(actualOutputList);
            } else {
                //数据库中无数据则新建数据插入
                ActualOutput actualOutput = new ActualOutput();
                actualOutput.setCountryFlag(countryFlag);
                actualOutput.setProductSeries(productSeries);
                actualOutput.setProductGroup(productGroup);
                actualOutput.setWorkshop(workshop);
                actualOutput.setArea(areaReplace(area));
                actualOutput.setYear(Integer.valueOf(year));
                actualOutput.setProductType(productType);
                actualOutput.setCellShard(cellShard);
                actualOutput.setBasePlace(Optional.ofNullable(moduleBasePlaceMap.get(workshop)).map(ModuleBasePlace::getBasePlace).orElse(null));
                for (OpActualOutputTempDTO opActualOutputTemp : opActualOutputTempList) {
                    String fieldName = "m" + opActualOutputTemp.getMonth() + "Quantity";
                    BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(actualOutput, fieldName);
                    if (ObjectUtils.isEmpty(value)) {
                        value = BigDecimal.ZERO;
                    }
                    ReflectUtil.setFieldValue(actualOutput, fieldName, value.add(opActualOutputTemp.getQuantity()));
                }
                Map<String, Integer> temp = new HashMap<>();
                for (int i = 1; i < 13; i++) {
                    String fieldName = "m" + i + "Quantity";
                    BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(actualOutput, fieldName);
                    if (ObjectUtils.isNotEmpty(value)) {
                        temp.put("actualOutput", i);
                    }
                }
                if (temp != null && temp.size() > 0) {
                    for (int m = 1; m < temp.get("actualOutput"); m++) {
                        String fieldName = "m" + m + "Quantity";
                        BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(actualOutput, fieldName);
                        if (ObjectUtils.isEmpty(value)) {
                            value = BigDecimal.ZERO;
                        }
                        ReflectUtil.setFieldValue(actualOutput, fieldName, value);

                    }
                }
                if (temp != null && temp.size() > 0 &&temp.get("actualOutput")!=12) {
                    for(int m = temp.get("actualOutput")+1; m <=12; m++ ){
                        String fieldName = "m" + m + "Quantity";
                        BigDecimal value = (BigDecimal) ReflectUtil.getFieldValue(actualOutput, fieldName);
                        if (ObjectUtils.isEmpty(value)) {
                            value = BigDecimal.ZERO;
                        }
                        ReflectUtil.setFieldValue(actualOutput, fieldName, value);

                    }
                }

                repository.save(actualOutput);
            }
        }

    }

    private String countryFlagReplace(String countryFlag) {
        return CountryMapping.matchByAll(countryFlag).getAopEnum().getCode();
    }

    private String productTypeReplace(String productType) {
        return TJConstant.ProductType.match(productType).getCode();
    }

    private String areaReplace(String area) {
        if (StringUtils.isNotEmpty(area) && area.equals(TJConstant.Area.OTHE.getTjCode())) {
            return TJConstant.Area.OTHS.getTjCode();
        }
        return area;
    }

    /**
     * 从临时表中查询实际产出，查询范围为创建时间为三个月内的数据
     *
     * @return
     */
    private List<OpActualOutputTemp> loadOutputTempList(String year,String month,Boolean syscFlag) {
        QOpActualOutputTemp qOpActualOutputTemp = QOpActualOutputTemp.opActualOutputTemp;
        JPAQueryFactory factory = new JPAQueryFactory(entityManager);
        //获取最新的创建时间
        List<LocalDateTime> localDateTimes = factory.select(qOpActualOutputTemp.createdTime.max()).from(qOpActualOutputTemp).fetch();
        List<OpActualOutputTemp> module = new ArrayList<>();
        //构建同步范围，同步创建时间三月内的数据

        if (!syscFlag) {
            Integer QMonth = 0;
            Integer QYear = 0;
            if (StringUtils.isBlank(year)||StringUtils.isBlank(month)) {
                year = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy"));
                month = LocalDate.now().format(DateTimeFormatter.ofPattern("MM"));
                if (month.equals("01")) {
                    QMonth=12;
                    QYear=Integer.parseInt(year)-1;
                } else {
                    QMonth=Integer.parseInt(month)-1;
                    QYear=Integer.parseInt(year);
                }
            }else {
                QMonth=Integer.parseInt(month);
                QYear=Integer.parseInt(year);
            }
            //查询组件实际产出
            module = factory
                    .select(Projections.fields(
                            OpActualOutputTemp.class,
                            qOpActualOutputTemp.countryFlag.as("countryFlag"),
                            qOpActualOutputTemp.organization.as("organization"),
                            qOpActualOutputTemp.type.as("type"),
                            qOpActualOutputTemp.workshop.as("workshop"),
                            qOpActualOutputTemp.item.as("item"),
                            qOpActualOutputTemp.productGroup.as("productGroup"),
                            qOpActualOutputTemp.year.as("year"),
                            qOpActualOutputTemp.month.as("month"),
                            qOpActualOutputTemp.quantity.as("quantity"),
                            qOpActualOutputTemp.area.as("area"),
                            qOpActualOutputTemp.itemType.as("itemType")
                    ))
                    .from(qOpActualOutputTemp)
                    .where(qOpActualOutputTemp.countryFlag.isNotNull())
                    .where(qOpActualOutputTemp.productGroup.isNotNull())
                    .where(qOpActualOutputTemp.workshop.isNotNull())
                    .where(qOpActualOutputTemp.area.isNotNull())
                    .where(qOpActualOutputTemp.year.eq(QYear))
                    .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                    .where(qOpActualOutputTemp.month.eq(QMonth))
                    .where(qOpActualOutputTemp.quantity.isNotNull())
                    .fetch();
            List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
            //规整化数据,修改组件产品族，设置组件产品系列
            module.forEach(item -> {
                String replace = item.getProductGroup().replace("TSM-", "");
//            if ("OTHE".equals(item.getArea())) {
//                item.setArea(AreaEnums.OTHS.getCode());
//            }
                if (StringUtils.isNotBlank(replace)) {
                    item.setProductGroup(replace);
                    ProductInfo productInfo = productInfoList.stream().filter(bean -> replace.equals(bean.getProductGroup())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(productInfo)) {
                        item.setProductSeries(productInfo.getProductSeries());
                    }
                }
            });
            module.removeIf(item -> ObjectUtils.isEmpty(item.getProductSeries()));
            //查询电池实际产出
            List<OpActualOutputTemp> cellList = factory
                    .select(Projections.fields(
                            OpActualOutputTemp.class,
                            qOpActualOutputTemp.countryFlag.as("countryFlag"),
                            qOpActualOutputTemp.organization.as("organization"),
                            qOpActualOutputTemp.type.as("type"),
                            qOpActualOutputTemp.workshop.as("workshop"),
                            qOpActualOutputTemp.item.as("item"),
                            qOpActualOutputTemp.productSeries.as("productSeries"),
                            qOpActualOutputTemp.year.as("year"),
                            qOpActualOutputTemp.month.as("month"),
                            qOpActualOutputTemp.quantity.as("quantity"),
                            qOpActualOutputTemp.area.as("area"),
                            qOpActualOutputTemp.itemType.as("itemType")
                    ))
                    .from(qOpActualOutputTemp)
                    .where(qOpActualOutputTemp.countryFlag.isNotNull())
                    .where(qOpActualOutputTemp.workshop.isNotNull())
                    .where(qOpActualOutputTemp.year.eq(QYear))
                    .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                    .where(qOpActualOutputTemp.type.eq(TJConstant.Type.MAKE.getTjCode()))
                    .where(qOpActualOutputTemp.month.eq(QMonth))
                    .where(qOpActualOutputTemp.quantity.isNotNull())
                    .fetch();
            cellList.forEach(item -> {
                item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
            });
            module.addAll(cellList);
        }else {
            Integer StarMonth = Integer.parseInt(month);
            Integer StarYear = Integer.parseInt(year);
            int endYear = LocalDate.now().minusMonths(1).getYear();
            int endMonth = LocalDate.now().minusMonths(1).getMonth().getValue();

            //查询组件实际产出
            if (StarYear<endYear) {
                module = factory
                        .select(Projections.fields(
                                OpActualOutputTemp.class,
                                qOpActualOutputTemp.countryFlag.as("countryFlag"),
                                qOpActualOutputTemp.organization.as("organization"),
                                qOpActualOutputTemp.type.as("type"),
                                qOpActualOutputTemp.workshop.as("workshop"),
                                qOpActualOutputTemp.item.as("item"),
                                qOpActualOutputTemp.productGroup.as("productGroup"),
                                qOpActualOutputTemp.year.as("year"),
                                qOpActualOutputTemp.month.as("month"),
                                qOpActualOutputTemp.quantity.as("quantity"),
                                qOpActualOutputTemp.area.as("area"),
                                qOpActualOutputTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualOutputTemp)
                        .where(qOpActualOutputTemp.countryFlag.isNotNull())
                        .where(qOpActualOutputTemp.productGroup.isNotNull())
                        .where(qOpActualOutputTemp.workshop.isNotNull())
                        .where(qOpActualOutputTemp.area.isNotNull())
                        .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                        .where(qOpActualOutputTemp.year.eq(StarYear).and(qOpActualOutputTemp.month.goe(StarMonth))
                                .or(qOpActualOutputTemp.year.gt(StarYear).and(qOpActualOutputTemp.year.lt(endYear)))
                                .or(qOpActualOutputTemp.year.eq(endYear).and(qOpActualOutputTemp.month.loe(endMonth))))
                        .where(qOpActualOutputTemp.quantity.isNotNull())
                        .fetch();
            }else{
                module = factory
                        .select(Projections.fields(
                                OpActualOutputTemp.class,
                                qOpActualOutputTemp.countryFlag.as("countryFlag"),
                                qOpActualOutputTemp.organization.as("organization"),
                                qOpActualOutputTemp.type.as("type"),
                                qOpActualOutputTemp.workshop.as("workshop"),
                                qOpActualOutputTemp.item.as("item"),
                                qOpActualOutputTemp.productGroup.as("productGroup"),
                                qOpActualOutputTemp.year.as("year"),
                                qOpActualOutputTemp.month.as("month"),
                                qOpActualOutputTemp.quantity.as("quantity"),
                                qOpActualOutputTemp.area.as("area"),
                                qOpActualOutputTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualOutputTemp)
                        .where(qOpActualOutputTemp.countryFlag.isNotNull())
                        .where(qOpActualOutputTemp.productGroup.isNotNull())
                        .where(qOpActualOutputTemp.workshop.isNotNull())
                        .where(qOpActualOutputTemp.area.isNotNull())
                        .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.MODULE.getTjCode()))
                        .where(qOpActualOutputTemp.year.eq(StarYear).and(qOpActualOutputTemp.month.goe(StarMonth))
                                .and(qOpActualOutputTemp.year.eq(endYear).and(qOpActualOutputTemp.month.loe(endMonth))))
                        .where(qOpActualOutputTemp.quantity.isNotNull())
                        .fetch();
            }
            List<ProductInfo> productInfoList = productInfoService.queryAllProductInfo();
            //规整化数据,修改组件产品族，设置组件产品系列
            module.forEach(item -> {
                String replace = item.getProductGroup().replace("TSM-", "");
//            if ("OTHE".equals(item.getArea())) {
//                item.setArea(AreaEnums.OTHS.getCode());
//            }
                if (StringUtils.isNotBlank(replace)) {
                    item.setProductGroup(replace);
                    ProductInfo productInfo = productInfoList.stream().filter(bean -> replace.equals(bean.getProductGroup())).findFirst().orElse(null);
                    if (ObjectUtils.isNotEmpty(productInfo)) {
                        item.setProductSeries(productInfo.getProductSeries());
                    }
                }
            });
            module.removeIf(item -> ObjectUtils.isEmpty(item.getProductSeries()));
            //查询电池实际产出
            List<OpActualOutputTemp> cellList = new ArrayList<>();
            if (StarYear<endYear) {
                cellList = factory
                        .select(Projections.fields(
                                OpActualOutputTemp.class,
                                qOpActualOutputTemp.countryFlag.as("countryFlag"),
                                qOpActualOutputTemp.organization.as("organization"),
                                qOpActualOutputTemp.type.as("type"),
                                qOpActualOutputTemp.workshop.as("workshop"),
                                qOpActualOutputTemp.item.as("item"),
                                qOpActualOutputTemp.productSeries.as("productSeries"),
                                qOpActualOutputTemp.year.as("year"),
                                qOpActualOutputTemp.month.as("month"),
                                qOpActualOutputTemp.quantity.as("quantity"),
                                qOpActualOutputTemp.area.as("area"),
                                qOpActualOutputTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualOutputTemp)
                        .where(qOpActualOutputTemp.countryFlag.isNotNull())
                        .where(qOpActualOutputTemp.workshop.isNotNull())
                        .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                        .where(qOpActualOutputTemp.type.eq(TJConstant.Type.MAKE.getTjCode()))
                        .where(qOpActualOutputTemp.year.eq(StarYear).and(qOpActualOutputTemp.month.goe(StarMonth))
                                .or(qOpActualOutputTemp.year.gt(StarYear).and(qOpActualOutputTemp.year.lt(endYear)))
                                .or(qOpActualOutputTemp.year.eq(endYear).and(qOpActualOutputTemp.month.loe(endMonth))))
                        .where(qOpActualOutputTemp.quantity.isNotNull())
                        .fetch();
                cellList.forEach(item -> {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                });
            }else {
                cellList = factory
                        .select(Projections.fields(
                                OpActualOutputTemp.class,
                                qOpActualOutputTemp.countryFlag.as("countryFlag"),
                                qOpActualOutputTemp.organization.as("organization"),
                                qOpActualOutputTemp.type.as("type"),
                                qOpActualOutputTemp.workshop.as("workshop"),
                                qOpActualOutputTemp.item.as("item"),
                                qOpActualOutputTemp.productSeries.as("productSeries"),
                                qOpActualOutputTemp.year.as("year"),
                                qOpActualOutputTemp.month.as("month"),
                                qOpActualOutputTemp.quantity.as("quantity"),
                                qOpActualOutputTemp.area.as("area"),
                                qOpActualOutputTemp.itemType.as("itemType")
                        ))
                        .from(qOpActualOutputTemp)
                        .where(qOpActualOutputTemp.countryFlag.isNotNull())
                        .where(qOpActualOutputTemp.workshop.isNotNull())
                        .where(qOpActualOutputTemp.itemType.eq(TJConstant.ProductType.CELL.getTjCode()))
                        .where(qOpActualOutputTemp.type.eq(TJConstant.Type.MAKE.getTjCode()))
                        .where(qOpActualOutputTemp.year.eq(StarYear).and(qOpActualOutputTemp.month.goe(StarMonth))
                                .and(qOpActualOutputTemp.year.eq(endYear).and(qOpActualOutputTemp.month.loe(endMonth))))
                        .where(qOpActualOutputTemp.quantity.isNotNull())
                        .fetch();
                cellList.forEach(item -> {
                    item.setQuantity(item.getQuantity().divide(BigDecimal.valueOf(1000000), 6, RoundingMode.HALF_UP));
                });
            }
            module.addAll(cellList);
        }
        return module;
    }

    private Map<String, ModuleBasePlace> loadModuleBasePlaceByWorkshopList(List<String> workshopList) {
        ModuleBasePlaceQuery query = new ModuleBasePlaceQuery();
        query.setWorkshopList(workshopList);
        Map<String, ModuleBasePlace> moduleBasePlaceList = Optional.ofNullable(apsFeignClient.queryByList(query))
                .map(HttpEntity::getBody)
                .map(Results::getData)
                .orElse(Lists.newArrayList()).stream()
                .collect(Collectors.toMap(ModuleBasePlace::getWorkshop, Function.identity(), (v1, v2) -> v1));
        return moduleBasePlaceList;
    }

    /**
     * 电池万片换算成MW
     *
     * @param cellList
     */
    private void calculateCell(List<ActualOutputDTO> cellList) {
        List<String> errorList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cellList)) {
            List<Integer> yearList = cellList.stream().map(ActualOutputDTO::getYear).distinct().collect(Collectors.toList());
            List<CellWaferWeightDTO> cellWaferWeightDTOS = cellWaferWeightService.queryByTypeYear(yearList, ProductTypeEnum.CELL);
            for (ActualOutputDTO actualOutputDTO : cellList) {
                List<CellWaferWeightDTO> cellWeightList = cellWaferWeightDTOS.stream().filter(item -> {
                    boolean flag = actualOutputDTO.getCountryFlag().equals(item.getCountryFlag()) && actualOutputDTO.getProductSeries().equals(item.getCellModel());
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getCellShard())) {
                        flag = flag && actualOutputDTO.getCellShard().equals(item.getCellShard());
                    }
                    return flag;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cellWeightList)) {
                    List<Integer> weightMonth = new ArrayList<>();
                    List<Integer> discountMonth = new ArrayList<>();
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM1Quantity())) {
                        BigDecimal m1Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1Quantity())) {
                                weightMonth.add(1);
                            } else if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM1DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM1DiscountQuantity()) == 0)) {
                                discountMonth.add(1);
                            } else {
                                if (cellWaferWeightDTO.getM1Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m1Quantity = m1Quantity.add(actualOutputDTO.getM1Quantity().multiply(cellWaferWeightDTO.getM1Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM1DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM1Quantity(m1Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM2Quantity())) {
                        BigDecimal m2Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM2Quantity())) {
                                weightMonth.add(2);
                            } else if (cellWaferWeightDTO.getM2Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM2DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM2DiscountQuantity()) == 0)) {
                                discountMonth.add(2);
                            } else {
                                if (cellWaferWeightDTO.getM2Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m2Quantity = m2Quantity.add(actualOutputDTO.getM2Quantity().multiply(cellWaferWeightDTO.getM2Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM2DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM2Quantity(m2Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM3Quantity())) {
                        BigDecimal m3Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM3Quantity())) {
                                weightMonth.add(3);
                            } else if (cellWaferWeightDTO.getM3Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM3DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM3DiscountQuantity()) == 0)) {
                                discountMonth.add(3);
                            } else {
                                if (cellWaferWeightDTO.getM3Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m3Quantity = m3Quantity.add(actualOutputDTO.getM3Quantity().multiply(cellWaferWeightDTO.getM3Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM3DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM3Quantity(m3Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM4Quantity())) {
                        BigDecimal m4Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM4Quantity())) {
                                weightMonth.add(4);
                            } else if (cellWaferWeightDTO.getM4Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM4DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM4DiscountQuantity()) == 0)) {
                                discountMonth.add(4);
                            } else {
                                if (cellWaferWeightDTO.getM4Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m4Quantity = m4Quantity.add(actualOutputDTO.getM4Quantity().multiply(cellWaferWeightDTO.getM4Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM4DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM4Quantity(m4Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM5Quantity())) {
                        BigDecimal m5Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM5Quantity())) {
                                weightMonth.add(5);
                            } else if (cellWaferWeightDTO.getM5Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM5DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM5DiscountQuantity()) == 0)) {
                                discountMonth.add(5);
                            } else {
                                if (cellWaferWeightDTO.getM5Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m5Quantity = m5Quantity.add(actualOutputDTO.getM5Quantity().multiply(cellWaferWeightDTO.getM5Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM5DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM5Quantity(m5Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM6Quantity())) {
                        BigDecimal m6Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM6Quantity())) {
                                weightMonth.add(6);
                            } else if (cellWaferWeightDTO.getM6Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM6DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM6DiscountQuantity()) == 0)) {
                                discountMonth.add(6);
                            } else {
                                if (cellWaferWeightDTO.getM6Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m6Quantity = m6Quantity.add(actualOutputDTO.getM6Quantity().multiply(cellWaferWeightDTO.getM6Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM6DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM6Quantity(m6Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM7Quantity())) {
                        BigDecimal m7Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM7Quantity())) {
                                weightMonth.add(7);
                            } else if (cellWaferWeightDTO.getM7Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM7DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM7DiscountQuantity()) == 0)) {
                                discountMonth.add(7);
                            } else {
                                if (cellWaferWeightDTO.getM7Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m7Quantity = m7Quantity.add(actualOutputDTO.getM7Quantity().multiply(cellWaferWeightDTO.getM7Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM7DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM7Quantity(m7Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM8Quantity())) {
                        BigDecimal m8Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM8Quantity())) {
                                weightMonth.add(8);
                            } else if (cellWaferWeightDTO.getM8Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM8DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM8DiscountQuantity()) == 0)) {
                                discountMonth.add(8);
                            } else {
                                if (cellWaferWeightDTO.getM8Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m8Quantity = m8Quantity.add(actualOutputDTO.getM8Quantity().multiply(cellWaferWeightDTO.getM8Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM8DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM8Quantity(m8Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM9Quantity())) {
                        BigDecimal m9Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM9Quantity())) {
                                weightMonth.add(9);
                            } else if (cellWaferWeightDTO.getM9Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM9DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM9DiscountQuantity()) == 0)) {
                                discountMonth.add(9);
                            } else {
                                if (cellWaferWeightDTO.getM9Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m9Quantity = m9Quantity.add(actualOutputDTO.getM9Quantity().multiply(cellWaferWeightDTO.getM9Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM9DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM9Quantity(m9Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM10Quantity())) {
                        BigDecimal m10Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM10Quantity())) {
                                weightMonth.add(10);
                            } else if (cellWaferWeightDTO.getM10Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM10DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM10DiscountQuantity()) == 0)) {
                                discountMonth.add(10);
                            } else {
                                if (cellWaferWeightDTO.getM10Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m10Quantity = m10Quantity.add(actualOutputDTO.getM10Quantity().multiply(cellWaferWeightDTO.getM10Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM10DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM10Quantity(m10Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM11Quantity())) {
                        BigDecimal m11Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM11Quantity())) {
                                weightMonth.add(11);
                            } else if (cellWaferWeightDTO.getM11Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM11DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM11DiscountQuantity()) == 0)) {
                                discountMonth.add(11);
                            } else {
                                if (cellWaferWeightDTO.getM11Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m11Quantity = m11Quantity.add(actualOutputDTO.getM11Quantity().multiply(cellWaferWeightDTO.getM11Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM11DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM11Quantity(m11Quantity);
                    }
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getM12Quantity())) {
                        BigDecimal m12Quantity = BigDecimal.ZERO;
                        for (CellWaferWeightDTO cellWaferWeightDTO : cellWeightList) {
                            if (ObjectUtils.isEmpty(cellWaferWeightDTO.getM12Quantity())) {
                                weightMonth.add(12);
                            } else if (cellWaferWeightDTO.getM12Quantity().compareTo(BigDecimal.ZERO) != 0 && (ObjectUtils.isEmpty(cellWaferWeightDTO.getM12DiscountQuantity()) || BigDecimal.ZERO.compareTo(cellWaferWeightDTO.getM12DiscountQuantity()) == 0)) {
                                discountMonth.add(12);
                            } else {
                                if (cellWaferWeightDTO.getM12Quantity().compareTo(BigDecimal.ZERO) != 0) {
                                    m12Quantity = m12Quantity.add(actualOutputDTO.getM12Quantity().multiply(cellWaferWeightDTO.getM12Quantity()).divide(BigDecimal.valueOf(1000000).multiply(cellWaferWeightDTO.getM12DiscountQuantity()), 6, RoundingMode.HALF_UP));
                                }
                            }
                        }
                        actualOutputDTO.setM12Quantity(m12Quantity);
                    }
                    if (CollectionUtils.isNotEmpty(weightMonth)) {
                        errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无权重配比", actualOutputDTO.getYear(), actualOutputDTO.getCountryFlag(), actualOutputDTO.getProductSeries(), actualOutputDTO.getCellShard(), weightMonth.stream().distinct().collect(Collectors.toList())));
                    }
                    if (CollectionUtils.isNotEmpty(discountMonth)) {
                        errorList.add(String.format("年份：%s,国内/海外：%s，电池型号：%s，电池分片方式：%s，月份：%s 无折算参数", actualOutputDTO.getYear(), actualOutputDTO.getCountryFlag(), actualOutputDTO.getProductSeries(), actualOutputDTO.getCellShard(), discountMonth.stream().distinct().collect(Collectors.toList())));
                    }
                } else {
                    StringBuilder sb = new StringBuilder("年份：").append(actualOutputDTO.getYear()).append("，国内/海外：").append(actualOutputDTO.getCountryFlag()).append("，电池型号：").append(actualOutputDTO.getProductSeries());
                    if (ObjectUtils.isNotEmpty(actualOutputDTO.getCellShard())) {
                        sb.append("，分片方式：").append(actualOutputDTO.getCellShard());
                    }
                    sb.append("无对应的权重配比");
                    errorList.add(sb.toString());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorList)) {
            StringBuffer sb = new StringBuffer();
            for (String s : errorList) {
                sb.append(s).append("<br/>");
            }
            throw new BizException(sb.toString());
        }
    }

    @Override
    public Page<ActualOutputDTO> queryModuleSummaryPage(ActualOutputQuery query) {
        QActualOutput qActualOutput = QActualOutput.actualOutput;
        JPAQueryFactory jpaQueryFactory = new JPAQueryFactory(entityManager);
        JPAQuery<ActualOutputDTO> fromSql = jpaQueryFactory.select(Projections.fields(ActualOutputDTO.class,
                qActualOutput.year.as("year"),
                qActualOutput.productSeries.as("productSeries"),
                qActualOutput.area.as("area"),
                qActualOutput.m1Quantity.sum().as("m1Quantity"),
                qActualOutput.m2Quantity.sum().as("m2Quantity"),
                qActualOutput.m3Quantity.sum().as("m3Quantity"),
                qActualOutput.m4Quantity.sum().as("m4Quantity"),
                qActualOutput.m5Quantity.sum().as("m5Quantity"),
                qActualOutput.m6Quantity.sum().as("m6Quantity"),
                qActualOutput.m7Quantity.sum().as("m7Quantity"),
                qActualOutput.m8Quantity.sum().as("m8Quantity"),
                qActualOutput.m9Quantity.sum().as("m9Quantity"),
                qActualOutput.m10Quantity.sum().as("m10Quantity"),
                qActualOutput.m11Quantity.sum().as("m11Quantity"),
                qActualOutput.m12Quantity.sum().as("m12Quantity")
        )).from(qActualOutput);

        BooleanBuilder builder = new BooleanBuilder();
        if (StringUtils.isNotBlank(query.getProductSeries())) {
            builder.and(qActualOutput.productSeries.eq(query.getProductSeries()));
        }

        if (query.getYear() != null) {
            builder.and(qActualOutput.year.eq(query.getYear()));
        }

        if (ObjectUtils.isNotEmpty(query.getProductType())) {
            builder.and(qActualOutput.productType.eq(query.getProductType()));
        }

        JPAQuery<ActualOutputDTO> actualOutputDTOJPAQuery = fromSql.where(builder).groupBy(
                qActualOutput.year, qActualOutput.productSeries, qActualOutput.area);

        List<ActualOutputDTO> fetch = actualOutputDTOJPAQuery
                .offset((long) (query.getPageNumber() - 1) * query.getPageSize())
                .limit(query.getPageSize()).fetch();

        List<Tuple> list = jpaQueryFactory.select(qActualOutput.year, qActualOutput.productSeries, qActualOutput.area)
                .from(qActualOutput)
                .where(builder)
                .groupBy(qActualOutput.year, qActualOutput.productSeries, qActualOutput.area).
                fetch();

        Pageable pageable = PageRequest.of(query.getPageNumber() - 1, query.getPageSize());
        setQuantity(fetch);

        return new PageImpl<>(fetch, pageable, list.size());
    }

    @Override
    @SneakyThrows
    public void syncWaferActualPurchase(LocalDate inventoryDate, Boolean mvIngoreFlag) {
        log.info("syncWaferActualPurchase start... {}", LocalDateTime.now());
        // 分页大小从0开始 0默认取第一行
        List<BigdataWaferActualPurchaseDTO> apiDataList = Lists.newLinkedList();
        pullWaferActualPurchaseHandler(0, inventoryDate, apiDataList);

        Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceMap = this.getCellWaferWeightMaintenanceMap(inventoryDate.getYear());
        apiDataList.forEach(ele -> {
            ele.setIsOversea(Objects.equals("国内", ele.getIsOversea()) ? CountryFlagEnum.INLAND.getCode() : CountryFlagEnum.OVERSEA.getCode());
            ele.setCellType(ele.getCellType().contains("+") ? ele.getCellType().replace("+", "") : ele.getCellType());
        });

        //保存
        ActualOutputServiceImpl beanHandle = SpringContextUtils.getBean(ActualOutputServiceImpl.class);
        beanHandle.batchSaveActualOutputHandler(inventoryDate, apiDataList, cellWaferWeightMaintenanceMap, mvIngoreFlag);
        beanHandle.batchSaveActualOtherHandler(inventoryDate, apiDataList, cellWaferWeightMaintenanceMap, mvIngoreFlag);
        log.info("syncOutgoingBattery end... {}", LocalDateTime.now());
    }

    @Override
    @SneakyThrows
    public void syncModuleActualOutput(LocalDate inventoryDate) {
        log.info("syncModuleActualOutput start... {}", LocalDateTime.now());

        // 分页大小从0开始 0默认取第一行 分页轮询请求
        List<BigdataModuleActualOutputDTO> apiDataList = Lists.newLinkedList();
        pullModuleActualOutputHandler(0, inventoryDate, apiDataList);

        List<BigdataModuleActualOutputDTO> emptyApiDataList = apiDataList.stream().filter(ele -> StringUtils.isEmpty(ele.getProductFamily())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(emptyApiDataList)) {
            log.info("syncModuleActualOutput productFamily empty {}", JSON.toJSONString(emptyApiDataList));
        }
        // 过滤产品族为空的数据
        apiDataList = apiDataList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getProductFamily())).collect(Collectors.toList());

        List<BigdataModuleActualOutputDTO> emptyWorkshopApiDataList = apiDataList.stream().filter(ele -> StringUtils.isEmpty(ele.getWorkshop())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(emptyWorkshopApiDataList)) {
            log.info("syncModuleActualOutput workshop empty {}", JSON.toJSONString(emptyWorkshopApiDataList));
        }
        // 过滤产品族为空的数据
        apiDataList = apiDataList.stream().filter(ele -> StringUtils.isNotEmpty(ele.getWorkshop())).collect(Collectors.toList());

        // 产品信息对照表 路径：AOP管理->配置管理->产品信息对照表
        List<ProductInfo> productInfos = productInfoService.queryAllProductInfo();
        Map<String, String> productInfoMap = productInfos.stream().collect(Collectors.toMap(ProductInfo::getProductGroup, ProductInfo::getProductSeries, (v1, v2) -> v1));
        // 产地信息表 路径：组件计划->基础数据->产地
        Map<String, ModuleBasePlace> workAndBaseMap = apsFeignClient.queryAllBasePlaceWorkshop().getBody().getData();

        //  产地 校验
        Set<String> errorBasePlaceList = Sets.newHashSet();
        Iterator<BigdataModuleActualOutputDTO> iterator = apiDataList.iterator();
        while (iterator.hasNext()) {
            BigdataModuleActualOutputDTO ele = iterator.next();
            ele.setIsOversea(Objects.equals("国内", ele.getIsOversea()) ? CountryFlagEnum.INLAND.getCode() : CountryFlagEnum.OVERSEA.getCode());
            String productFamily = ele.getProductFamily().replaceFirst("TSM-", "");
            productFamily = productFamily.lastIndexOf("-Q") != -1 ? productFamily.substring(0, productFamily.lastIndexOf("-Q")) : productFamily;
            ele.setProductFamily(productFamily);
            //产品型号校验
            String productSeries = productInfoMap.get(productFamily);
            if (StringUtils.isEmpty(productSeries)) {
                log.info("syncCellActualOutput productSeries empty {}", JSON.toJSONString(ele));
                iterator.remove();
                continue;
            }
            //基地校验
            ModuleBasePlace moduleBasePlace = workAndBaseMap.get(ele.getWorkshop());
            if (Objects.isNull(moduleBasePlace) || StringUtils.isEmpty(moduleBasePlace.getBasePlace())) {
                errorBasePlaceList.add(ele.getWorkshop());
            }
        }

        Assert.isTrue(CollectionUtils.isEmpty(errorBasePlaceList), String.format("产地信息表中无以下对应的生产车间：%s", StringUtils.join(errorBasePlaceList, "、")));

        //保存
        ActualOutputServiceImpl beanHandle = SpringContextUtils.getBean(ActualOutputServiceImpl.class);
        beanHandle.batchSaveModuleActualOutputHandler(inventoryDate, apiDataList, productInfoMap, workAndBaseMap);
        // 【p2_1341】AOP03-A-结果计算改造  组件A-产出 & 外销A-电池
        beanHandle.batchSaveAopThirdAModuleProduceHandler(inventoryDate, apiDataList);
        log.info("syncModuleActualOutput end... {}", LocalDateTime.now());
    }

    @Override
    @SneakyThrows
    public void syncCellActualOutput(LocalDate inventoryDate, Boolean mvIngoreFlag) {
        log.info("syncCellActualOutput start... {} inventoryDate：{} mvIngoreFlag：{}", LocalDateTime.now(), inventoryDate, mvIngoreFlag);

        // 分页大小从0开始 0默认取第一行 分页轮询请求
        List<BigdataCellActualOutputDTO> apiDataList = Lists.newLinkedList();
        pullCellActualOutputHandler(0, inventoryDate, apiDataList);

        List<BigdataCellActualOutputDTO> apiDataEmptyList = apiDataList.stream().filter(ele -> StringUtils.isEmpty(ele.getCtName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(apiDataEmptyList)) {
            log.info("syncCellActualOutput CtName null {}", JSON.toJSONString(apiDataEmptyList));
            apiDataList = apiDataList.stream().filter(ele -> !StringUtils.isEmpty(ele.getCtName())).collect(Collectors.toList());
        }

        // MW折算系数-电池 路径：组件计划->电池分配 ->MW折算系数
        List<MwCoefficientDTO> mwCoefficientDTOS = powerFeignClient.allList().getBody().getData();
        Map<String, BigDecimal> mwCoefficientMap = mwCoefficientDTOS.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(Arrays.asList(ele.getIsOversea(), ele.getCellType()), "_"), MwCoefficientDTO::getCoefficient, (v1, v2) -> v1));

        // 产地信息表 路径：组件计划->基础数据->产地
        Map<String, ModuleBasePlace> workAndBaseMap = apsFeignClient.queryAllBasePlaceWorkshop().getBody().getData();

        // 产品信息 && 产地 校验
        Set<String> errorMVSets = Sets.newHashSet();
        Iterator<BigdataCellActualOutputDTO> iterator = apiDataList.iterator();
        while (iterator.hasNext()) {
            BigdataCellActualOutputDTO next = iterator.next();
            //基地校验
            ModuleBasePlace moduleBasePlace = workAndBaseMap.get(next.getCtName());
            if (Objects.isNull(moduleBasePlace) || StringUtils.isEmpty(moduleBasePlace.getBasePlace())) {
                log.info("syncCellActualOutput moduleBasePlace null {}", JSON.toJSONString(next));
                iterator.remove();
                // MW折算系数需要国内/海外字段
                continue;
            }
            // 当月的硅片-MW折算系数 可支持手工传入跳过MV折算系数 mvIngoreFlag=true即跳过不参与后续计算
            CountryFlagEnum countryFlagEnumDesc = CountryFlagEnum.getCountryFlagEnumDesc(moduleBasePlace.getIsOversea());
            String isOversea = Objects.nonNull(countryFlagEnumDesc) ? countryFlagEnumDesc.getCode() : moduleBasePlace.getIsOversea();
            String maintenanceGroupKey = StringUtils.join(Arrays.asList(isOversea, next.getProductName()), "_");
            BigDecimal mvQuantity = mwCoefficientMap.get(maintenanceGroupKey);
            if (Objects.isNull(mvQuantity) || mvQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                if (!mvIngoreFlag) {
                    errorMVSets.add(maintenanceGroupKey);
                }
                iterator.remove();
            } else {
                log.info(maintenanceGroupKey);
            }
        };

        Assert.isTrue(CollectionUtils.isEmpty(errorMVSets), "MV折算系数未命中" + StringUtils.join(errorMVSets, "、"));

        //保存
        ActualOutputServiceImpl beanHandle = SpringContextUtils.getBean(ActualOutputServiceImpl.class);
        beanHandle.batchSaveCellActualOutputHandler(inventoryDate, apiDataList, mwCoefficientMap, workAndBaseMap);
        log.info("syncCellActualOutput end... {}", LocalDateTime.now());
    }


    /**
     * AOP管理->配置管理->实际数据->组件实际产出 更新
     * @param inventoryDate
     * @param apiDataList
     * @param mwCoefficientMap
     * @param workAndBaseMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveCellActualOutputHandler(LocalDate inventoryDate,
                                                 List<BigdataCellActualOutputDTO> apiDataList,
                                                 Map<String, BigDecimal> mwCoefficientMap,
                                                 Map<String, ModuleBasePlace> workAndBaseMap) {
        Integer month = inventoryDate.getMonthValue();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        List<ActualOutput> allActualOutputList = Lists.newLinkedList();

        // AOP管理->配置管理->实际数据->电池实际产出
        List<ActualOutput> actualOutputList = this.queryByYearAndType(inventoryDate.getYear(), ProductTypeEnum.CELL);
        // 分组维度 = 国内/海外 + 组件型号 + 产品族 + 车间
        Map<String, ActualOutput> actualOutputMap = actualOutputList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getProductType(), ele.getProductSeries(), ele.getCellShard(),
                        ele.getWorkshop(), ele.getBasePlace(), ele.getYear()), "&"), Function.identity(), (v1, v2) -> v1));

        // 维度：country_flag + product_type + product_series + cell_shard + workshop + base_place + year
        Map<String, List<BigdataCellActualOutputDTO>> apiDataMap = apiDataList.stream().collect(Collectors.groupingBy(ele ->{
            ModuleBasePlace moduleBasePlace = workAndBaseMap.get(ele.getCtName());
            CountryFlagEnum countryFlagEnumDesc = CountryFlagEnum.getCountryFlagEnumDesc(moduleBasePlace.getIsOversea());
            String isOversea = Objects.nonNull(countryFlagEnumDesc) ? countryFlagEnumDesc.getCode() : moduleBasePlace.getIsOversea();
            String[] split = ele.getProductName().split("_");
            String cellModel = split[2] + "-" + split[1].replace("型", "");
            String cellShard = Objects.equals("二分", split[5]) ? Priority.CellShard.TWC.getCode() : Priority.CellShard.THC.getCode();
            return StringUtils.join(Arrays.asList(isOversea, ProductTypeEnum.CELL.getCode(),
                    cellModel, cellShard, ele.getCtName(), moduleBasePlace.getBasePlace(), inventoryDate.getYear()), "&");
        }));

        apiDataMap.forEach((groupKey, v) -> {
            String[] split = groupKey.split("&");
            BigDecimal mwCoefficient = mwCoefficientMap.get(StringUtils.join(Arrays.asList(split[0], v.get(0).getProductName()), "_"));

            if (actualOutputMap.containsKey(groupKey)) {
                ActualOutput actualOutput = actualOutputMap.get(groupKey);
                BigDecimal calculateQuantity = v.stream().map(ele -> {
                    BigDecimal primaryQuantity = new BigDecimal(ele.getPrimaryQuantity());
                    BigDecimal quantity = (Objects.isNull(primaryQuantity) || Objects.isNull(mwCoefficient) || mwCoefficient.compareTo(BigDecimal.ZERO) == 0)
                            ? BigDecimal.ZERO : primaryQuantity.divide(mwCoefficient, 6, RoundingMode.HALF_UP);
                    return quantity;
                }).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualOutput, fieldName, calculateQuantity);
                allActualOutputList.add(actualOutput);
            } else {
                ActualOutput actualOutput = new ActualOutput();
                actualOutput.setCountryFlag(split[0]);
                actualOutput.setProductType(split[1]);
                actualOutput.setProductSeries(split[2]);
                actualOutput.setCellShard(split[3]);
                actualOutput.setWorkshop(split[4]);
                actualOutput.setBasePlace(split[5]);
                actualOutput.setYear(Integer.valueOf(split[6]));
                actualOutput.setCreatedBy("-1");
                actualOutput.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal calculateQuantity = null;
                    if (curMonth < month) {
                        calculateQuantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        calculateQuantity = v.stream().map(ele -> {
                            BigDecimal primaryQuantity = new BigDecimal(ele.getPrimaryQuantity());
                            BigDecimal quantity = (Objects.isNull(primaryQuantity) || Objects.isNull(mwCoefficient) || mwCoefficient.compareTo(BigDecimal.ZERO) == 0)
                                    ? BigDecimal.ZERO : primaryQuantity.divide(mwCoefficient, 6, RoundingMode.HALF_UP);
                            return quantity;
                        }).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                    }
                    ReflectUtil.setFieldValue(actualOutput, curFieldName, calculateQuantity);
                });
                allActualOutputList.add(actualOutput);
            }
        });
        repository.saveAll(allActualOutputList);
    }

    private void pullCellActualOutputHandler(int page, LocalDate inventoryDate, List<BigdataCellActualOutputDTO> apiDataList) throws Exception {
        try {
            HashMap<String, Object> condition = Maps.newHashMap();
            condition.put("period_wid", inventoryDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String response = bigdataApiTypeFactory.executeByStrategy(BigdataTypeEnums.CELL_ACTUAL_OUTPUT, condition, page);
            TjResponse<BigdataCellActualOutputDTO> tjResponse = JSON.parseObject(response, new TypeReference<TjResponse<BigdataCellActualOutputDTO>>(BigdataCellActualOutputDTO.class) {});
            if (tjResponse.isSuccess()) {
                List<BigdataCellActualOutputDTO> spliteApiDataList = tjResponse.getResults();
                if (CollectionUtils.isNotEmpty(spliteApiDataList)) {
                    log.info("pullCellActualOutputHandler 第{}批次 apiDataList {}", page + 1, JSON.toJSON(spliteApiDataList));
                    apiDataList.addAll(spliteApiDataList);
                    //继续调用
                    page++;
                    pullCellActualOutputHandler(page, inventoryDate, apiDataList);
                }
            }
        } catch (Exception ex) {
            log.error("pullCellActualOutputHandler exception", ex);
            throw ex;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchSaveAopThirdAModuleProduceHandler(LocalDate inventoryDate,
                                                       List<BigdataModuleActualOutputDTO> apiDataList) {
        // 先全量清除
        aopThirdAModuleProduceRepository.deleteAllByYearAndMonth(inventoryDate.getYear(), String.valueOf(inventoryDate.getMonthValue()));
        List<BigdataModuleActualOutputDTO> effectApiDataList = apiDataList.stream().filter(ele -> ele.getSaleItem().contains("A-")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(effectApiDataList)) {
            return;
        }

        List<AopThirdAModuleProduce> resultList = Lists.newLinkedList();
        // 维度：国内海外 + 组件车间 + 产品族
        Map<String, List<BigdataModuleActualOutputDTO>> apiDataMap = effectApiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getWorkshop(), ele.getProductFamily())));
        apiDataMap.forEach((k, v) -> {
            BigdataModuleActualOutputDTO item = v.get(0);

            AopThirdAModuleProduce produce = new AopThirdAModuleProduce();
            produce.setCountryFlag(item.getIsOversea());
            produce.setWorkshop(item.getWorkshop());
            produce.setYear(inventoryDate.getYear());
            produce.setMonth(String.valueOf(inventoryDate.getMonthValue()));
            produce.setProductFamily(item.getProductFamily());
            produce.setProductionOutputMw(String.valueOf(v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)));
            produce.setProductionOutputWpc(String.valueOf(v.stream().map(ele -> new BigDecimal(ele.getWQuantity()))
                    .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)));
            produce.setCreatedBy("-1");
            produce.setUpdatedBy("-1");
            resultList.add(produce);
        });
        aopThirdAModuleProduceRepository.saveAll(resultList);
    }


    /**
     * AOP管理->配置管理->实际数据->组件实际产出 更新
     * @param inventoryDate
     * @param apiDataList
     * @param productInfoMap
     * @param workAndBaseMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveModuleActualOutputHandler(LocalDate inventoryDate,
                                                   List<BigdataModuleActualOutputDTO> apiDataList,
                                                   Map<String, String> productInfoMap,
                                                   Map<String, ModuleBasePlace> workAndBaseMap) {
        Integer month = inventoryDate.getMonthValue();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        List<ActualOutput> allActualOutputList = Lists.newLinkedList();

        // AOP管理->配置管理->实际数据->硅片实际产出
        List<ActualOutput> actualOutputList = this.queryByYearAndType(inventoryDate.getYear(), ProductTypeEnum.MODULE);
        // 分组维度 = 国内/海外 + 组件型号 + 产品族 + 车间 + 区域
        Map<String, ActualOutput> actualOutputMap = actualOutputList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductGroup(), ele.getWorkshop(), ele.getArea()), Function.identity(), (v1, v2) -> v1));

        // 维度：国内海外 + 产品族 + 组件车间 + 销售区域
        Map<String, List<BigdataModuleActualOutputDTO>> apiDataMap = apiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getProductFamily(), ele.getWorkshop(), ele.getRegion())));
        apiDataMap.forEach((groupKey, v) -> {
            BigdataModuleActualOutputDTO item = v.get(0);
            if (actualOutputMap.containsKey(groupKey)) {
                ActualOutput actualOutput = actualOutputMap.get(groupKey);
                BigDecimal quantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualOutput, fieldName, quantity.setScale(6, RoundingMode.HALF_UP));
                allActualOutputList.add(actualOutput);
            } else {
                ActualOutput actualOutput = new ActualOutput();
                actualOutput.setCountryFlag(item.getIsOversea());
                actualOutput.setProductType(ProductTypeEnum.MODULE.getCode());
                actualOutput.setProductSeries(productInfoMap.get(item.getProductFamily()));
                actualOutput.setProductGroup(item.getProductFamily());
                actualOutput.setWorkshop(item.getWorkshop());
                actualOutput.setYear(Integer.valueOf(item.getYear()));
                actualOutput.setBasePlace(workAndBaseMap.get(item.getWorkshop()).getBasePlace());
                actualOutput.setArea(item.getRegion());
                actualOutput.setCreatedBy("-1");
                actualOutput.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal quantity = null;
                    if (curMonth < month) {
                        quantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        quantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(6, RoundingMode.HALF_UP);
                    }
                    ReflectUtil.setFieldValue(actualOutput, curFieldName, quantity);
                });
                allActualOutputList.add(actualOutput);
            }
        });
        repository.saveAll(allActualOutputList);
    }

    private void pullModuleActualOutputHandler(int page, LocalDate inventoryDate, List<BigdataModuleActualOutputDTO> apiDataList) throws Exception {
        try {
            HashMap<String, Object> condition = Maps.newHashMap();
            condition.put("month", inventoryDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String response = bigdataApiTypeFactory.executeByStrategy(BigdataTypeEnums.MODULE_ACTUAL_OUTPUT, condition, page);
            TjResponse<BigdataModuleActualOutputDTO> tjResponse = JSON.parseObject(response, new TypeReference<TjResponse<BigdataModuleActualOutputDTO>>(BigdataModuleActualOutputDTO.class) {});
            if (tjResponse.isSuccess()) {
                List<BigdataModuleActualOutputDTO> spliteApiDataList = tjResponse.getResults();
                if (CollectionUtils.isNotEmpty(spliteApiDataList)) {
                    log.info("pullModuleActualOutputHandler 第{}批次 apiDataList {}", page + 1, JSON.toJSON(spliteApiDataList));
                    apiDataList.addAll(spliteApiDataList);
                    //继续调用
                    page++;
                    pullModuleActualOutputHandler(page, inventoryDate, apiDataList);
                }
            }
        } catch (Exception ex) {
            log.error("pullModuleActualOutputHandler exception", ex);
            throw ex;
        }
    }

    /**
     * AOP管理->配置管理->实际数据->硅片实际产出 更新
     * @param inventoryDate
     * @param apiDataList
     * @param cellWaferWeightMaintenanceMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveActualOutputHandler(LocalDate inventoryDate,
                                             List<BigdataWaferActualPurchaseDTO> apiDataList,
                                             Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceMap,
                                             Boolean mvIngoreFlag) {
        List<String> effectOrgCodeList = bigdataApiTypeFactory.getAllEffectOrgCodeList();
        // p2_2772 AOP实际数接口-硅片实际采购-增加账套和供应商过滤(仅保留SUPPLIER值为TOD(SH)的数据)
        List<BigdataWaferActualPurchaseDTO> externalApiDataList = apiDataList.stream().filter(ele ->
                !Objects.equals("External", ele.getIntercompanyFlag()) && effectOrgCodeList.contains(ele.getOrgCode())
                        && Objects.equals("TOD(SH)", ele.getSupplier())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(externalApiDataList)) {
            log.info("batchSaveActualOutputHandler externalApiDataList empty");
            return;
        }

        Integer month = inventoryDate.getMonthValue();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        List<ActualOutput> allActualOutputList = Lists.newLinkedList();

        // AOP管理->配置管理->实际数据->硅片实际产出
        List<ActualOutput> actualOutputList = this.queryByYearAndType(inventoryDate.getYear(), ProductTypeEnum.WAFER);
        Map<String, ActualOutput> actualOutputMap = actualOutputList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductSeries(), ele.getWorkshop()), Function.identity(), (v1, v2) -> v1));

        Map<String, List<BigdataWaferActualPurchaseDTO>> apiDataMap = externalApiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getCellType(), ele.getOrgCode())));

        Set<String> errorMVSets = Sets.newHashSet();
        apiDataMap.forEach((groupKey, v) -> {
            BigdataWaferActualPurchaseDTO item = v.get(0);

            // 当月的硅片-MW折算系数
            String maintenanceGroupKey = StringUtils.join(item.getIsOversea(), item.getCellType());
            CellWaferWeightMaintenanceDTO maintenanceDTO = cellWaferWeightMaintenanceMap.get(maintenanceGroupKey);
            BigDecimal mvQuantity;
            if (Objects.isNull(maintenanceDTO)) {
                mvQuantity = BigDecimal.ZERO;
                if (!mvIngoreFlag) {
                    errorMVSets.add(maintenanceGroupKey);
                }
                return;
            } else {
                mvQuantity = Objects.isNull(maintenanceDTO) ?
                        BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(maintenanceDTO, fieldName);
                if (Objects.isNull(mvQuantity) || mvQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    if (!mvIngoreFlag) {
                        errorMVSets.add(maintenanceGroupKey);
                    }
                    return;
                }
            }

            if (actualOutputMap.containsKey(groupKey)) {
                ActualOutput actualOutput = actualOutputMap.get(groupKey);
                BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualOutput, fieldName, Objects.equals(BigDecimal.ZERO, mvQuantity) ?
                        BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP));
                allActualOutputList.add(actualOutput);
            } else {
                ActualOutput actualOutput = new ActualOutput();
                actualOutput.setCountryFlag(item.getIsOversea());
                actualOutput.setProductType(ProductTypeEnum.WAFER.getCode());
                actualOutput.setProductSeries(item.getCellType());
                actualOutput.setWorkshop(item.getOrgCode());
                actualOutput.setYear(Integer.valueOf(v.get(0).getYear()));
                actualOutput.setCreatedBy("-1");
                actualOutput.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal quantity = null;
                    if (curMonth < month) {
                        quantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantity = Objects.equals(BigDecimal.ZERO, mvQuantity) ? BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP);
                    }
                    ReflectUtil.setFieldValue(actualOutput, curFieldName, quantity);
                });
                allActualOutputList.add(actualOutput);
            }
        });
        Assert.isTrue(CollectionUtils.isEmpty(errorMVSets), "MV折算系数未命中" + StringUtils.join(errorMVSets, "、"));
        if (CollectionUtils.isEmpty(allActualOutputList)) return;
        repository.saveAll(allActualOutputList);
    }


    /**
     * AOP管理->配置管理->实际数据->外卖&外购数据->电池硅片外购 更新
     * @param inventoryDate
     * @param apiDataList
     * @param cellWaferWeightMaintenanceMap
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveActualOtherHandler(LocalDate inventoryDate,
                                            List<BigdataWaferActualPurchaseDTO> apiDataList,
                                            Map<String, CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceMap,
                                            Boolean mvIngoreFlag) {
        // p2_2772 AOP实际数接口-硅片实际采购-增加账套和供应商过滤(仅保留SUPPLIER值为TOD(SH)的数据)
        List<BigdataWaferActualPurchaseDTO> externalApiDataList = apiDataList.stream().filter(ele ->
                Objects.equals("External", ele.getIntercompanyFlag()) && Objects.equals("TOD(SH)", ele.getOrgCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(externalApiDataList)) {
            log.info("batchSaveActualOtherHandler externalApiDataList empty");
            return;
        }

        Integer month = inventoryDate.getMonthValue();
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        List<ActualOther> allActualOtherList = Lists.newLinkedList();

        // AOP管理->配置管理->实际数据->外卖&外购数据->电池硅片外购
        List<ActualOther> actualOtherList = actualOtherService.queryPurchaseByYearType(inventoryDate.getYear(), ProductTypeEnum.WAFER, CountryFlagEnum.INLAND);
        List<ActualOther> overseaActualOtherList = actualOtherService.queryPurchaseByYearType(inventoryDate.getYear(), ProductTypeEnum.WAFER, CountryFlagEnum.OVERSEA);
        actualOtherList.addAll(overseaActualOtherList);
        Map<String, ActualOther> actualOtherMap = actualOtherList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductSeries()), Function.identity(), (v1, v2) -> v1));

        Set<String> errorMVSets = Sets.newHashSet();
        Map<String, List<BigdataWaferActualPurchaseDTO>> apiDataMap = externalApiDataList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getCellType())));
        apiDataMap.forEach((groupKey, v) -> {
            BigdataWaferActualPurchaseDTO item = v.get(0);
            // 当月的硅片-MW折算系数
            String maintenanceGroupKey = StringUtils.join(item.getIsOversea(), item.getCellType());
            CellWaferWeightMaintenanceDTO maintenanceDTO = cellWaferWeightMaintenanceMap.get(maintenanceGroupKey);
            BigDecimal mvQuantity;
            if (Objects.isNull(maintenanceDTO)) {
                mvQuantity = BigDecimal.ZERO;
                if (!mvIngoreFlag) {
                    errorMVSets.add(maintenanceGroupKey);
                }
                return;
            } else {
                mvQuantity = Objects.isNull(maintenanceDTO) ?
                        BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(maintenanceDTO, fieldName);
                if (Objects.isNull(mvQuantity) || mvQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                    if (!mvIngoreFlag) {
                        errorMVSets.add(maintenanceGroupKey);
                    }
                    return;
                }
            }

            if (actualOtherMap.containsKey(groupKey)) {
                ActualOther actualOther = actualOtherMap.get(groupKey);
                BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                ReflectUtil.setFieldValue(actualOther, fieldName, Objects.equals(BigDecimal.ZERO, mvQuantity) ?
                        BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP));
                allActualOtherList.add(actualOther);
            } else {
                ActualOther actualOther = new ActualOther();
                actualOther.setCountryFlag(item.getIsOversea());
                actualOther.setProductType(ProductTypeEnum.WAFER.getCode());
                actualOther.setProductFrom(ProductFromEnum.PURCHASE.getCode());
                actualOther.setProductSeries(item.getCellType());
                actualOther.setYear(Integer.valueOf(item.getYear()));
                actualOther.setCreatedBy("-1");
                actualOther.setUpdatedBy("-1");
                // 默认未来月份的数量为空、过去月份的数量为0，当前月份按接口获取的数据更新
                IntStream.rangeClosed(1, 12).forEach(curMonth -> {
                    String curFieldName = String.format(AdjustConstant.MQUANTITY, curMonth);
                    BigDecimal quantity = null;
                    if (curMonth < month) {
                        quantity = BigDecimal.ZERO;
                    } else if (curMonth == month) {
                        BigDecimal thirdQuantity = v.stream().map(ele -> new BigDecimal(ele.getQuantity()))
                                .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                        quantity = Objects.equals(BigDecimal.ZERO, mvQuantity) ? BigDecimal.ZERO : thirdQuantity.divide(mvQuantity, 6, RoundingMode.HALF_UP);
                    }
                    ReflectUtil.setFieldValue(actualOther, curFieldName, quantity);
                });
                allActualOtherList.add(actualOther);
            }
        });
        Assert.isTrue(CollectionUtils.isEmpty(errorMVSets), "MV折算系数未命中" + StringUtils.join(errorMVSets, "、"));
        if (CollectionUtils.isEmpty(allActualOtherList)) return;
        actualOtherRepository.saveAll(allActualOtherList);
    }

    public Map<String, CellWaferWeightMaintenanceDTO> getCellWaferWeightMaintenanceMap(Integer year) {
        CellWaferWeightMaintenanceQuery query = new CellWaferWeightMaintenanceQuery();
        query.setYear(year);
        query.setProductType(ProductTypeEnum.WAFER);
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        Page<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMaintenanceService.queryByPage(query);
        return maintenanceDTOS.getContent().stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getCellModel()), Function.identity(), (v1, v2) -> v1));
    }

    private void pullWaferActualPurchaseHandler(int page, LocalDate inventoryDate, List<BigdataWaferActualPurchaseDTO> apiDataList) throws Exception {
        try {
            HashMap<String, Object> condition = Maps.newHashMap();
            condition.put("month", inventoryDate.format(DateTimeFormatter.ofPattern("yyyyMM")));
            String response = bigdataApiTypeFactory.executeByStrategy(BigdataTypeEnums.WAFER_ACTUAL_PURCHASE, condition, page);
            TjResponse<BigdataWaferActualPurchaseDTO> tjResponse = JSON.parseObject(response, new TypeReference<TjResponse<BigdataWaferActualPurchaseDTO>>(BigdataWaferActualPurchaseDTO.class) {});
            if (tjResponse.isSuccess()) {
                List<BigdataWaferActualPurchaseDTO> spliteApiDataList = tjResponse.getResults();
                if (CollectionUtils.isNotEmpty(spliteApiDataList)) {
                    log.info("pullWaferActualPurchaseHandler 第{}批次 apiDataList {}", page + 1, JSON.toJSON(spliteApiDataList));
                    apiDataList.addAll(spliteApiDataList);
                    //继续调用
                    page++;
                    pullWaferActualPurchaseHandler(page, inventoryDate, apiDataList);
                }
            }
        } catch (Exception ex) {
            log.error("pullWaferActualPurchaseHandler exception", ex);
            throw ex;
        }
    }
}









