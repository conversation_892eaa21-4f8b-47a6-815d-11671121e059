package com.trinasolar.scp.aop.service.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.querydsl.core.BooleanBuilder;
import com.trinasolar.scp.aop.domain.base.CalculateAllocationResultDTO;
import com.trinasolar.scp.aop.domain.constant.QPConstant;
import com.trinasolar.scp.aop.domain.dto.*;
import com.trinasolar.scp.aop.domain.dto.budget.OverallBudgetCellPurchaseDTO;
import com.trinasolar.scp.aop.domain.dto.budget.OverallBudgetUnifyDTO;
import com.trinasolar.scp.aop.domain.entity.*;
import com.trinasolar.scp.aop.domain.enums.CountryFlagEnum;
import com.trinasolar.scp.aop.domain.enums.LovHeaderCodeConstant;
import com.trinasolar.scp.aop.domain.enums.ProductFromEnum;
import com.trinasolar.scp.aop.domain.priority.Priority;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.query.ProductGroupPercentQuery;
import com.trinasolar.scp.aop.service.feign.client.OverallBudgetFeignClient;
import com.trinasolar.scp.aop.service.feign.client.PowerFeignClient;
import com.trinasolar.scp.aop.service.repository.AopCalculateAOutgoingRepository;
import com.trinasolar.scp.aop.service.repository.CalculateCellMinusRepository;
import com.trinasolar.scp.aop.service.repository.ImportActualCapacityBohRepository;
import com.trinasolar.scp.aop.service.service.*;
import com.trinasolar.scp.aop.service.utils.AdjustConstant;
import com.trinasolar.scp.aop.service.utils.DataHandleUtils;
import com.trinasolar.scp.aop.service.utils.IterableUtils;
import com.trinasolar.scp.common.api.base.LovLineDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.BizException;
import com.trinasolar.scp.common.api.util.LovUtils;
import com.trinasolar.scp.common.api.util.Results;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @Date 2024/5/6
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OverallBudgetServiceImpl implements OverallBudgetService {
    private final ImportActualCapacityService importActualCapacityService;

    private final HorizontalOrVerticalService horizontalOrVerticalService;

    private final ActualOtherService actualOtherService;

    private final CalculateSchedulingService calculateSchedulingService;

    private final CalculatePurchaseDemandService calculatePurchaseDemandService;

    private final CellDistributionService cellDistributionService;

    private final PowerFeignClient powerFeignClient;

    private final OverallBudgetFeignClient overallBudgetFeignClient;

    private final ProductGroupPercentService productGroupPercentService;

    private final AdjustImportDetailService adjustImportDetailService;

    private final ImportActualCapacityBohRepository importActualCapacityBohRepository;

    private final CalculateActualEohService calculateActualEohService;

    private final ComponentWorkshopUsageService componentWorkshopUsageService;

    private final CalculateCellMinusRepository calculateCellMinusRepository;

    private final CellWaferWeightMaintenanceService cellWaferWeightMaintenanceService;

    private final AopCalculateAOutgoingRepository aopCalculateAOutgoingRepository;

    private final ProductInfoService productInfoService;

    @Autowired
    private CalculateAllocationService calculateAllocationService;

    // 全面预算仅支持国内/海外,不支持印尼等
    private final static List<String> EFFECT_COUNTRY_LIST = ImmutableList.of(CountryFlagEnum.INLAND.getCode(), CountryFlagEnum.OVERSEA.getCode());

    @Override
    public void syncModuleOutput(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryModuleOutputList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 组件产品系列排产量接口 syncModuleOutput empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 组件产品系列排产量接口 syncModuleOutput 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncModuleOutput(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 组件产品系列排产量接口 syncModuleOutput 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 组件产品系列排产量接口 syncModuleOutput 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 组件产品系列排产量接口 syncModuleOutput 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryModuleOutputList(OverallBudgetBaseDTO baseDTO) {
        List<ImportActualCapacity> importActualCapacityList = importActualCapacityService.queryByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.MODULE);
        importActualCapacityList = importActualCapacityList.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(importActualCapacityList)) {
            return Collections.emptyList();
        }
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        importActualCapacityList.forEach(ele -> {
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setWorkShop(Objects.equals(ProductFromEnum.OEM.getCode(), ele.getProductFrom())
                        ? ProductFromEnum.OEM.getCode() : ele.getWorkshop());
                outputDTO.setProductSeries(ele.getProductSeries());
                outputDTO.setScheduleYield(Objects.isNull(quantity) ? BigDecimal.ZERO : quantity);
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    @Override
    public void syncHorizontalOrVertical(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryHorizontalOrVerticalList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 组件属性横竖装比例 syncHorizontalOrVertical empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 组件属性横竖装比例 syncHorizontalOrVertical 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncHorizontalOrVertical(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 组件属性横竖装比例 syncHorizontalOrVertical 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 组件属性横竖装比例 syncHorizontalOrVertical 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 组件属性横竖装比例 syncHorizontalOrVertical 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryHorizontalOrVerticalList(OverallBudgetBaseDTO baseDTO) {
        List<HorizontalOrVertical> horizontalOrVerticals = horizontalOrVerticalService.findAllByYearAndDataVersion(Integer.valueOf(baseDTO.getYears()), baseDTO.getDataVersion());
        horizontalOrVerticals = horizontalOrVerticals.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(horizontalOrVerticals)) {
            return Collections.emptyList();
        }
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        horizontalOrVerticals.forEach(ele -> {
            IntStream.rangeClosed(1, 4).forEach(quarter -> {
                String fieldName = String.format(AdjustConstant.QPERCENT, quarter);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod("Q" + quarter);
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setProductSeries(ele.getProductSeries());
                outputDTO.setCrossWiseRatio(Objects.isNull(quantity) ? BigDecimal.ZERO : (BigDecimal.ONE.subtract(quantity.setScale(8, RoundingMode.HALF_UP))));
                outputDTO.setUpRightRatio(Objects.isNull(quantity) ? BigDecimal.ZERO : quantity.setScale(8, RoundingMode.HALF_UP));
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    @Override
    public void syncProductFamilyRaito(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryProductFamilyRaitoList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 产品族比例 syncProductFamilyRaito empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 产品族比例 syncProductFamilyRaito 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncProductFamilyRaito(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 产品族比例 syncProductFamilyRaito 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 产品族比例 syncProductFamilyRaito 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 产品族比例 syncProductFamilyRaito 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryProductFamilyRaitoList(OverallBudgetBaseDTO baseDTO) {
        ProductGroupPercentQuery query = new ProductGroupPercentQuery();
        query.setYear(Integer.valueOf(baseDTO.getYears()));
        query.setCalculateVersion(baseDTO.getDataVersion());
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        Page<ProductGroupPercentDTO> productGroupPercentDTOS = productGroupPercentService.queryByPage(query);
        List<ProductGroupPercentDTO> percentDTOS = productGroupPercentDTOS.getContent().stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(percentDTOS)) {
            return Collections.emptyList();
        }
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        percentDTOS.forEach(ele -> {
            IntStream.rangeClosed(1, 4).forEach(quarter -> {
                String fieldName = String.format(AdjustConstant.QPERCENT, quarter);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod("Q" + quarter);
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setProductSeries(ele.getProductSeries());
                outputDTO.setProductFamily(ele.getProductGroup());
                outputDTO.setScheduleYield(quantity.setScale(8, RoundingMode.HALF_UP));
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    @Override
    public void syncCellSchedule(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryCellScheduleList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 电池排产量 syncCellSchedule empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 电池排产量 syncCellSchedule 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncCellSchedule(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 电池排产量 syncCellSchedule 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 电池排产量 syncCellSchedule 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 电池排产量 syncCellSchedule 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryCellScheduleList(OverallBudgetBaseDTO baseDTO) {
        Map<String, LovLineDTO> aopCellShardMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        Map<String, LovLineDTO> effectPowerCellMap = this.buildEffectPowerCellMap();
//        Map<String, BigDecimal> mwCoefficientMap = this.buildMwCoefficientMap();

        List<CellDistribution> cellDistributions = cellDistributionService.queryCellDistributionByVersion(baseDTO.getDataVersion());
        cellDistributions = cellDistributions.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        // 过滤掉行全部为0的数据
        cellDistributions = cellDistributions.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());

        // p2_1748 全面预算接口对接-组件车间A-使用量 A-相关数据
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        List<OverallBudgetUnifyDTO> resultList1 = Lists.newLinkedList();
        Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMap = this.getCellWeightMap();
        List<CalculateCellMinus> ieaMinusSupply = calculateCellMinusRepository.findAllByYearEqualsAndDataVersionEqualsAndProductFromEquals(baseDTO.getYears(), baseDTO.getDataVersion(), com.trinasolar.scp.common.api.enums.ProductFromEnum.IE.getCode());
        // 过滤掉行全部为0的数据
        ieaMinusSupply = ieaMinusSupply.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());

        getAVoid1(baseDTO, ieaMinusSupply, effectPowerCellMap, cellWaferWeightMap, resultList1);
        getAVoid(baseDTO, ieaMinusSupply, effectPowerCellMap, cellWaferWeightMap, resultList);
        Map<String, List<OverallBudgetUnifyDTO>> budgetMap = resultList1.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(Arrays.asList(ele.getWorkShop(), ele.getPeriod(), ele.getBatteryType() + "-" + ele.getBatteryModel().replace("型", ""), ele.getShardMethod()), "_")));

        cellDistributions.forEach(ele -> {
            IntStream.rangeClosed(1, 12).forEach(month -> {
                Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getProductSeries());

                LovLineDTO cellShardLovLineDTO = aopCellShardMap.get(ele.getCellShard());
                String cellShard = Objects.nonNull(cellShardLovLineDTO) ? cellShardLovLineDTO.getLovName() : null;
                Assert.notNull(cellShard, String.format("LOV-AOP电池分片方式【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
                        ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));

                LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getProductSeries(), cellShard));
                Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                        ele.getProductSeries(), cellShard));
                List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = budgetMap.get(StringUtils.join(Arrays.asList(ele.getWorkshop(), String.valueOf(month), ele.getProductSeries().replace("型", ""), cellShard), "_"));
                BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(overallBudgetUnifyDTOS) ? BigDecimal.ZERO :
                        DataHandleUtils.calSumZero(overallBudgetUnifyDTOS.stream().map(OverallBudgetUnifyDTO::getMasterPlanMw)
                        .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);
                Optional<OverallBudgetUnifyDTO> first = overallBudgetUnifyDTOS.stream().findFirst();
                BigDecimal cellWaferQuantityTemp =  first.isPresent() ? first.get().getCellWaferQuantityTemp() : BigDecimal.ZERO;
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                BigDecimal masterPlanMw = Objects.isNull(quantity) ? BigDecimal.ZERO : quantity.setScale(8, RoundingMode.HALF_UP);
//                BigDecimal mwCoefficient = mwCoefficientMap.getOrDefault(StringUtils.join(ele.getCountryFlag(), cellSeriesLovLineDTO.getLovName()), BigDecimal.ONE);
                BigDecimal effectMasterPlanMw = masterPlanMw.subtract(cellWaferQuantity);

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setWorkShop(ele.getWorkshop());
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(typeAndModelPair.getKey());
                outputDTO.setBatteryModel(typeAndModelPair.getValue().endsWith("2.0") ? (StringUtils.join(typeAndModelPair.getValue().replace("2.0", "").trim(), "型"))
                        : StringUtils.join(typeAndModelPair.getValue(), "型"));
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                outputDTO.setProductFamily(cellSeriesLovLineDTO.getLovName());
                String shardMethodLov = convertShardMethodLov(typeAndModelPair.getKey(), cellSeriesLovLineDTO);
                outputDTO.setShardMethod(shardMethodLov);
                outputDTO.setOem(this.convertOEM(outputDTO.getBatteryType(), outputDTO.getBatteryModel()));
                outputDTO.setMasterPlanPc(effectMasterPlanMw.multiply(cellWaferQuantityTemp).multiply(BigDecimal.valueOf(10000)).setScale(8, RoundingMode.HALF_UP));
                outputDTO.setMasterPlanMw(effectMasterPlanMw);
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    private void getAVoid1(OverallBudgetBaseDTO baseDTO, List<CalculateCellMinus> ieaMinusSupply, Map<String, LovLineDTO> effectPowerCellMap, Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMap, List<OverallBudgetUnifyDTO> resultList) {
        ieaMinusSupply.forEach(ele -> {
            String[] split = ele.getCellType().split("_");
            String[] cellSplit = ele.getCellModel().split("-");
            String shardMethod = Objects.equals(Priority.CellShard.TWC.getCode() , ele.getFragmentType()) ? "二分片" : "三分片";
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getCellModel(), shardMethod));
            Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                    ele.getCellModel(), shardMethod));
            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getCellModel());

            String fragmentType = getFragmentType(ele);
            List<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMap.get(
                    StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getCellModel(), fragmentType, ele.getYear()), "_"));
            Assert.isTrue(!CollectionUtils.isEmpty(maintenanceDTOS), String.format("MW折算系数-电池 【国内/海外：%s】【电池型号：%s】【分片方式：%s】【年份：%s】",
                    ele.getCountryFlag(), ele.getCellModel(), ele.getFragmentType(), ele.getYear()));

            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fileName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fileName);
                BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(maintenanceDTOS) ? BigDecimal.ZERO :
                        DataHandleUtils.calSumZero(maintenanceDTOS.stream().map(itemSummary -> (BigDecimal) ReflectUtil.getFieldValue(itemSummary, fileName))
                        .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setWorkShop(ele.getWorkshop());
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(cellSplit[0]);
                outputDTO.setBatteryModel(cellSplit[1]);
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                outputDTO.setShardMethod(Objects.equals(Priority.CellShard.TWC.getCode() , ele.getFragmentType()) ? "二分片" : "三分片");
                outputDTO.setOem(this.convertOEM(typeAndModelPair.getKey(), typeAndModelPair.getValue()));
                outputDTO.setMasterPlanPc(quantity.multiply(cellWaferQuantity).multiply(BigDecimal.valueOf(10000).setScale(8, RoundingMode.HALF_UP)));
                outputDTO.setMasterPlanMw(quantity.setScale(8, RoundingMode.HALF_UP));
                outputDTO.setCellWaferQuantityTemp(cellWaferQuantity);
                outputDTO.setBatteryRating("A-");
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
    }

    private static String getFragmentType(CalculateCellMinus ele) {
        String fragmentType = ele.getFragmentType();
        if(ele.getCellModel().contains("210R-N") && Objects.equals(Priority.CellShard.THC.getCode() , ele.getFragmentType())){
            fragmentType = Priority.CellShard.TWC.getCode();
        }
        return fragmentType;
    }

    private void getAVoid(OverallBudgetBaseDTO baseDTO, List<CalculateCellMinus> ieaMinusSupply, Map<String, LovLineDTO> effectPowerCellMap, Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMap, List<OverallBudgetUnifyDTO> resultList) {
        ieaMinusSupply.forEach(ele -> {
            String[] split = ele.getCellType().split("_");
            String[] cellSplit = ele.getCellModel().split("-");
            String shardMethod = Objects.equals(Priority.CellShard.TWC.getCode() , ele.getFragmentType()) ? "二分片" : "三分片";
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getCellModel(), shardMethod));
            Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                    ele.getCellModel(), shardMethod));
            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getCellModel());
            String fragmentType = getFragmentType(ele);
            List<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMap.get(
                    StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getCellModel(), fragmentType, ele.getYear()), "_"));
            Assert.isTrue(!CollectionUtils.isEmpty(maintenanceDTOS), String.format("MW折算系数-电池 【国内/海外：%s】【电池型号：%s】【分片方式：%s】【年份：%s】",
                    ele.getCountryFlag(), ele.getCellModel(), ele.getFragmentType(), ele.getYear()));

            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fileName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fileName);
                BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(maintenanceDTOS) ? BigDecimal.ZERO :
                        DataHandleUtils.calSumZero(maintenanceDTOS.stream().map(itemSummary -> (BigDecimal) ReflectUtil.getFieldValue(itemSummary, fileName))
                                .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setWorkShop(ele.getWorkshop());
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(cellSplit[0]);
                outputDTO.setBatteryModel(cellSplit[1].replace("型", "").endsWith("2.0") ? (StringUtils.join(cellSplit[1].replace("型", "").replace("2.0", "").trim(), "型"))
                        : StringUtils.join(cellSplit[1].replace("型", ""), "型"));
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                outputDTO.setShardMethod(Objects.equals(Priority.CellShard.TWC.getCode() , ele.getFragmentType()) ?
                        (cellSplit[1].replace("型", "").endsWith("2.0") ? "二半片" : "二分片") : (cellSplit[1].replace("型", "").endsWith("2.0") ? "三半片" : "三分片"));
                outputDTO.setOem(this.convertOEM(typeAndModelPair.getKey(), typeAndModelPair.getValue()));
                outputDTO.setMasterPlanPc(quantity.multiply(cellWaferQuantity).multiply(BigDecimal.valueOf(10000).setScale(8, RoundingMode.HALF_UP)));
                outputDTO.setMasterPlanMw(quantity.setScale(8, RoundingMode.HALF_UP));
                outputDTO.setCellWaferQuantityTemp(cellWaferQuantity);
                outputDTO.setBatteryRating("A-");
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
    }

    private Map<String, List<CellWaferWeightMaintenanceDTO>> getCellWeightMap() {
        CellWaferWeightMaintenanceQuery query = new CellWaferWeightMaintenanceQuery();
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setProductType(ProductTypeEnum.CELL);
        Page<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS = cellWaferWeightMaintenanceService.queryByPage(query);
        return cellWaferWeightMaintenanceDTOS.getContent().stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getCellModel(), ele.getCellShard(), ele.getYear()), "_")));
    }

    private Map<String, List<CellWaferWeightMaintenanceDTO>> getWaferWeightMap() {
        CellWaferWeightMaintenanceQuery query = new CellWaferWeightMaintenanceQuery();
        query.setPageNumber(1);
        query.setPageSize(Integer.MAX_VALUE);
        query.setProductType(ProductTypeEnum.WAFER);
        Page<CellWaferWeightMaintenanceDTO> cellWaferWeightMaintenanceDTOS = cellWaferWeightMaintenanceService.queryByPage(query);
        return cellWaferWeightMaintenanceDTOS.getContent().stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getCellModel(), ele.getYear()), "_")));
    }

    private String convertMainGridNumLov(LovLineDTO cellSeriesLovLineDTO) {
        String mainGridNum = null;
        if (StringUtils.isNumeric(cellSeriesLovLineDTO.getAttribute3())) {
            LovLineDTO lovLineDTO = LovUtils.get(Long.valueOf(cellSeriesLovLineDTO.getAttribute3()));
            if (Objects.nonNull(lovLineDTO)) {
                mainGridNum = lovLineDTO.getLovName();
            }
        } else {
            mainGridNum = cellSeriesLovLineDTO.getAttribute3();
        }
        return mainGridNum;
    }

    private String convertShardMethodLov(String batteryType, LovLineDTO cellSeriesLovLineDTO) {
        // 【全面预算接口】电池型号对接逻辑调整-分片方式优化
        // 由于上个需求优化时是改为使用LOV：APS_POWER_CELL_SERIES_TYPE 通过电池型号+分片方式获取对应的LOV NAME中维护的电池类型，
        // 按“_”拆分后截取最后一段传递给全面预算，但是电池类型需要从单晶_N型_210R_双面_TONCON_16BB_二半片改为单晶_N型_210R_双面_TONCON2.0_16BB_二分，
        // 因此需要优化；
        // 还是根据电池类型，若电池类型包含2.0，则将分片方式的half-cut改为二半片，3-cut 改为三半片
        // 修改后的分片方式逻辑
        //outputDTO.setShardMethod(finalCellShardName.contains("二分片") ? "二半片" : (finalCellShardName.contains("三分片") ? "三半片" : null));
        String[] s = cellSeriesLovLineDTO.getLovName().split("_");
        String shardSegment = s[s.length - 1];
        if (batteryType.contains("2.0")) {
            // 如果电池类型包含2.0，则将分片方式转换
            return shardSegment.contains("half-cut") ? "二半片" :
                            shardSegment.contains("3-cut") ? "三半片" :
                                    shardSegment.contains("片") ? shardSegment : shardSegment + "片";
        } else {
            // 否则保持原逻辑
            return s[s.length - 1].contains("片") ? s[s.length - 1] : s[s.length - 1] + "片";
        }
    }

    @Override
    public void syncCellSale(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryCellSaleList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 电池销售 syncCellSale empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 电池销售 syncCellSale 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncCellSale(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 电池销售 syncCellSale 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 电池销售 syncCellSale 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 电池销售 syncCellSale 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryCellSaleList(OverallBudgetBaseDTO baseDTO) {
        Map<String, LovLineDTO> effectPowerCellMap = this.buildEffectPowerCellMap();
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();

        List<AopCalculateAOutgoing> aopCalculateAOutgoings = aopCalculateAOutgoingRepository.findAllByDataVersionAndYear(baseDTO.getDataVersion(), Integer.valueOf(baseDTO.getYears()));
        // 过滤掉行全部为0的数据
        aopCalculateAOutgoings = aopCalculateAOutgoings.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        aopCalculateAOutgoings.forEach(ele -> {
            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getCellModel());

            String cellShardName = "";
            if (ObjectUtils.isNotEmpty(ele.getCellShard())) {
                LovLineDTO cellShard = LovUtils.get(LovHeaderCodeConstant.AOP_CELL_SHARD, ele.getCellShard());
                if (Objects.nonNull(cellShard)) {
                    cellShardName = cellShard.getLovName();
                }
            }
            String finalCellShardName = cellShardName;
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getProductSeries(), finalCellShardName));
            Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                    ele.getProductSeries(), ele.getCellShard()));

            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(typeAndModelPair.getKey());
                outputDTO.setBatteryModel(typeAndModelPair.getValue().endsWith("2.0") ? (StringUtils.join(typeAndModelPair.getValue().replace("2.0", "").trim(), "型"))
                        : StringUtils.join(typeAndModelPair.getValue(), "型"));
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                String[] s = cellSeriesLovLineDTO.getLovName().split("_");
                outputDTO.setShardMethod(s[s.length - 1].contains("片") ? s[s.length - 1] : s[s.length - 1]+"片");
                //outputDTO.setShardMethod(finalCellShardName.contains("二分片") ? "二半片" : (finalCellShardName.contains("三分片") ? "三半片" : null));
                outputDTO.setOem(this.convertOEM(outputDTO.getBatteryType(), outputDTO.getBatteryModel()));
                outputDTO.setScheduleYield(Objects.isNull(quantity) ? BigDecimal.ZERO : quantity);
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                outputDTO.setBatteryRating("A-");
                resultList.add(outputDTO);
            });
        });

        Map<String, List<OverallBudgetUnifyDTO>> budgetMap = resultList.stream().collect(Collectors.groupingBy(ele ->
                StringUtils.join(Arrays.asList(ele.getDomesticForeign(), ele.getPeriod(), ele.getBatteryType() + ele.getBatteryModel().replace("型", ""), ele.getShardMethod()), "_")));

        List<ActualOther> allActualOtherList = Lists.newLinkedList();
        Arrays.asList(ProductTypeEnum.values()).forEach(productTypeEnum -> {
            Arrays.asList(CountryFlagEnum.values()).forEach(countryFlagEnum -> {
                List<ActualOther> spliteActualOtherList = actualOtherService.queryOutgoingByYearType(Integer.valueOf(baseDTO.getYears()), productTypeEnum, countryFlagEnum);
                if (!CollectionUtils.isEmpty(spliteActualOtherList)) {
                    allActualOtherList.addAll(spliteActualOtherList);
                }
            });
        });

        List<ActualOther> finalAllActualOtherList = allActualOtherList.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag()) && Objects.equals("Q1", ele.getBatteryLevel())).collect(Collectors.toList());
        // 过滤掉行全部为0的数据
        finalAllActualOtherList = finalAllActualOtherList.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        finalAllActualOtherList.forEach(ele -> {
            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getProductSeries());
            String cellShardName = "";
            if (ObjectUtils.isNotEmpty(ele.getCellShard())) {
                LovLineDTO cellShard = LovUtils.get(LovHeaderCodeConstant.AOP_CELL_SHARD, ele.getCellShard());
                if (Objects.nonNull(cellShard)) {
                    cellShardName = cellShard.getLovName();
                }
            }
            String finalCellShardName = cellShardName;
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getProductSeries(), finalCellShardName));
            Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                    ele.getProductSeries(), ele.getCellShard()));
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String domesticForeign = Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外";
                List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = budgetMap.get(StringUtils.join(Arrays.asList(domesticForeign, String.valueOf(month), ele.getProductSeries().replace("型", ""), finalCellShardName), "_"));
                BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(overallBudgetUnifyDTOS) ? BigDecimal.ZERO :
                        DataHandleUtils.calSumZero(overallBudgetUnifyDTOS.stream().map(OverallBudgetUnifyDTO::getScheduleYield)
                                .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);

                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                quantity = Objects.isNull(quantity) ? BigDecimal.ZERO : quantity;

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(domesticForeign);
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(typeAndModelPair.getKey());
                outputDTO.setBatteryModel(typeAndModelPair.getValue().endsWith("2.0") ? (StringUtils.join(typeAndModelPair.getValue().replace("2.0", "").trim(), "型"))
                        : StringUtils.join(typeAndModelPair.getValue(), "型"));
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                String shardMethodLov = convertShardMethodLov(typeAndModelPair.getKey(), cellSeriesLovLineDTO);
                outputDTO.setShardMethod(shardMethodLov);
                outputDTO.setOem(this.convertOEM(outputDTO.getBatteryType(), outputDTO.getBatteryModel()));
                outputDTO.setScheduleYield(quantity.subtract(cellWaferQuantity));
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });


        Map<String, List<OverallBudgetUnifyDTO>> sumResultMap = resultList.stream().collect(Collectors.groupingBy(ele -> String.join(ele.getYears(),
                ele.getPeriod(), ele.getDomesticForeign(), ele.getCrystalType(), ele.getBatteryType(),
                ele.getBatteryModel(), ele.getMainGridNum(), ele.getShardMethod(), ele.getOem(), ele.getCreateDate(),
                ele.getVersionCode(), ele.getScenario(), ele.getBatteryRating())));
        List<OverallBudgetUnifyDTO> sumResultList = Lists.newLinkedList();
        sumResultMap.forEach((k, v) -> {
            OverallBudgetUnifyDTO overallBudgetUnifyDTO = v.get(0);
            overallBudgetUnifyDTO.setScheduleYield(v.stream().map(OverallBudgetUnifyDTO::getScheduleYield).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            sumResultList.add(overallBudgetUnifyDTO);
        });
        return sumResultList;
    }

    @Override
    public void syncCellPurchase(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryCellPurchaseList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 电池片采购量 syncCellPurchase empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 电池片采购量 syncCellPurchase 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncCellPurchase(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 电池片采购量 syncCellPurchase 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 电池片采购量 syncCellPurchase 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 电池片采购量 syncCellPurchase 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryCellPurchaseList(OverallBudgetBaseDTO baseDTO) {
        Map<String, LovLineDTO> aopCellShardMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.AOP_CELL_SHARD);
        Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMap = this.getCellWeightMap();
        List<CalculatePurchaseDemand> cellPurchaseDemand = calculatePurchaseDemandService.queryPurchaseDemandByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.CELL);
        List<Long> purchaseOldIdList = cellPurchaseDemand.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculatePurchaseDemand::getOldId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(purchaseOldIdList)) {
            cellPurchaseDemand.removeIf(item -> purchaseOldIdList.contains(item.getId()));
        }
        cellPurchaseDemand = cellPurchaseDemand.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        // 过滤掉行全部为0的数据
        cellPurchaseDemand = cellPurchaseDemand.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cellPurchaseDemand)) {
            return Collections.emptyList();
        }

        Map<String, LovLineDTO> effectPowerCellMap = this.buildEffectPowerCellMap();
//        Map<String, BigDecimal> mwCoefficientMap = this.buildMwCoefficientMap();

        List<String> errorMsgLovList = Lists.newLinkedList();
        List<String> errorMsgWeightList = Lists.newLinkedList();

        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        cellPurchaseDemand.forEach(ele -> {
            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(ele.getProductSeries());
            String shardMethod = "二分片";
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(ele.getProductSeries(), shardMethod));
            if (Objects.isNull(cellSeriesLovLineDTO)) {
                errorMsgLovList.add(String.format("【电池型号：%s 分片方式：%s】", ele.getProductSeries(), shardMethod));
            }

            LovLineDTO cellShardLovLineDTO = aopCellShardMap.get(shardMethod);
            String cellShard = Objects.nonNull(cellShardLovLineDTO) ? cellShardLovLineDTO.getLovName() : null;
            Assert.notNull(cellShard, String.format("LOV-AOP电池分片方式未维护 【分片方式：%s】", shardMethod));

            List<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMap.get(
                    StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getProductSeries(), cellShardLovLineDTO.getLovValue(), ele.getYear()), "_"));
            if (CollectionUtils.isEmpty(maintenanceDTOS)) {
                errorMsgWeightList.add(String.format("【国内/海外：%s 电池型号：%s 分片方式：%s 年份：%s】", ele.getCountryFlag(), ele.getProductSeries(), shardMethod, ele.getYear()));
            }

            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                BigDecimal masterPlanMw = Objects.isNull(quantity) ? BigDecimal.ZERO : quantity.setScale(8, RoundingMode.HALF_UP);
//                BigDecimal mwCoefficient = mwCoefficientMap.getOrDefault(StringUtils.join(ele.getCountryFlag(), cellSeriesLovLineDTO.getLovName()), BigDecimal.ONE);
                BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(maintenanceDTOS) ? BigDecimal.ZERO :
                        DataHandleUtils.calSumZero(maintenanceDTOS.stream().map(itemSummary -> (BigDecimal) ReflectUtil.getFieldValue(itemSummary, fieldName))
                                .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);

                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(ele.getYear()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(typeAndModelPair.getKey());
                outputDTO.setBatteryModel(typeAndModelPair.getValue().endsWith("2.0") ? (StringUtils.join(typeAndModelPair.getValue().replace("2.0", "").trim(), "型"))
                        : StringUtils.join(typeAndModelPair.getValue(), "型"));
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                String shardMethodLov = convertShardMethodLov(typeAndModelPair.getKey(), cellSeriesLovLineDTO);
                outputDTO.setShardMethod(shardMethodLov);
                outputDTO.setOem(this.convertOEM(outputDTO.getBatteryType(), outputDTO.getBatteryModel()));
                outputDTO.setProductFamily(cellSeriesLovLineDTO.getLovName());// todo check
//                outputDTO.setScheduleYield(Objects.isNull(quantity) ? BigDecimal.ZERO : quantity);
                outputDTO.setScheduleYield(masterPlanMw.multiply(cellWaferQuantity).multiply(BigDecimal.valueOf(10000).setScale(8, RoundingMode.HALF_UP)));
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });

        String errorMsg1 = CollectionUtils.isEmpty(errorMsgLovList) ? "" :
                String.format("未匹配到LOV-电池型号映射关系 %s", StringUtils.join(errorMsgLovList, ""));
        String errorMsg2 = CollectionUtils.isEmpty(errorMsgWeightList) ? "" :
                String.format("未匹配到MW折算系数-电池 %s", StringUtils.join(errorMsgWeightList, ""));
        Assert.isTrue(CollectionUtils.isEmpty(errorMsgLovList) && CollectionUtils.isEmpty(errorMsgWeightList), errorMsg1 + errorMsg2);
        return resultList;
    }

    @Override
    public void syncWaferPurchase(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryWaferPurchaseList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 硅片采购 syncWaferPurchase empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 硅片采购 syncWaferPurchase 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncWaferPurchase(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 硅片采购 syncWaferPurchase 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 硅片采购 syncWaferPurchase 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 硅片采购 syncWaferPurchase 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryWaferPurchaseList(OverallBudgetBaseDTO baseDTO) {
        Map<String, List<CellWaferWeightMaintenanceDTO>> cellWaferWeightMap = this.getWaferWeightMap();

        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        //硅片产能
        List<CalculateScheduling> waferCapacityList = calculateSchedulingService.queryDemandByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.WAFER);
        List<Long> oldIdList = waferCapacityList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateScheduling::getOldId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oldIdList)) {
            waferCapacityList.removeIf(item -> oldIdList.contains(item.getId()));
        }
        // 过滤掉行全部为0的数据
        waferCapacityList = waferCapacityList.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        List<String> errorMsgList = Lists.newLinkedList();
        waferCapacityList = waferCapacityList.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(waferCapacityList)) {
            waferCapacityList.forEach(ele -> {
                String wafeType = StringUtils.substringBeforeLast(ele.getProductSeries(), "-");
                String wafeModel = StringUtils.substringAfterLast(ele.getProductSeries(), "-");
                wafeModel = wafeModel.endsWith("型") ? wafeModel : StringUtils.join(wafeModel, "型");
                String finalWafeModel = wafeModel;

                List<CellWaferWeightMaintenanceDTO> maintenanceDTOS = cellWaferWeightMap.get(
                        StringUtils.join(Arrays.asList(ele.getCountryFlag(), ele.getProductSeries().replace("型", ""), ele.getYear()), "_"));
                if (CollectionUtils.isEmpty(maintenanceDTOS)) {
                    errorMsgList.add(String.format("【国内/海外：%s 电池型号：%s 年份：%s】",
                            ele.getCountryFlag(), ele.getProductSeries().replace("型", ""), ele.getYear()));
                }

                IntStream.rangeClosed(1, 12).forEach(month -> {
                    String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                    BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                    quantity = Objects.isNull(quantity) ? BigDecimal.ZERO : quantity;
                    BigDecimal cellWaferQuantity = CollectionUtils.isEmpty(maintenanceDTOS) ? BigDecimal.ZERO :
                            DataHandleUtils.calSumZero(maintenanceDTOS.stream().map(itemSummary -> (BigDecimal) ReflectUtil.getFieldValue(itemSummary, fieldName))
                                    .collect(Collectors.toList())).setScale(8, RoundingMode.HALF_UP);

                    OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                    outputDTO.setYears(String.valueOf(ele.getYear()));
                    outputDTO.setPeriod(String.valueOf(month));
                    outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                    outputDTO.setInOut("内部");
                    outputDTO.setWafeType(wafeType);
                    outputDTO.setWafeModel(finalWafeModel);
                    outputDTO.setProductFamily(StringUtils.join(Arrays.asList("单晶硅片", finalWafeModel, wafeType), "_"));
                    outputDTO.setCrystalType("单晶硅片");
                    //指标名称（万片）	规则
                    outputDTO.setScheduleYield(quantity.multiply(cellWaferQuantity).multiply(BigDecimal.valueOf(10000).setScale(8, RoundingMode.HALF_UP)));
                    outputDTO.setCreateDate(baseDTO.getPushDate());
                    outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                    outputDTO.setScenario(baseDTO.getBudgetScene());
                    resultList.add(outputDTO);
                });
            });
        }

        Assert.isTrue(CollectionUtils.isEmpty(errorMsgList), String.format("未匹配到MW折算系数-硅片 %s", StringUtils.join(errorMsgList, "")));

        //采购
        List<CalculatePurchaseDemand> waferPurchaseDemand = calculatePurchaseDemandService.queryPurchaseDemandByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.WAFER);
        List<Long> purchaseOldIdList = waferPurchaseDemand.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculatePurchaseDemand::getOldId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(purchaseOldIdList)) {
            waferPurchaseDemand.removeIf(item -> purchaseOldIdList.contains(item.getId()));
        }
        waferPurchaseDemand = waferPurchaseDemand.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(waferPurchaseDemand)) {
            waferPurchaseDemand.forEach(ele -> {
                IntStream.rangeClosed(1, 12).forEach(month -> {
                    String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                    BigDecimal quantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                    String wafeType = StringUtils.substringBeforeLast(ele.getProductSeries(), "-");
                    String wafeModel = StringUtils.substringAfterLast(ele.getProductSeries(), "-");
                    wafeModel = wafeModel.endsWith("型") ? wafeModel : StringUtils.join(wafeModel, "型");
                    OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                    outputDTO.setYears(String.valueOf(ele.getYear()));
                    outputDTO.setPeriod(String.valueOf(month));
                    outputDTO.setDomesticForeign(Objects.equals("INLAND", ele.getCountryFlag()) ? "国内" : "国外");
                    outputDTO.setInOut("外部");
                    outputDTO.setWafeType(wafeType);
                    outputDTO.setWafeModel(wafeModel);
                    outputDTO.setProductFamily(StringUtils.join(Arrays.asList("单晶硅片", wafeModel, wafeType), "_"));
                    outputDTO.setCrystalType("单晶硅片");
                    //指标名称（万片）	规则
                    outputDTO.setScheduleYield(Objects.isNull(quantity) ? BigDecimal.ZERO : quantity);
                    outputDTO.setCreateDate(baseDTO.getPushDate());
                    outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                    outputDTO.setScenario(baseDTO.getBudgetScene());
                    resultList.add(outputDTO);
                });
            });
        }
        return resultList;
    }

    @Override
    public void syncCellInventoryDays(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryCellInventoryDaysList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 主材库存天数-电池片 syncCellInventoryDays empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 主材库存天数-电池片 syncCellInventoryDays 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncCellInventoryDays(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 主材库存天数-电池片 syncCellInventoryDays 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 主材库存天数-电池片 syncCellInventoryDays 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 主材库存天数-电池片 syncCellInventoryDays 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    public List<OverallBudgetUnifyDTO> queryCellInventoryDaysList(OverallBudgetBaseDTO baseDTO) {
        //自产
        List<CalculateScheduling> moduleCapacityList = calculateSchedulingService.queryDemandByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.MODULE);
        List<Long> oldIdList = moduleCapacityList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateScheduling::getOldId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(oldIdList)) {
            moduleCapacityList.removeIf(item -> oldIdList.contains(item.getId()));
        }
        //OEM
        List<CalculatePurchaseDemand> moduleOemList = calculatePurchaseDemandService.queryOemByVersionAndType(baseDTO.getDataVersion(), ProductTypeEnum.MODULE);
        List<Long> oemOldIdList = moduleOemList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculatePurchaseDemand::getOldId).collect(Collectors.toList());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(oemOldIdList)) {
            moduleOemList.removeIf(item -> oemOldIdList.contains(item.getId()));
        }
        if (CollectionUtils.isEmpty(moduleCapacityList) && CollectionUtils.isEmpty(moduleOemList)) {
            return Collections.emptyList();
        }
        List<AdjustImportDetailDTO> moduleDetailDTOS = this.buildAdjustImportDetailDTOList(moduleCapacityList, moduleOemList);
        moduleDetailDTOS = moduleDetailDTOS.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        // 过滤掉行全部为0的数据
        moduleDetailDTOS = moduleDetailDTOS.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(moduleDetailDTOS)) {
            return Collections.emptyList();
        }
        //  调用AOP导入中第三步计算 - 每月组件总产出
        Map<String, AdjustImportDetailDTO> moduleOutputMap = adjustImportDetailService.buildOutputWithCellDistribution(moduleDetailDTOS);
        //  每月电池EOH
        Map<String, ImportActualCapacityBoh> capacityBohMap = this.queryCapacityBohMap(baseDTO);
        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        Map<String, LovLineDTO> effectPowerCellMap = this.buildEffectPowerCellMap();

        moduleOutputMap.forEach((k, v) -> {
            String shardMethod = Objects.equals(Priority.CellShard.TWC.getCode() , v.getCellShard()) ? "二分片" : "三分片";
            LovLineDTO cellSeriesLovLineDTO = effectPowerCellMap.get(StringUtils.join(v.getProductSeries(), shardMethod));
            Assert.notNull(cellSeriesLovLineDTO, String.format("未匹配到LOV-电池型号映射关系【电池型号：%s】【分片方式：%s】",
                    v.getProductSeries(), shardMethod));

            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(v.getProductSeries());
            ImportActualCapacityBoh importActualCapacityBoh = capacityBohMap.get(k);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(baseDTO.getYears()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", v.getCountryFlag()) ? "国内" : "国外");
                outputDTO.setCrystalType("单晶电池片");
                outputDTO.setBatteryType(typeAndModelPair.getKey());
                outputDTO.setBatteryModel(typeAndModelPair.getValue().endsWith("2.0") ? (StringUtils.join(typeAndModelPair.getValue().replace("2.0", "").trim(), "型"))
                        : StringUtils.join(typeAndModelPair.getValue(), "型"));

                String shardMethodLov = convertShardMethodLov(typeAndModelPair.getKey(), cellSeriesLovLineDTO);
                outputDTO.setShardMethod(shardMethodLov);
                outputDTO.setMainGridNum(this.convertMainGridNumLov(cellSeriesLovLineDTO));
                outputDTO.setOem(this.convertOEM(outputDTO.getBatteryType(), outputDTO.getBatteryModel()));
                //  本月EOH÷（次月组件总产出÷次月份自然天）
                String curFieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal curEOH = Objects.isNull(importActualCapacityBoh)
                        ? BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(importActualCapacityBoh, curFieldName);
                curEOH = Objects.isNull(curEOH) ? BigDecimal.ZERO : curEOH;
                Integer nextMonth = (month == 12) ? month : (month + 1);
                String nextFieldName = String.format(AdjustConstant.MQUANTITY, nextMonth);
                BigDecimal nextOutput = (BigDecimal) ReflectUtil.getFieldValue(v, nextFieldName);
                //  次月份自然天
                int days = YearMonth.of(Integer.valueOf(baseDTO.getYears()), nextMonth).lengthOfMonth();
                if (curEOH.compareTo(BigDecimal.ZERO) == 0 || nextOutput.compareTo(BigDecimal.ZERO) == 0) {
                    outputDTO.setScheduleYield(BigDecimal.ZERO);
                } else {
                    outputDTO.setScheduleYield(curEOH.divide(nextOutput.divide(new BigDecimal(days), 8, RoundingMode.HALF_UP), 8, RoundingMode.HALF_UP));
                }
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    private List<AdjustImportDetailDTO> buildAdjustImportDetailDTOList(List<CalculateScheduling> moduleCapacityList,
                                                                       List<CalculatePurchaseDemand> moduleOemList) {
        Map<String, CalculateScheduling> schedulingMap = moduleCapacityList.stream().collect(Collectors.toMap(ele ->
                        StringUtils.join(ele.getCountryFlag(), "_", ele.getProductType(), "_", ele.getProductSeries()),
                Function.identity(), (v1, v2) -> v1));

        Map<String, CalculatePurchaseDemand> demandMap = moduleOemList.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), "_", ele.getProductType(), "_", ele.getProductSeries()),
                Function.identity(), (v1, v2) -> v1));

        Set<String> allGroupKey = Sets.newHashSet();
        allGroupKey.addAll(schedulingMap.keySet());
        allGroupKey.addAll(demandMap.keySet());

        allGroupKey.forEach(ele -> {
            CalculateScheduling schedulingBean = schedulingMap.get(ele);
            if (Objects.isNull(schedulingBean)) {
                schedulingBean = new CalculateScheduling();
                schedulingBean.initZero();
            }
            CalculatePurchaseDemand demandBean = demandMap.get(ele);
            if (Objects.isNull(demandBean)) {
                demandBean = new CalculatePurchaseDemand();
                demandBean.initZero();
            }
            String[] split = ele.split("_");
            AdjustImportDetailDTO
                    .builder()
                    .countryFlag(split[0])
                    .productType(split[1])
                    .productSeries(split[2])
                    .m1Quantity(schedulingBean.getM1Quantity().add(demandBean.getM1Quantity()))
                    .m2Quantity(schedulingBean.getM2Quantity().add(demandBean.getM2Quantity()))
                    .m3Quantity(schedulingBean.getM3Quantity().add(demandBean.getM3Quantity()))
                    .m4Quantity(schedulingBean.getM4Quantity().add(demandBean.getM4Quantity()))
                    .m5Quantity(schedulingBean.getM5Quantity().add(demandBean.getM5Quantity()))
                    .m6Quantity(schedulingBean.getM6Quantity().add(demandBean.getM6Quantity()))
                    .m7Quantity(schedulingBean.getM7Quantity().add(demandBean.getM7Quantity()))
                    .m8Quantity(schedulingBean.getM8Quantity().add(demandBean.getM8Quantity()))
                    .m9Quantity(schedulingBean.getM9Quantity().add(demandBean.getM9Quantity()))
                    .m10Quantity(schedulingBean.getM10Quantity().add(demandBean.getM10Quantity()))
                    .m11Quantity(schedulingBean.getM11Quantity().add(demandBean.getM11Quantity()))
                    .m12Quantity(schedulingBean.getM12Quantity().add(demandBean.getM12Quantity()))
                    .build();
        });

        return moduleCapacityList.stream().map(ele -> {
            return AdjustImportDetailDTO
                    .builder()
                    .countryFlag(ele.getCountryFlag())
                    .productType(ele.getProductType())
                    .productFrom(ele.getProductFrom())
                    .productSeries(ele.getProductSeries())
                    .m1Quantity(ele.getM1Quantity())
                    .m2Quantity(ele.getM2Quantity())
                    .m3Quantity(ele.getM3Quantity())
                    .m4Quantity(ele.getM4Quantity())
                    .m5Quantity(ele.getM5Quantity())
                    .m6Quantity(ele.getM6Quantity())
                    .m7Quantity(ele.getM7Quantity())
                    .m8Quantity(ele.getM8Quantity())
                    .m9Quantity(ele.getM9Quantity())
                    .m10Quantity(ele.getM10Quantity())
                    .m11Quantity(ele.getM11Quantity())
                    .m12Quantity(ele.getM12Quantity())
                    .build();
        }).collect(Collectors.toList());
    }

    private Map<String, ImportActualCapacityBoh> queryCapacityBohMap(OverallBudgetBaseDTO baseDTO) {
        BooleanBuilder booleanBuilder = new BooleanBuilder();
        QImportActualCapacityBoh capacityBoh = QImportActualCapacityBoh.importActualCapacityBoh;
        booleanBuilder.and(capacityBoh.calculateVersion.eq(baseDTO.getDataVersion()));
        List<ImportActualCapacityBoh> importActualCapacityBohs = IterableUtils.iterableToList(importActualCapacityBohRepository.findAll(booleanBuilder));
        return importActualCapacityBohs.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductSeries(), ele.getCellShard()), Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public void syncWaferInventoryDays(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryWaferInventoryDaysList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 主材库存天数-硅片 syncWaferInventoryDays empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 主材库存天数-硅片 syncWaferInventoryDays 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncWaferInventoryDays(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 主材库存天数-硅片 syncWaferInventoryDays 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 主材库存天数-硅片 syncWaferInventoryDays 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 主材库存天数-硅片 syncWaferInventoryDays 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    @Override
    public void syncModuleDischarge(OverallBudgetBaseDTO baseDTO) throws Exception {
        List<OverallBudgetUnifyDTO> overallBudgetUnifyDTOS = this.queryModuleDischargeList(baseDTO);
        if (CollectionUtils.isEmpty(overallBudgetUnifyDTOS)) {
            log.info("全面预算系统对接 组件车间A-使用量 syncModuleDischarge empty skip");
            return;
        }
        List<List<OverallBudgetUnifyDTO>> partition = ListUtil.partition(overallBudgetUnifyDTOS, 2000);
        for (int i = 0; i < partition.size(); i++) {
            log.info("全面预算系统对接 组件车间A-使用量 syncModuleDischarge 最大批次：{}", partition.size());
            try {
                JSONObject resultJson = overallBudgetFeignClient.syncModuleDischarge(partition.get(i));
                if (!Objects.equals("0", resultJson.getString("code"))) {
                    throw new BizException(resultJson.getString("msg"));
                }
                log.info("全面预算系统对接 组件车间A-使用量 syncModuleDischarge 当前批次：{} response：{}", i + 1, resultJson);
            } catch (Exception ex) {
                log.info("全面预算系统对接 组件车间A-使用量 syncModuleDischarge 当前批次：{} 传参打印：{}", i + 1, JSONUtil.toJsonStr(partition.get(i)));
                log.error("全面预算系统对接 组件车间A-使用量 syncModuleDischarge 当前批次：{} exception:", i + 1, ex);
                throw new Exception("外部接口调用失败：" + ex.getMessage());
            }
        }
    }

    @Override
    public void syncCellPurchaseData(OverallBudgetBaseDTO baseDTO) throws Exception {
        try {
            Map<String, List<CalculateAllocationResultDTO>> data = calculateAllocationService.queryAllocationForAllBudget(baseDTO.getDataVersion());

            Map<String, String> sceneMap = LovService.findLovName("AOP_BUDGET_SCENE");
            String sceneName = sceneMap.get(baseDTO.getBudgetScene());
            List<OverallBudgetCellPurchaseDTO> list = transfer2OverallBudgetCellPurchaseDTOList(data,baseDTO.getBudgetVersion(),sceneName);
            log.info("全面预算系统对接 组件车间A-使用量 syncCellPurchaseData 传参打印：{}", list);

            JSONObject resultJson = overallBudgetFeignClient.syncCellPurchaseData(list);
            if (!Objects.equals("0", resultJson.getString("code"))) {
                throw new BizException(resultJson.getString("msg"));
            }
            log.info("全面预算系统对接 syncCellPurchaseData response：{}" , resultJson);
        } catch (Exception ex) {
            log.error("全面预算系统对接 组件车间A-使用量 syncCellPurchaseData exception:", ex);
            throw new Exception("外部接口调用失败：" + ex.getMessage());
        }
    }

    private List<OverallBudgetCellPurchaseDTO> transfer2OverallBudgetCellPurchaseDTOList(Map<String, List<CalculateAllocationResultDTO>> data
            , String versionName, String sceneName) {
        List<OverallBudgetCellPurchaseDTO> resultList = new ArrayList<>();
        List<CalculateAllocationResultDTO> list1 = data.get("INLAND");
        List<CalculateAllocationResultDTO> list2 = data.get("OVERSEA");
        this.mergeData(resultList, list1, versionName, sceneName);
        this.mergeData(resultList, list2, versionName, sceneName);
        return resultList;
    }

    private void mergeData(List<OverallBudgetCellPurchaseDTO> resultList, List<CalculateAllocationResultDTO> dataList
            , String versionName, String sceneName) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date now = new Date();

        if (CollectionUtils.isNotEmpty(dataList)) {
            for (CalculateAllocationResultDTO data : dataList) {
                resultList.add(this.ceateMergeDateByPeriod(data, "Q1", sdf.format(now), versionName, sceneName));
                resultList.add(this.ceateMergeDateByPeriod(data, "Q2", sdf.format(now), versionName, sceneName));
                resultList.add(this.ceateMergeDateByPeriod(data, "Q3", sdf.format(now), versionName, sceneName));
                resultList.add(this.ceateMergeDateByPeriod(data, "Q4", sdf.format(now), versionName, sceneName));
            }
        }
    }

    private OverallBudgetCellPurchaseDTO ceateMergeDateByPeriod(CalculateAllocationResultDTO data,
                                                                String period, String now, String versionName, String sceneName) {
        OverallBudgetCellPurchaseDTO result = new OverallBudgetCellPurchaseDTO();
        result.setScenario(sceneName);
        result.setVersionCode(versionName);
        result.setYears(data.getYear().toString());
        result.setPeriod(period);
        result.setDomesticForeign(data.getCountryFlag().equals("INLAND") ? "国内" : "海外");
        result.setRegion(data.getArea());
        result.setProductSeries(data.getProductSeries());
        if (period.equals("Q1")) {
            result.setScheduleYield(data.getQ1Eoh().toString());
        } else if (period.equals("Q2")) {
            result.setScheduleYield(data.getQ2Eoh().toString());
        }
        if (period.equals("Q3")) {
            result.setScheduleYield(data.getQ3Eoh().toString());
        }
        if (period.equals("Q4")) {
            result.setScheduleYield(data.getQ4Eoh().toString());
        }
        result.setCreateDate(now);
        return result;
    }


    public List<OverallBudgetUnifyDTO> queryWaferInventoryDaysList(OverallBudgetBaseDTO baseDTO) {
        List<CalculateScheduling> cellCapacityList = calculateSchedulingService.queryDemandByVersionType(baseDTO.getDataVersion(), ProductTypeEnum.CELL);
        List<Long> oldIdList = cellCapacityList.stream().filter(item -> ObjectUtils.isNotEmpty(item.getOldId())).map(CalculateScheduling::getOldId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(oldIdList)) {
            cellCapacityList.removeIf(item -> oldIdList.contains(item.getId()));
        }
        cellCapacityList = cellCapacityList.stream().filter(ele -> EFFECT_COUNTRY_LIST.contains(ele.getCountryFlag())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(cellCapacityList)) {
            return Collections.emptyList();
        }
        // p2_3223 全面预算系统对接-主材库存天数-硅片逻辑调整
        // 电池型号与硅片型号的映射表
        Map<String, String> productInfoMap = productInfoService.queryAllProductInfo().stream().collect(Collectors.toMap(ProductInfo::getCellModel, ProductInfo::getWaferModel, (v1, v2) -> v1));
        Map<String, List<CalculateScheduling>> cellCapacityMap = Maps.newHashMap();
        cellCapacityList.forEach(ele -> {
            String waferModel = productInfoMap.get(ele.getProductSeries());
            if (StringUtils.isEmpty(waferModel)) {
                return;
            }
            String groupKey = StringUtils.join(ele.getCountryFlag(), ";", waferModel);
            List<CalculateScheduling> calculateSchedulingList = cellCapacityMap.getOrDefault(groupKey, Lists.newLinkedList());
            calculateSchedulingList.add(ele);
            cellCapacityMap.put(groupKey, calculateSchedulingList);
        });

        //实际EOH
        List<CalculateActualEoh> cellActualEoh = calculateActualEohService.queryByVersionAndType(baseDTO.getDataVersion(), ProductTypeEnum.WAFER);
        Map<String, CalculateActualEoh> cellActualEohMap = cellActualEoh.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getCountryFlag(), ele.getProductSeries()), Function.identity(), (v1, v2) -> v1));

        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        cellCapacityMap.forEach((k, v) -> {
            String countryFlag = StringUtils.substringBeforeLast(k, ";");
            String waferModel = StringUtils.substringAfterLast(k, ";");

            Pair<String, String> typeAndModelPair = this.convertTypeAndModel(waferModel);
            CalculateActualEoh actualEoh = cellActualEohMap.get(StringUtils.join(countryFlag, waferModel));
            IntStream.rangeClosed(1, 12).forEach(month -> {
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(baseDTO.getYears()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(Objects.equals("INLAND", countryFlag) ? "国内" : "国外");
                outputDTO.setWafeType(typeAndModelPair.getKey());
                outputDTO.setWafeModel(typeAndModelPair.getValue().endsWith("型") ? typeAndModelPair.getValue() : StringUtils.join(typeAndModelPair.getValue(), "型"));
                outputDTO.setCrystalType("单晶硅片");
                //  本月EOH÷（次月组件总产出÷次月份自然天）
                String curFieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal curEOH = Objects.isNull(actualEoh)
                        ? BigDecimal.ZERO : (BigDecimal) ReflectUtil.getFieldValue(actualEoh, curFieldName);
                curEOH = Objects.isNull(curEOH) ? BigDecimal.ZERO : curEOH;
                Integer nextMonth = (month == 12) ? month : (month + 1);
                String nextFieldName = String.format(AdjustConstant.MQUANTITY, nextMonth);
                BigDecimal nextOutput = v.stream().map(ele -> (BigDecimal) ReflectUtil.getFieldValue(ele, nextFieldName)).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                //  次月份自然天
                int days = YearMonth.of(Integer.valueOf(baseDTO.getYears()), nextMonth).lengthOfMonth();
                if (curEOH.compareTo(BigDecimal.ZERO) == 0 || nextOutput.compareTo(BigDecimal.ZERO) == 0) {
                    outputDTO.setScheduleYield(BigDecimal.ZERO);
                } else {
                    outputDTO.setScheduleYield(curEOH.divide(nextOutput.divide(new BigDecimal(days), 8, RoundingMode.HALF_UP), 8, RoundingMode.HALF_UP));
                }
                outputDTO.setCreateDate(baseDTO.getPushDate());
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                resultList.add(outputDTO);
            });
        });
        return resultList;
    }

    public List<OverallBudgetUnifyDTO> queryModuleDischargeList(OverallBudgetBaseDTO baseDTO) {
        CalculateVersionDTO versionDTO = new CalculateVersionDTO();
        versionDTO.setYear(baseDTO.getYears());
        versionDTO.setDataVersion(baseDTO.getDataVersion());
        List<ComponentWorkshopUsageDTO> workshopUsageDTOS = componentWorkshopUsageService.workShopUsage(versionDTO);
        // 过滤掉行全部为0的数据
        workshopUsageDTOS = workshopUsageDTOS.stream().filter(ele -> {
            AtomicReference<Boolean> saveFlag = new AtomicReference<>(false);
            IntStream.rangeClosed(1, 12).forEach(month -> {
                String fieldName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal fieldValue = (BigDecimal) ReflectUtil.getFieldValue(ele, fieldName);
                if (Objects.nonNull(fieldValue) && fieldValue.compareTo(BigDecimal.ZERO) > 0) {
                    saveFlag.set(true);
                }
            });
            return saveFlag.get();
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workshopUsageDTOS)) {
            return Collections.emptyList();
        }
        Map<String, LovLineDTO> allWorkShopMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.WORK_SHOP);
        Map<String, LovLineDTO> allBasePlaceMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.BASE_PLACE);

        Set<String> allWorkShopErrorSets = Sets.newHashSet();
        Set<String> allBasePlaceErrorSets = Sets.newHashSet();

        List<OverallBudgetUnifyDTO> resultList = Lists.newLinkedList();
        workshopUsageDTOS.forEach(ele -> {
            String countryFlag = null, basePlace = null;
            try {
                LovLineDTO lovLineDTO = allWorkShopMap.get(ele.getComponentWorkshop());
                basePlace = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute1())).getLovValue();
            } catch (Exception ex) {
                allWorkShopErrorSets.add(ele.getComponentWorkshop());
            }
            if (!StringUtils.isEmpty(basePlace)) {
                try {
                    LovLineDTO lovLineDTO = allBasePlaceMap.get(basePlace);
                    countryFlag = LovUtils.get(Long.valueOf(lovLineDTO.getAttribute2())).getLovName();
                } catch (Exception ex) {
                    allBasePlaceErrorSets.add(basePlace);
                }
            }
            if (StringUtils.isEmpty(countryFlag)) {
                return;
            }

            String finalCountryFlag = countryFlag;
            IntStream.rangeClosed(1, 12).forEach(month -> {
                OverallBudgetUnifyDTO outputDTO = OverallBudgetUnifyDTO.initUnifyDTO();
                outputDTO.setYears(String.valueOf(baseDTO.getYears()));
                outputDTO.setPeriod(String.valueOf(month));
                outputDTO.setDomesticForeign(finalCountryFlag);
                outputDTO.setVersionCode(baseDTO.getBudgetVersion());
                outputDTO.setScenario(baseDTO.getBudgetScene());
                outputDTO.setWorkShopCode("");
                outputDTO.setWorkShop(ele.getComponentWorkshop());
                outputDTO.setProductFamily(ele.getProductFamily() + "-A-");
                outputDTO.setProductSeries(ele.getProductSeries());
                String fileName = String.format(AdjustConstant.MQUANTITY, month);
                BigDecimal mQuantity = (BigDecimal) ReflectUtil.getFieldValue(ele, fileName);
                outputDTO.setScheduleYield(mQuantity);
                outputDTO.setCreateDate(baseDTO.getPushDate());
                resultList.add(outputDTO);
            });
        });
        if (CollectionUtils.isNotEmpty(allWorkShopErrorSets)) {
            throw new BizException(String.format("%s 车间LOV或绑定的基地未维护", StringUtils.join(allWorkShopErrorSets, "、")));
        }
        if (CollectionUtils.isNotEmpty(allBasePlaceErrorSets)) {
            throw new BizException(String.format("%s 基地LOV或绑定的国内海外未维护", StringUtils.join(allBasePlaceErrorSets, "、")));
        }
        return resultList;
    }

    private String getAndVaildShardMethod(Map<String, LovLineDTO> aopCellShardMap, String cellShard, String errorMsg) {
        LovLineDTO cellShardLovLineDTO = aopCellShardMap.get(cellShard);
        String shardMethod = Objects.nonNull(cellShardLovLineDTO) ? cellShardLovLineDTO.getLovName() : null;
//        Assert.notNull(shardMethod, String.format("全面预算系统对接-电池排产量接口，LOV-AOP电池分片方式【基地：%s】【车间：%s】【电池型号：%s】【分片方式：%s】",
//                ele.getBasePlace(), ele.getWorkshop(), ele.getProductSeries(), ele.getCellShard()));
        return shardMethod;
    }

    private BigDecimal getMonthQuantity(Object oj, int month) {
        String fieldName = String.format(AdjustConstant.MQUANTITY, month);
        return  (BigDecimal) ReflectUtil.getFieldValue(oj, fieldName);
    }

    private Pair<String, String> convertTypeAndModel(String productSeries) {
        String batteryType = StringUtils.substringBeforeLast(productSeries, "-");
        String batteryModel = StringUtils.substringAfterLast(productSeries, "-");
        batteryModel = batteryModel.endsWith("型") ? batteryModel.replace("型", "") : batteryModel;
        return Pair.of(batteryType, batteryModel);
    }

    private String convertOEM(String batteryType, String batteryModel) {
        return (Objects.equals("182", batteryType) && Objects.equals("P型", batteryModel)) ? "代工" : "";
    }

    private Map<String, LovLineDTO> buildEffectPowerCellMap() {
        Map<String, LovLineDTO> apsPowerCellSeriesTypeMap = LovUtils.getAllByHeaderCode(LovHeaderCodeConstant.APS_POWER_CELL_SERIES_TYPE);
        return apsPowerCellSeriesTypeMap.values().stream().map(ele -> {
            if (StringUtils.isNotEmpty(ele.getAttribute1()) && StringUtils.isNumeric(ele.getAttribute1())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(ele.getAttribute1()));
                ele.setAttribute1(Objects.nonNull(lovLineDTO) ? lovLineDTO.getLovValue() : null);
            }
            if (StringUtils.isNotEmpty(ele.getAttribute4()) && StringUtils.isNumeric(ele.getAttribute4())) {
                LovLineDTO lovLineDTO = LovUtils.get(Long.parseLong(ele.getAttribute4()));
                ele.setAttribute4(Objects.nonNull(lovLineDTO) ? lovLineDTO.getLovName() : null);
            }
            return ele;
        }).collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getAttribute1(), ele.getAttribute4()), Function.identity(), (v1, v2) -> v1));
    }

    private Map<String, BigDecimal> buildMwCoefficientMap() {
        ResponseEntity<Results<List<MwCoefficientDTO>>> resultsResponseEntity = powerFeignClient.allList();
        if (resultsResponseEntity == null || resultsResponseEntity.getBody() == null || !resultsResponseEntity.getBody().isSuccess()) {
            log.error("全面预算系统对接-电池排产量-MW折算系数接口获取失败：{}", JSON.toJSONString(resultsResponseEntity));
            throw new BizException("MW折算系数接口获取失败");
        }
        List<MwCoefficientDTO> mwCoefficientDTOS = resultsResponseEntity.getBody().getData();
        if (CollectionUtils.isEmpty(mwCoefficientDTOS)) {
            return Maps.newHashMap();
        }
        return mwCoefficientDTOS.stream().collect(Collectors.toMap(ele ->
                StringUtils.join(ele.getIsOversea(), ele.getCellType()), MwCoefficientDTO::getCoefficient, (v1, v2) -> v1));
    }
}
