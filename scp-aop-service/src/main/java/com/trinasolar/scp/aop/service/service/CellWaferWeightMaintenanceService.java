package com.trinasolar.scp.aop.service.service;

import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceCalculateDTO;
import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceCheckDTO;
import com.trinasolar.scp.aop.domain.dto.CellWaferWeightMaintenanceDTO;
import com.trinasolar.scp.aop.domain.query.CellWaferWeightMaintenanceQuery;
import com.trinasolar.scp.aop.domain.save.CellWaferWeightMaintenanceSaveDTO;
import com.trinasolar.scp.common.api.enums.ProductTypeEnum;
import com.trinasolar.scp.common.api.util.ExcelPara;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 电池硅片权重维护 服务接口
 *
 * <AUTHOR>
 * @email
 * @date 2024-12-04
 */
public interface CellWaferWeightMaintenanceService {
    /**
     * 分页获取电池硅片权重
     *
     * @param query 查询对象
     * @return 电池硅片权重分页对象
     */
    Page<CellWaferWeightMaintenanceDTO> queryByPage(CellWaferWeightMaintenanceQuery query);

    /**
     * 根据主键获取电池硅片权重详情
     *
     * @param id 主键
     * @return 电池硅片权重详情
     */
    CellWaferWeightMaintenanceDTO queryById(Long id);

    /**
     * 保存或更新电池硅片权重
     *
     * @param saveDTO 电池硅片权重保存对象
     * @return 电池硅片权重对象
     */
    CellWaferWeightMaintenanceDTO save(CellWaferWeightMaintenanceSaveDTO saveDTO);

    /**
     * 根据主键逻辑删除电池硅片权重
     *
     * @param ids 主键集合
     */
    void logicDeleteByIds(List<Long> ids);

    /**
     * 导出
     *
     * @param query
     * @param response
     */
    void export(CellWaferWeightMaintenanceQuery query, HttpServletResponse response);

    /**
     * 导入
     *
     * @param multipartFile
     * @param excelPara
     * @return 批次号
     */
    String importData(MultipartFile multipartFile, ExcelPara excelPara, ProductTypeEnum productType);

    /**
     * 校验 导入数据
     * @param multipartFile
     * @param excelPara
     * @return 校验结果
     */
    CellWaferWeightMaintenanceCheckDTO checkImport(MultipartFile multipartFile, ExcelPara excelPara, ProductTypeEnum productType);

    /**
     * 电池折算系数计算
     * @param calculateDTO
     */
    Map<String,String> calculate(CellWaferWeightMaintenanceCalculateDTO calculateDTO);
}

