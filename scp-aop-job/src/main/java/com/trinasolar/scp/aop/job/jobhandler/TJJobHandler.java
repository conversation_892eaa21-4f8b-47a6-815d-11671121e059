package com.trinasolar.scp.aop.job.jobhandler;

import com.alibaba.fastjson.JSON;
import com.trinasolar.scp.aop.domain.query.BigdataJobParamsDTO;
import com.trinasolar.scp.aop.service.service.ActualBohService;
import com.trinasolar.scp.aop.service.service.ActualOtherService;
import com.trinasolar.scp.aop.service.service.ActualOutputService;
import com.trinasolar.scp.aop.service.service.ActualSaleService;
import com.trinasolar.scp.common.api.util.BizException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoField;
import java.util.Objects;

/**
 * 同步天玑数据定时任务
 **/
@Slf4j
@Component
@EnableScheduling
public class TJJobHandler {

    @Autowired
    private ActualOutputService actualOutputService;

    @Autowired
    private ActualSaleService actualSaleService;

    @Autowired
    private ActualBohService actualBohService;

    @Autowired
    private ActualOtherService actualOtherService;

    private static final String DEFAULT_REGEX = "-";

    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncActualOutputDataJobHandler")
    public ReturnT<String> syncActualOutputData() {
        XxlJobHelper.log("开始同步天玑实际产出数据：" + LocalDateTime.now());
        try {
            String year = null;
            String month = null;
            actualOutputService.syncActualOutputData(year, month, false);
            XxlJobHelper.log("同步天玑实际产出数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑实际产出数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncActualBohDataJobHandler")
    public ReturnT<String> syncActualBohData() {
        XxlJobHelper.log("开始同步天玑实际期初库存数据：" + LocalDateTime.now());
        try {
            String year = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy"));
            String month = LocalDate.now().format(DateTimeFormatter.ofPattern("MM"));
            actualBohService.syncActualBohData(year, month, false);
            XxlJobHelper.log("同步天玑实际库存数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑实际库存数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncTJActualSales2SCPJobHandler")
    public ReturnT<String> syncTJActualSales2SCP() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑实际销售量数据,任务参数(格式为xxxx-xx):[" + jobParam + "]");
        XxlJobHelper.log("开始同步天玑实际销售量数据：" + LocalDateTime.now());
        try {
            String year = null;
            String month = null;
//            if(StringUtils.isNotBlank(jobParam) && jobParam.contains(DEFAULT_REGEX)){
//                String [] yearAndMonth = jobParam.split(DEFAULT_REGEX);
//                year = yearAndMonth[0];
//                month = yearAndMonth[1];
//            }
            actualSaleService.syncTJActualSales2SCP(year, month, false);
            XxlJobHelper.log("同步天玑实际销售量数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑实际销售量数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1853 AOP实际数接口-电池期初库存
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncActualBohDataNewJobHandler")
    public ReturnT<String> syncActualBohDataNew() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑实际期初库存数据：" + LocalDateTime.now());
        try {
            LocalDate inventoryDate = LocalDate.now().withDayOfMonth(1);
            if (!StringUtils.isEmpty(jobParam)) {
                if (!this.isValidDate(jobParam, "yyyyMM") && !this.isValidDate(jobParam, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(jobParam, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(jobParam, formatter);
            }
            actualBohService.syncActualBohDataNew(inventoryDate);
            XxlJobHelper.log("同步天玑实际库存数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑实际库存数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    public static boolean isValidDate(String date, String format) {
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            formatter.parse(date);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    /**
     * p2_1915 AOP实际数接口-电池实际外购
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncCellActualPurchaseJobHandler")
    public ReturnT<String> syncOutgoingJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑电池实际外购数据：" + LocalDateTime.now());
        try {
            Boolean mvIngoreFlag = false;
            LocalDate inventoryDate = LocalDate.now().minusMonths(1);
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualOtherService.syncCellActualPurchase(inventoryDate, mvIngoreFlag);
            XxlJobHelper.log("同步天玑电池实际外购数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑电池实际外购数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1997 AOP实际数接口-硅片期初库存
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncWaferBohJobHandler")
    public ReturnT<String> syncWaferBohJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑硅片期初库存数据：" + LocalDateTime.now());
        try {
            Boolean mvIngoreFlag = false;
            LocalDate inventoryDate = LocalDate.now();
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();
                mvIngoreFlag = Objects.isNull(bigdataJobParamsDTO.getMvIngoreFlag()) ? false : bigdataJobParamsDTO.getMvIngoreFlag();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualBohService.syncWaferBohJobHandler(inventoryDate, mvIngoreFlag);
            XxlJobHelper.log("同步天玑硅片期初库存数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑硅片期初库存数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1998 AOP实际数接口-硅片实际采购
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncWaferActualPurchaseJobHandler")
    public ReturnT<String> syncWaferActualPurchaseJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑硅片实际采购数据：" + LocalDateTime.now());
        try {
            Boolean mvIngoreFlag = false;
            LocalDate inventoryDate = LocalDate.now().minusMonths(1);
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();
                mvIngoreFlag = Objects.isNull(bigdataJobParamsDTO.getMvIngoreFlag()) ? false : bigdataJobParamsDTO.getMvIngoreFlag();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualOutputService.syncWaferActualPurchase(inventoryDate, mvIngoreFlag);
            XxlJobHelper.log("同步天玑硅片实际采购数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑硅片实际采购数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1919 AOP实际数接口-组件实际产出接口
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncModuleActualOutputJobHandler")
    public ReturnT<String> syncModuleActualOutputJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑组件实际产出数据：" + LocalDateTime.now());
        try {
            // 取上月组件产出的数据
            LocalDate inventoryDate = LocalDate.now().minusMonths(1);
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualOutputService.syncModuleActualOutput(inventoryDate);
            XxlJobHelper.log("同步天玑组件实际产出数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑组件实际产出数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1916 AOP实际数接口-电池实际外卖
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncCellActualOutgoingJobHandler")
    public ReturnT<String> syncCellActualOutgoingJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑电池实际外卖数据：" + LocalDateTime.now());
        try {
            Boolean mvIngoreFlag = false;
            // 取上月组件产出的数据
            LocalDate inventoryDate = LocalDate.now().minusMonths(1);
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();
                mvIngoreFlag = Objects.isNull(bigdataJobParamsDTO.getMvIngoreFlag()) ? false : bigdataJobParamsDTO.getMvIngoreFlag();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualOtherService.syncCellActualOutgoing(inventoryDate, mvIngoreFlag);
            XxlJobHelper.log("同步天玑电池实际外卖数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑电池实际外卖数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_1996 AOP实际数接口-组件期初库存
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncModuleBohJobHandler")
    public ReturnT<String> syncModuleBohJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑组件期初库存数据：" + LocalDateTime.now());
        try {
            // 取上月组件产出的数据
            LocalDate inventoryDate = LocalDate.now();
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualBohService.syncModuleBohJobHandler(inventoryDate);
            XxlJobHelper.log("同步天玑组件期初库存数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑组件期初库存数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }

    /**
     * p2_2368 AOP实际数接口-电池实际产出
     * @return
     */
    @Transactional(rollbackFor = RuntimeException.class)
    @XxlJob("syncCellActualOutputJobHandler")
    public ReturnT<String> syncCellActualOutputJobHandler() {
        String jobParam = XxlJobHelper.getJobParam();
        XxlJobHelper.log("开始同步天玑电池实际产出数据：" + LocalDateTime.now());
        try {
            Boolean mvIngoreFlag = false;
            LocalDate inventoryDate = LocalDate.now().minusMonths(1);
            if (!StringUtils.isEmpty(jobParam)) {
                BigdataJobParamsDTO bigdataJobParamsDTO = JSON.parseObject(jobParam, BigdataJobParamsDTO.class);
                Assert.isTrue(Objects.nonNull(bigdataJobParamsDTO), "传参不能为空");
                Assert.isTrue(StringUtils.isNotEmpty(bigdataJobParamsDTO.getMonth()), "月份不能为空");
                String month = bigdataJobParamsDTO.getMonth();
                mvIngoreFlag = Objects.isNull(bigdataJobParamsDTO.getMvIngoreFlag()) ? false : bigdataJobParamsDTO.getMvIngoreFlag();

                if (!this.isValidDate(month, "yyyyMM") && !this.isValidDate(month, "yyyy-MM")) {
                    throw new BizException("年月日期格式有误，请输入yyyyMM或yyyy-MM格式");
                }
                DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                        .appendPattern(this.isValidDate(month, "yyyyMM") ? "yyyyMM" : "yyyy-MM")
                        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
                        .toFormatter();
                inventoryDate = LocalDate.parse(month, formatter);
            }
            actualOutputService.syncCellActualOutput(inventoryDate, mvIngoreFlag);
            XxlJobHelper.log("同步天玑电池实际产出数据结束：" + LocalDateTime.now());
        } catch (Exception e) {
            e.printStackTrace();
            XxlJobHelper.log("同步天玑电池实际产出数据发生异常：" + LocalDateTime.now(), e);
            throw new RuntimeException(e);
        }
        return ReturnT.SUCCESS;
    }
}
